package cn.psycloud.psyplatform.controller.survey;

import cn.psycloud.psyplatform.dto.activityroom.ActivitySurveyRecordDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto;
import cn.psycloud.psyplatform.dto.trainingcamp.TrainingCampSurveyRecordDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivitySurveyEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampSurveyEntity;
import cn.psycloud.psyplatform.service.survey.SurveyRecordService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/survey/surveyrecord")
public class SurveyRecordController {
    @Autowired
    private SurveyRecordService surveyRecordService;

    @GetMapping("/list")
    public String recordList(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("011004".equals(a.getFunctionCode()) || "011004".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canDelete = false, canExport = false, canViewReport = false;
        for (SysFunctionDto privilege: privilegeList){
            //删除权限
            if ("01100401".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //导出权限
            if ("01100402".equals(privilege.getFunctionCode())) {
                canExport = true;
            }
            //查看报告权限
            if ("01100403".equals(privilege.getFunctionCode())) {
                canViewReport = true;
            }
        }
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canExport",canExport);
        request.setAttribute("canViewReport",canViewReport);
        return "survey/recordList";
    }

    /**
     *  添加调查问卷作答数据：测评任务
     * @param entity 实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method= RequestMethod.POST)
    @ResponseBody
    public  Object addRecord(@RequestBody TaskSurveyRecordEntity entity, HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto)request.getSession().getAttribute("user");
        entity.setUserId(user.getUserId());
        if(surveyRecordService.addRecord(entity)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  添加调查问卷作答数据：测评任务
     * @param entity 实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_for_task_survey",method= RequestMethod.POST)
    @ResponseBody
    public  Object addRecordForTaskSurvey(@RequestBody TaskSurveyRecordEntity entity, HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto)request.getSession().getAttribute("user");
        entity.setUserId(user.getUserId());
        if(surveyRecordService.addRecordForTaskSurvey(entity)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  添加调查问卷作答数据：心理训练营
     * @param entity 实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_for_camp",method= RequestMethod.POST)
    @ResponseBody
    public  Object addRecordForCamp(@RequestBody TrainingCampSurveyEntity entity, HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto)request.getSession().getAttribute("user");
        entity.setUserId(user.getUserId());
        if(surveyRecordService.addRecordForCamp(entity)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  添加调查问卷作答数据：活动
     * @param entity 实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_for_activity",method= RequestMethod.POST)
    @ResponseBody
    public Object addRecordForActivity(@RequestBody ActivitySurveyEntity entity, HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto)request.getSession().getAttribute("user");
        entity.setUserId(user.getUserId());
        if(surveyRecordService.addRecordForActivity(entity)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  根据查询条件获取测评任务里问卷的作答记录集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value="/list",method=RequestMethod.POST)
    @ResponseBody
    public Object recordList(@RequestBody TaskSurveyDto dto) {
        return surveyRecordService.getListByPaged(dto);
    }

    @RequestMapping(value = "/task_survey_list",method = RequestMethod.POST)
    @ResponseBody
    public Object taskSurveyRecordList(@RequestBody TaskSurveyDto dto) {
        return surveyRecordService.getListForTaskSurveyByPaged(dto);
    }

    /**
     *  删除作答记录
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/del_record",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(surveyRecordService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  获取训练营问卷作答记录：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_trainingcamp_surveyrecordlist",method = RequestMethod.POST)
    @ResponseBody
    public Object getTrainingCampSurveyRecordListByPaged(@RequestBody TrainingCampSurveyRecordDto dto){
        return surveyRecordService.getTrainingCampSurveyRecordListByPaged(dto);
    }

    /**
     *  获取活动问卷作答记录：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_activity_surveyrecordlist",method = RequestMethod.POST)
    @ResponseBody
    public Object getActivitySurveyRecordListByPaged(@RequestBody ActivitySurveyRecordDto dto){
        return surveyRecordService.getActivitySurveyRecordListByPaged(dto);
    }

    /**
     *  获取我的问卷记录集合：分页
     * @param dto 查询条件
     * @return 问卷记录集合
     */
    @RequestMapping(value="/get_my_records",method=RequestMethod.POST)
    @ResponseBody
    public Object getMyRecords(@RequestBody TaskSurveyDto dto) {
        return surveyRecordService.getMyRecords(dto);
    }
}
