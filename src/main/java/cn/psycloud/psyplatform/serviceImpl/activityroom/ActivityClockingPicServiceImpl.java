package cn.psycloud.psyplatform.serviceImpl.activityroom;

import cn.psycloud.psyplatform.dao.activityroom.ActivityClockingPicDao;
import cn.psycloud.psyplatform.entity.activityroom.ActivityClockingPicEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityClockingPicService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class ActivityClockingPicServiceImpl implements ActivityClockingPicService {
    @Autowired
    private ActivityClockingPicDao activityClockingPicDao;

    /**
     * 插入签到图片上传记录
     * @param activityClockingPic 签到图片上传记录实体类
     * @return 影响行数
     */
    @Override
    public int insert(ActivityClockingPicEntity activityClockingPic) {
        return activityClockingPicDao.uploadPic(activityClockingPic);
    }

    /**
     * 删除签到图片上传记录
     * @param id 签到图片上传记录ID
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return activityClockingPicDao.delPic(id);
    }

    /**
     * 获取签到图片上传记录列表
     * @return 签到图片上传记录列表
     */
    @Override
    public List<ActivityClockingPicEntity> getList() {
        return activityClockingPicDao.getList();
    }

    /**
     * 批量删除签到图片上传记录
     * @param ids 签到图片上传记录ID集合
     * @return 影响行数
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     * 获取随机签到图片
     * @return 随机签到图片
     */
    @Override
    public String getRandomPic(){
        return activityClockingPicDao.getRandomPic();
    }
}
