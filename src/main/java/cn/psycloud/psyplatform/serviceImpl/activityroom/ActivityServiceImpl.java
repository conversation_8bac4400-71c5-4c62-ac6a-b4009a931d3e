package cn.psycloud.psyplatform.serviceImpl.activityroom;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.activityroom.ActivityDao;
import cn.psycloud.psyplatform.dao.activityroom.SelfEvaluationDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dto.activityroom.*;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivityEntity;
import cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.activityroom.ClockingService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 心理活动服务实现类
 */
@Service
public class ActivityServiceImpl implements ActivityService {
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private ClockingService clockingService;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    @Autowired
    private SelfEvaluationDao selfEvaluationDao;

    /**
     * 添加心理活动
     * @param activityEntity 心理活动实体
     * @return 活动ID
     */
    @Override
    public int add(ActivityEntity activityEntity){
        activityEntity.setAddDate(new Date());
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        activityEntity.setOperator(userDto.getUserId());
        return activityDao.add(activityEntity);
    }

    /**
     * 修改心理活动
     * @param activityEntity 心理活动实体
     * @return 是否成功
     */
    @Override
    public int update(ActivityEntity activityEntity){
        return activityDao.update(activityEntity);
    }

    /**
     * 删除心理活动
     * @param id 心理活动id
     * @return 是否成功
     */
    @Override
    public int delete(Integer id){
        return activityDao.delete(id);
    }

    /**
     * 批量删除心理活动
     * @param ids 心理活动id
     * @return 是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     * 根据条件查询心理活动列表：分页
     * @param activityDto 心理活动查询条件
     * @return 心理活动列表
     */
    @Override
    public BSDatatableRes<ActivityDto> getListByPaged(ActivityDto activityDto){
        var dtRes = new BSDatatableRes<ActivityDto>();
        PageHelper.startPage(activityDto.getPageIndex()/activityDto.getPageSize()+1,activityDto.getPageSize());
        var listCourses = activityDao.getList(activityDto);
        var dto = new PageInfo<>(listCourses);
        dtRes.setData(listCourses);
        dtRes.setRecordsTotal((int) dto.getTotal());
        dtRes.setRecordsFiltered((int) dto.getTotal());
        return dtRes;
    }

    /**
     * 根据条件查询心理活动列表
     * @param activityDto 心理活动查询条件
     * @return 心理活动列表
     */
    @Override
    public List<ActivityDto> getList(ActivityDto activityDto){
        return activityDao.getList(activityDto);
    }

    /**
     * 根据id查询心理活动
     * @param id 心理活动id
     * @return 心理活动详情
     */
    @Override
    public ActivityDto getById(Integer id){
        return activityDao.getById(id);
    }

    /**
     * 根据id查询心理活动详情
     * @param id 心理活动id
     * @return 心理活动详情
     */
    @Override
    public ActivityDto getForDetail(Integer id){
        var activityDto = getById(id);
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        var activityState = clockingService.getUserActivityStatus(userDto.getUserId(),id);
        activityDto.setActivityStateForUser(activityState);
        var isSurveyDone = 0;
        if(isSurveyDone(userDto.getUserId(),id,activityDto.getSurveyId())){
            isSurveyDone = 1;
        }
        else{
            //获取问卷
            var surveyId = activityDto.getSurveyId();
            var surveyDto = new SurveyDto();
            surveyDto.setId(surveyId);
            surveyDto.setSurveyName(activityDto.getSurveyName());
            var surveyQuestions = surveyQuestionDao.getListBySurveyId(surveyId);
            surveyDto.setListQuestions(surveyQuestions);
            activityDto.setSurvey(surveyDto);
        }
        activityDto.setIsSurveyDone(isSurveyDone);
        return activityDto;
    }

    /**
     * 判断调查问卷是否已做
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param surveyId 调查问卷ID
     * @return 是否已做
     */
    @Override
    public boolean isSurveyDone(Integer userId, Integer activityId, Integer surveyId){
        var map = new HashMap<String,Integer>();
        map.put("userId",userId);
        map.put("activityId",activityId);
        map.put("surveyId",surveyId);
        return activityDao.isSurveyDone(map) > 0;
    }

    /**
     * 导出活动问卷作答记录
     * @param dto 导出条件
     * @return 导出数据
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportSurveyResult(ExportActivitySurveyRecordDto dto){
        List<LinkedHashMap<String,Object>> questions =surveyQuestionDao.getListForExport(dto.getSurveyId() == null ? 0 : dto.getSurveyId());
        StringBuilder questionBuilder = new StringBuilder();
        for(LinkedHashMap<String,Object> questionMap: questions){
            questionBuilder.append(String.format("max(case when psq.q_number ='%s' then psr.item_id else 0 end) as 第%s题,",questionMap.get("q_number"),questionMap.get("q_number")));
        }
        String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
        dto.setSql(qNoStr);
        return activityDao.getExportSurveyTestResult(dto);
    }

    /**
     * 获取我的活动
     * @param userId 用户ID
     * @return 我的活动列表
     */
    @Override
    public List<ActivityDto> getMyActivities(Integer userId){
        var activitys =  activityDao.getMyActivities(userId);
        for (ActivityDto activity : activitys) {
            var map = new HashMap<String,Object>();
            map.put("userId",userId);
            map.put("activityId",activity.getId());
            var tags = selfEvaluationDao.getMySelfEvaluationList(map);
            activity.setTags(tags);
        }
        return activitys;
    }

    /**
     * 更新活动总评
     * @param overallEvaluationEntity 总评实体
     * @return 是否成功
     */
    @Override
    public int updateOverallEvaluation(OverallEvaluationEntity overallEvaluationEntity){
        return activityDao.updateOverallEvaluation(overallEvaluationEntity);
    }

    /**
     * 获取活动总评
     * @param activityId 活动ID
     * @return 活动总评
     */
    @Override
    public OverallEvaluationDto getOverallEvaluation(Integer activityId){
        return activityDao.getOverallEvaluation(activityId);
    }

    /**
     * 获取活动报告
     * @param activityId 活动ID
     * @return 活动报告
     */
    @Override
    public ActivityReportDto getActivityReport(Integer activityId){
        var activityReport = activityDao.getActivityReport(activityId);
        var selfEvaluations = selfEvaluationDao.getSelfEvaluationList(activityId);
        activityReport.setSelfEvaluations(selfEvaluations);
        return activityReport;
    }

    /**
     * 获取咨询师的活动集合：select
     * @param userId 咨询师id
     * @return 活动集合
     */
    @Override
    public List<Object> getCounselorActivitiesForSelect(Integer userId){
        var activities =  activityDao.getCounselorActivitiesForSelect(userId);
        var lsNode = new ArrayList<>();
        for (ActivityForSelectDto dto : activities) {
            var select2Data = new Select2Data();
            select2Data.setId(dto.getId());
            select2Data.setText(dto.getActivityName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }
}
