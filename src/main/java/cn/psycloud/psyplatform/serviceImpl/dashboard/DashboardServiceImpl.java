package cn.psycloud.psyplatform.serviceImpl.dashboard;

import cn.psycloud.psyplatform.dao.counselingroom.CounselingQuestionDao;
import cn.psycloud.psyplatform.dao.dashboard.DashboardDao;
import cn.psycloud.psyplatform.dao.homePage.StatOfHomePageDao;
import cn.psycloud.psyplatform.dao.relaxroom.ArticleCategoryDao;
import cn.psycloud.psyplatform.dto.homePage.DashboardDto;
import cn.psycloud.psyplatform.service.dashboard.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DashboardServiceImpl implements DashboardService {
    @Autowired
    private DashboardDao dashboardDao;
    @Autowired
    private CounselingQuestionDao questionDao;
    @Autowired
    private StatOfHomePageDao statOfHomePageDao;
    @Autowired
    private ArticleCategoryDao articleCategoryDao;
    /**
     *  获取数据看板数据
     * @return 实体对象
     */
    @Override
    public DashboardDto get(){
        DashboardDto dto =dashboardDao.get();
        dto.setCounselingQuestions(questionDao.getForDashboard());
        dto.setCounselingCapacitys(statOfHomePageDao.getCounselingCapacity());
        dto.setCounselingTypeCapacitys(questionDao.getCounselingTypeCapacityList());
        dto.setCounselingQuestionCapacitys(questionDao.getCounselingQuestionCapacitytList());
        dto.setTestRecordCapacitys(statOfHomePageDao.getTestRecordCapacitys());
        dto.setArticleCapacitys(articleCategoryDao.getArticleCapacityList());
        return dto;
    }
}
