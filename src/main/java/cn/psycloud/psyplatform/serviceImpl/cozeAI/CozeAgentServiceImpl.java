package cn.psycloud.psyplatform.serviceImpl.cozeAI;

import cn.hutool.json.JSONObject;
import cn.psycloud.psyplatform.dao.aiagent.RiskWarningDao;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.cozeAI.Agent.BotInfoDetailDto;
import cn.psycloud.psyplatform.dto.cozeAI.Agent.OpenGetBotDataDto;
import cn.psycloud.psyplatform.dto.cozeAI.CozeAgentResponseDto;
import cn.psycloud.psyplatform.dto.cozeAI.chat.AdditionalMessage;
import cn.psycloud.psyplatform.dto.cozeAI.chat.ChatV3MessageDetail;
import cn.psycloud.psyplatform.dto.cozeAI.chat.CozeAgentChatRequestDto;
import cn.psycloud.psyplatform.dto.cozeAI.conversation.ConversationData;
import cn.psycloud.psyplatform.dto.cozeAI.conversation.SectionDto;
import cn.psycloud.psyplatform.entity.aiagent.conversation.ConversationEntity;
import cn.psycloud.psyplatform.entity.aiagent.conversation.RiskWarningEntity;
import cn.psycloud.psyplatform.service.aiagent.ConversationService;
import cn.psycloud.psyplatform.service.cozeAI.CozeAgentService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@Slf4j
@Service
public class CozeAgentServiceImpl implements CozeAgentService {
    @Autowired
    private CozeAIUtil cozeAIUtil;
    @Autowired
    private ConversationService conversationService;
    @Value("${cozeAI.analyze-agent.botId}")
    private String analyzeBotId;
    @Autowired
    private RiskWarningDao riskWarningDao;

    /**
     * 获取鉴权Token
     * @return token
     */
    @Override
    public String getAccessToken(Integer userId){
        var accessToken = "";
        if(JedisUtil.exists("coze-token")){
            accessToken = JedisUtil.get("coze-token");
        }
        else{
            accessToken = cozeAIUtil.getAccessToken(userId);
            JedisUtil.set("coze-token",accessToken,86399);
        }
        return accessToken;
    }

    /**
     * 创建会话
     * @param botId 智能体ID
     * @param accessToken access_token
     * @return 会话响应实体对象
     */
    private CozeAgentResponseDto<ConversationData> createConversation(String botId,String accessToken){
        return cozeAIUtil.createConversation(botId, accessToken);
    }

    /**
     * 获取聊天请求实体对象
     * @param map 参数集合
     * @return 聊天请求实体对象
     */
    private CozeAgentChatRequestDto getCozeAgentChatRequestDto(HashMap<String,Object> map) {
        var dto = new CozeAgentChatRequestDto();
        dto.setBot_id(map.get("botId").toString());
        dto.setUser_id(map.get("userId").toString());
        dto.setAuto_save_history(true);
        dto.setStream(false);
        var additionalMessage = new AdditionalMessage();
        additionalMessage.setRole("user");
        additionalMessage.setType("question");
        additionalMessage.setContent(map.get("content").toString());
        additionalMessage.setContent_type(map.get("contentType").toString());
        dto.setAdditional_messages(new ArrayList<AdditionalMessage>() {{
            add(additionalMessage);
        }});
        return dto;
    }

    /**
     * 分析用户输入内容，进行话题识别与心理风险预警
     * @param userId 用户ID
     * @param content 用户输入的内容
     * @return 分析结果的JSONObject
     */
    private Optional<JSONObject> analyzeContent(Integer userId, String content) {
        var accessToken = getAccessToken(userId);
        // 1. 构造请求
        var requestDto = new CozeAgentChatRequestDto();
        requestDto.setBot_id(analyzeBotId);
        requestDto.setUser_id(userId.toString());
        requestDto.setStream(false);
        String prompt = content;

        var additionalMessage = new AdditionalMessage();
        additionalMessage.setRole("user");
        additionalMessage.setType("question");
        additionalMessage.setContent(prompt);
        additionalMessage.setContent_type("text");
        requestDto.setAdditional_messages(new ArrayList<AdditionalMessage>() {{
            add(additionalMessage);
        }});

        // 2. 为本次分析创建临时会话
        var convResponse = createConversation(analyzeBotId, accessToken);
        if (convResponse.getCode() != 0) {
            return Optional.empty();
        }
        String conversationId = convResponse.getData().getId();

        // 3. 发送聊天请求
        var chatResponse = cozeAIUtil.createChat(accessToken, conversationId, requestDto);
        if (chatResponse.getCode() != 0) {
            return Optional.empty();
        }
        // 4. 轮询获取结果
        var chatId = chatResponse.getData().getId();
        var status = chatResponse.getData().getStatus();
        if ("in_progress".equals(status)) {
            int maxRetries = 30; // 30次，每次等待2秒
            int retryCount = 0;
            while (retryCount < maxRetries && "in_progress".equals(status)) {
                try {
                    Thread.sleep(2000); // 等待2秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return Optional.empty();
                }
                var statusCheckDto = cozeAIUtil.getChatStatus(accessToken, conversationId, chatId);
                if (statusCheckDto.getCode() == 0) {
                    status = statusCheckDto.getData().getStatus();
                    retryCount++;
                } else {
                    break; // 退出轮询
                }
            }
        }

        // 5. 获取并解析结果
        if (!"completed".equals(status)) {
            return Optional.empty();
        }

        var messageDetailDto = cozeAIUtil.getChatMessageDetail(accessToken, conversationId, chatId);
        if (messageDetailDto.getCode() == 0) {
            return messageDetailDto.getData().stream()
                    .filter(m -> "answer".equals(m.getType()))
                    .findFirst()
                    .map(answer -> {
                        try {
                            return new JSONObject(answer.getContent());
                        } catch (Exception e) {
                            return null;
                        }
                    });
        } else {
            log.error("获取分析消息详情失败: {}", messageDetailDto.getMsg());
        }

        return Optional.empty();
    }

    /**
     * 创建聊天
     * @param map 参数集合
     * @return 聊天响应实体对象
     */
    @Override
    public JsonResult<List<ChatV3MessageDetail>> createChat(HashMap<String, Object> map){
        var result = new JsonResult<List<ChatV3MessageDetail>>();
        var userId = Integer.parseInt(map.get("userId").toString());

        // 获取access_token
        var accessToken = getAccessToken(userId);
        // 如果会话id为空，先创建会话
        var conversationId = map.get("conversationId").toString();
        var botId = map.get("botId").toString();
        if(conversationId == null || conversationId.isEmpty()){
            var cozeAgentResponseDto = createConversation(botId,accessToken);
            if(cozeAgentResponseDto.getCode() == 0){
                conversationId = cozeAgentResponseDto.getData().getId();
            }
            else{
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(cozeAgentResponseDto.getMsg());
            }
        }

        // 对用户输入内容进行分析，并保存到数据库
        String contentToAnalyze = map.get("content").toString();
        var jsonObject = analyzeContent(userId, contentToAnalyze);
        if (jsonObject.isPresent()) {
            JSONObject obj = jsonObject.get();
            RiskWarningEntity entity = new RiskWarningEntity();
            entity.setUserId(userId);
            entity.setConversationId(conversationId);
            entity.setTopic(obj.getStr("topic"));
            entity.setRiskLevel(obj.getStr("risk_level"));
            entity.setRiskReason(obj.getStr("risk_reason"));
            entity.setRiskAccord(obj.getStr("risk_accord"));
            entity.setRiskDetail(obj.getStr("risk_detail")); // 注意：riskDetail
            entity.setRiskDate(new Date());
            riskWarningDao.addRiskWarning(entity);
        }

        final String finalConversationId = conversationId;
        final String finalBotId = botId;
        // 发送聊天请求
        var dto = getCozeAgentChatRequestDto(map);
        var responseDto = cozeAIUtil.createChat(accessToken, conversationId, dto);
        if(responseDto.getCode() == 0){
            var chatId = responseDto.getData().getId();
            var status = responseDto.getData().getStatus();
            // 如果状态为in_progress，需要轮询等待完成
            if("in_progress".equals(status)){
                // 轮询等待聊天完成，最多等待60秒
                int maxRetries = 30; // 30次，每次等待2秒
                int retryCount = 0;

                while(retryCount < maxRetries && "in_progress".equals(status)){
                    try {
                        Thread.sleep(2000); // 等待2秒
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }

                    // 重新查询聊天状态
                    var statusCheckDto = cozeAIUtil.getChatStatus(accessToken, conversationId, chatId);
                    if(statusCheckDto.getCode() == 0){
                        status = statusCheckDto.getData().getStatus();
                        retryCount++;
                    } else {
                        break;
                    }
                }

                // 如果超时仍未完成
                if("in_progress".equals(status)){
                    result.setResultCode(ResultCodeAndMsg.FailureCode);
                    result.setResultMsg("聊天请求处理超时，请稍后重试");
                    return result;
                }
            }
            // 获取聊天消息详情
            var messageDetailDto = cozeAIUtil.getChatMessageDetail(accessToken, conversationId, chatId);
            if (messageDetailDto.getCode() == 0) {
                result.setData(messageDetailDto.getData());
                // 保存用户消息
                var botInfo = getAgentInfo(userId, finalBotId).getData();
                var userConversation = new ConversationEntity();
                userConversation.setBotId(finalBotId);
                userConversation.setBotName(botInfo.getName());
                userConversation.setUserId(Integer.parseInt(map.get("userId").toString()));
                userConversation.setConversationId(finalConversationId);
                userConversation.setRole("user");
                userConversation.setChatContent(map.get("content").toString());
                conversationService.addConversation(userConversation);
                // 保存AI消息
                messageDetailDto.getData().stream()
                        .filter(m -> "answer".equals(m.getType()))
                        .findFirst()
                        .ifPresent(answer -> {
                            var assistantConversation = new ConversationEntity();
                            assistantConversation.setBotId(finalBotId);
                            assistantConversation.setBotName(botInfo.getName());
                            assistantConversation.setUserId(Integer.parseInt(map.get("userId").toString()));
                            assistantConversation.setConversationId(finalConversationId);
                            assistantConversation.setRole("assistant");
                            assistantConversation.setChatContent(answer.getContent());
                            conversationService.addConversation(assistantConversation);
                        });
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(messageDetailDto.getMsg());
            }
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(responseDto.getMsg());
        }
        return result;
    }

    /**
     * 上传文件
     * @param userId 用户id
     * @param file 要上传的文件
     * @return 文件上传结果
     */
    @Override
    public JsonResult<JSONObject> uploadFile(Integer userId, MultipartFile file) {
        var result = new JsonResult<JSONObject>();
        try {
            // 获取access_token
            var accessToken = getAccessToken(userId);
            // 调用文件上传API
            var uploadResult = cozeAIUtil.uploadFile(file, accessToken);
            if (uploadResult.getCode() == 0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg("文件上传成功");
                result.setData(uploadResult.getData());
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(uploadResult.getMsg());
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("文件上传失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 上传文件（使用字节数组）
     * @param userId 用户id
     * @param fileBytes 文件字节数组
     * @param fileName 文件名
     * @return 文件上传结果
     */
    @Override
    public JsonResult<JSONObject> uploadFileWithBytes(Integer userId, byte[] fileBytes, String fileName) {
        var result = new JsonResult<JSONObject>();
        try {
            // 获取access_token
            var accessToken = getAccessToken(userId);
            // 调用文件上传API
            var uploadResult = cozeAIUtil.uploadFileWithBytes(fileBytes, fileName, accessToken);
            if (uploadResult.getCode() == 0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg("文件上传成功");
                result.setData(uploadResult.getData());
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(uploadResult.getMsg());
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("文件上传失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取智能体列表
     * @param userId 用户id
     * @param spaceId 空间id
     * @return 智能体列表
     */
    public JsonResult<OpenGetBotDataDto> getAgentList(Integer userId, String spaceId) {
        var result = new JsonResult<OpenGetBotDataDto>();
        try {
            // 获取access_token
            var accessToken = getAccessToken(userId);
            // 调用获取智能体列表API
            var agentListResult = cozeAIUtil.getAgentList(accessToken, spaceId);
            if (agentListResult.getCode() == 0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg("获取智能体列表成功");
                result.setData(agentListResult.getData());
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(agentListResult.getMsg());
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("获取智能体列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取智能体信息
     * @param userId 用户id
     * @param botId 智能体id
     * @return 智能体信息
     */
    public JsonResult<BotInfoDetailDto> getAgentInfo(Integer userId, String botId) {
        var result = new JsonResult<BotInfoDetailDto>();
        try {
            // 获取access_token
            var accessToken = getAccessToken(userId);
            // 调用获取智能体信息API
            var agentInfoResult = cozeAIUtil.getAgentInfo(accessToken, botId);
            if (agentInfoResult.getCode() == 0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg("获取智能体信息成功");
                result.setData(agentInfoResult.getData());
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(agentInfoResult.getMsg());
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("获取智能体信息失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 清空会话
     * @param userId 用户id
     * @param conversationId 会话id
     * @return 清空会话结果
     */
    public JsonResult<SectionDto> clearSection(Integer userId, String conversationId) {
        var result = new JsonResult<SectionDto>();
        try {
            // 获取access_token
            var accessToken = getAccessToken(userId);
            // 调用清空会话API
            var clearSectionResult = cozeAIUtil.clearSection(accessToken, conversationId);
            if (clearSectionResult.getCode() == 0) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg("清空会话成功");
                result.setData(clearSectionResult.getData());
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(clearSectionResult.getMsg());
            }
        }
        catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("清空会话失败：" + e.getMessage());
        }
        return result;
    }
}
