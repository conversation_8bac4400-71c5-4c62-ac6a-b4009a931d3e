package cn.psycloud.psyplatform.serviceImpl.caremap;

import cn.psycloud.psyplatform.dao.caremap.CaremapArticleDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.relaxroom.ArticleDto;
import cn.psycloud.psyplatform.entity.relaxroom.ArticleEntity;
import cn.psycloud.psyplatform.service.caremap.CaremapArticleService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CaremapArticleServiceImpl implements CaremapArticleService {
    @Autowired
    private CaremapArticleDao articleDao;

    /**
     *  添加文章
     * @param entity 文章实体对象
     * @return 影响行数
     */
    @Override
    public int addArticle(ArticleEntity entity){
        entity.setAddDate(new Date());
        var user = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setOperator(user.getUserId());
        return articleDao.insert(entity);
    }

    /**
     *  修改文章
     * @param entity 文章实体对象
     * @return 影响行数
     */
    @Override
    public int updateArticle(ArticleEntity entity){
        return articleDao.update(entity);
    }

    /**
     *  删除文章
     * @param id 文章id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return articleDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  查询文章集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<ArticleDto> getListByPaged(ArticleDto dto){
        var dtRes = new BSDatatableRes<ArticleDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listArticles = articleDao.getList(dto);
        var articleDto = new PageInfo<>(listArticles);
        dtRes.setData(articleDto.getList());
        dtRes.setRecordsTotal((int)articleDto.getTotal());
        dtRes.setRecordsFiltered((int)articleDto.getTotal());
        return dtRes;
    }

    /**
     *  查询文章信息
     * @param id 文章id
     * @return 文章实体对象
     */
    @Override
    public ArticleDto getById(Integer id){
        return articleDao.getById(id);
    }

    /**
     *  更新点击量
     * @param id 文章id
     * @return 影响行数
     */
    @Override
    public int addHitCount(Integer id){
        return articleDao.addHitCount(id);
    }

    /**
     *  查询文章集合
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public List<ArticleDto> getList(ArticleDto dto){
        return articleDao.getList(dto);
    }

    /**
     *  获取栏目下的文章集合
     * @param categoryId 栏目id
     * @return 文章集合
     */
    @Override
    public List<ArticleDto> getListByType(Integer categoryId) {
        var articleDto = new ArticleDto();
        articleDto.setCategoryId(categoryId);
        return getList(articleDto);
    }
}
