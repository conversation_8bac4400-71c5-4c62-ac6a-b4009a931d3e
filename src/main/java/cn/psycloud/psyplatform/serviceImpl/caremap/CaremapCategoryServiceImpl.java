package cn.psycloud.psyplatform.serviceImpl.caremap;

import cn.psycloud.psyplatform.dao.caremap.CaremapCategoryDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.relaxroom.ArticleCategoryDto;
import cn.psycloud.psyplatform.dto.relaxroom.ArticleDto;
import cn.psycloud.psyplatform.entity.relaxroom.ArticleCategoryEntity;
import cn.psycloud.psyplatform.service.caremap.CaremapArticleCategoryService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CaremapCategoryServiceImpl implements CaremapArticleCategoryService {
    @Autowired
    private CaremapCategoryDao articleCategoryDao;
    /**
     *  根据id查询栏目
     * @param categoryId 栏目id
     * @return 栏目实体对象
     */
    @Override
    public ArticleCategoryDto getById(Integer categoryId){
        return articleCategoryDao.getById(categoryId);
    }

    /**
     *  查询文章栏目集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<ArticleCategoryDto> getListByPaged(ArticleCategoryDto dto){
        var dtRes = new BSDatatableRes<ArticleCategoryDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listCategorys = articleCategoryDao.getListByPaged(dto);
        var articleCategoryDto = new PageInfo<>(listCategorys);
        dtRes.setData(articleCategoryDto.getList());
        dtRes.setRecordsTotal((int)articleCategoryDto.getTotal());
        dtRes.setRecordsFiltered((int)articleCategoryDto.getTotal());
        return dtRes;
    }

    /**
     *  查询文章栏目集合
     * @return 集合
     */
    @Override
    public List<ArticleCategoryDto> getList() {
        var dto = new ArticleCategoryDto();
        return articleCategoryDao.getList(dto);
    }

    /**
     *  网站首页文章列表
     * @param pageSize 每页数量
     * @return 集合
     */
    @Override
    public List<ArticleCategoryDto> getArticleListForIndex(Integer pageSize){
        var categoryList = getList();
        for(ArticleCategoryDto articleCategoryDto: categoryList) {
            var articles = articleCategoryDto
                    .getArticles()
                    .stream()
                    .sorted(Comparator.comparing(ArticleDto::getAddDate).reversed())
                    .limit(pageSize)
                    .collect(Collectors.toList());
            articleCategoryDto.setArticles(articles);
        }
        return categoryList;
    }

    /**
     *  查询文章栏目集合：转换成select所需格式
     * @return 集合
     */
    @Override
    public List<Object> getListForSelect(){
        var listCategorys = getList();
        return getSelect2Data(listCategorys);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listCategorys 问题分类集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<ArticleCategoryDto> listCategorys) {
        var lsNode = new ArrayList<>();
        for (ArticleCategoryDto dto : listCategorys) {
            var select2Data = new Select2Data();
            select2Data.setId(dto.getId());
            select2Data.setText(dto.getCategoryName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  新增
     * @param entity 栏目实体对象
     * @return 影响行数
     */
    @Override
    public int add(ArticleCategoryEntity entity){
        return articleCategoryDao.insert(entity);
    }

    /**
     *  更新
     * @param entity 栏目实体对象
     * @return 影响行数
     */
    @Override
    public int update(ArticleCategoryEntity entity) {
        return articleCategoryDao.update(entity);
    }

    /**
     *  删除
     * @param id 栏目id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return articleCategoryDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }
}
