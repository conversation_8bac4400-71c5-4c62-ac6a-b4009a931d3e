package cn.psycloud.psyplatform.serviceImpl.trainingcamp;

import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCourseDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.trainingcamp.TrainingCourseDto;
import cn.psycloud.psyplatform.entity.trainingcamp.TrainingCourseEntity;
import cn.psycloud.psyplatform.service.trainingcamp.TrainingCourseService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TrainingCourseServiceImpl implements TrainingCourseService {
    @Autowired
    private TrainingCourseDao trainingCourseDao;
    /**
     *  查询训练营课程集合：分页
     * @param dto 课程实体对象
     * @return 课程集合
     */
    @Override
    public BSDatatableRes<TrainingCourseDto> getListByPaged(TrainingCourseDto dto) {
        var dtRes = new BSDatatableRes<TrainingCourseDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listCourses = trainingCourseDao.getList(dto);
        var courseDto = new PageInfo<>(listCourses);
        dtRes.setData(courseDto.getList());
        dtRes.setRecordsTotal((int) courseDto.getTotal());
        dtRes.setRecordsFiltered((int)courseDto.getTotal());
        return dtRes;
    }

    /**
     *  根据课程id获取训练营课程信息
     * @param courseId 课程id
     * @return 课程信息
     */
    @Override
    public TrainingCourseDto getById(Integer courseId){
        return trainingCourseDao.get(courseId);
    }

    @Override
    public List<Object> getListForSelect() {
        var listCourses = trainingCourseDao.getListForSelect();
        return getSelect2Data(listCourses);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listCourses 课程集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<TrainingCourseDto> listCourses) {
        var lsNode = new ArrayList<>();
        for (TrainingCourseDto dto : listCourses) {
            var select2Data = new Select2Data();
            select2Data.setId(dto.getId());
            var courseType  = "";
            if(dto.getCourseType() == 1) courseType = "视频";
            if(dto.getCourseType() == 2) courseType = "音频";
            if(dto.getCourseType() == 3) courseType = "文章";
            select2Data.setText("【"+courseType+"】" + dto.getCourseName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  添加训练营课程
     * @param entity 课程实体对象
     * @return 影响行数
     */
    @Override
    public int add(TrainingCourseEntity entity){
        var userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setOperator(Integer.parseInt(userDto.getUserId().toString()));
        entity.setAddDate(new Date());
        return trainingCourseDao.add(entity);
    }

    /**
     *  修改训练营课程
     * @param entity 课程实体对象
     * @return 影响行数
     */
    @Override
    public int update(TrainingCourseEntity entity){
        return trainingCourseDao.update(entity);
    }

    /**
     *  删除训练营课程
     * @param courseId 课程id
     * @return 影响行数
     */
    @Override
    public int delete(Integer courseId){
        return trainingCourseDao.delete(courseId);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }
}
