package cn.psycloud.psyplatform.serviceImpl.trainingcamp;

import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampCheckInDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampCheckInEntity;
import cn.psycloud.psyplatform.service.trainingcamp.TrainingCampCheckInService;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TrainingCampCheckInServiceImpl implements TrainingCampCheckInService {
    @Autowired
    private TrainingCampCheckInDao trainingCampCheckInDao;

    /**
     *  添加训练营打卡记录
     * @param entity 打卡记录实体对象
     * @return 影响行数
     */
    @Override
    public int addCheckInRecord(TrainingCampCheckInEntity entity){
        var userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setUserId(userDto.getUserId());
        entity.setCheckInDate(new Date());
        return trainingCampCheckInDao.addCheckInRecord(entity);
    }

    /**
     *  验证用户是否打卡课程
     * @param entity 打卡记录实体对象
     * @return 记录数
     */
    @Override
    public int isCheckIn(TrainingCampCheckInEntity entity){
        return trainingCampCheckInDao.isCheckIn(entity);
    }
}
