package cn.psycloud.psyplatform.serviceImpl.trainingcamp;

import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampUserDao;
import cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampUserEntity;
import cn.psycloud.psyplatform.service.trainingcamp.TrainingCampUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TrainingCampUserServiceImpl implements TrainingCampUserService {
    @Autowired
    private TrainingCampUserDao trainingCampUserDao;
    /**
     *  报名训练营
     * @param entity 学员实体对象
     * @return 影响行数
     */
    public int regCamp(TrainingCampUserEntity entity){
        return trainingCampUserDao.addCampUser(entity);
    }
}
