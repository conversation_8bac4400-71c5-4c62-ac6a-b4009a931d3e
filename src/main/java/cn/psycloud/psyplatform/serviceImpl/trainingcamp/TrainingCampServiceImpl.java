package cn.psycloud.psyplatform.serviceImpl.trainingcamp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampCheckInDao;
import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampDao;
import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampUserDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.platform.SendSmsDto;
import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.dto.trainingcamp.*;
import cn.psycloud.psyplatform.entity.trainingcamp.*;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import cn.psycloud.psyplatform.service.trainingcamp.TrainingCampService;
import cn.psycloud.psyplatform.util.SessionUtil;
import cn.psycloud.psyplatform.util.sms.aliyun.SendSmsService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TrainingCampServiceImpl implements TrainingCampService {
    @Autowired
    private TrainingCampDao trainingCampDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private TrainingCampUserDao trainingCampUserDao;
    @Autowired
    private TrainingCampCheckInDao trainingCampCheckInDao;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SendSmsService sendSmsService;

    /**
     *  添加训练营基本信息
     * @param entity 实体类对象
     * @return 训练营id
     */
    @Override
    public int addBaseInfo(TrainingCampEntity entity){
        var userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setId(0);
        entity.setOperator(userDto.getUserId());
        entity.setAddDate(new Date());
        trainingCampDao.addBaseInfo(entity);
        return entity.getId();
    }

    /**
     *  修改训练营基本信息
     * @param entity 实体类对象
     * @return 影响行数
     */
    @Override
    public int updateBaseInfo(TrainingCampEntity entity){
        return trainingCampDao.updateBaseInfo(entity);
    }

    /**
     *  获取训练营集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<TrainingCampDto> getListByPaged(TrainingCampDto dto){
        var dtRes = new BSDatatableRes<TrainingCampDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listCamps = trainingCampDao.getCampList(dto);
        var trainingCampDto = new PageInfo<>(listCamps);
        dtRes.setData(trainingCampDto.getList());
        dtRes.setRecordsTotal((int) trainingCampDto.getTotal());
        dtRes.setRecordsFiltered((int) trainingCampDto.getTotal());
        return dtRes;
    }

    /**
     *  根据id获取训练营信息
     * @param id  训练营id
     * @return 训练营对象
     */
    @Override
    public TrainingCampDto getById(Integer id){
        return trainingCampDao.get(id);
    }

    /**
     *  根据id获取训练营信息：更新训练营基本信息界面
     * @param id 训练营id
     * @return 训练营对象
     */
    @Override
    public TrainingCampDto getForUpdate(Integer id){
        return trainingCampDao.getForUpdate(id);
    }

    /**
     *  在训练营介绍页获取训练营信息
     * @param campId 训练营id
     * @return 训练营对象
     */
    @Override
    public TrainingCampDetailDto getByIdForDetail(Integer campId){
        var trainingCampDetailDto = new TrainingCampDetailDto();
        var trainingCampDto = getById(campId);
        //训练营信息
        trainingCampDetailDto.setTrainingCamp(trainingCampDto);
        //是否报名
        boolean hasInCamp = isUserInCamp(campId);
        trainingCampDetailDto.setReg(hasInCamp);
        //是否满员
        boolean isCampFull = isCampFull(campId);
        trainingCampDetailDto.setCampFull(isCampFull);
        //判断训练营状态
        var campStartDate = trainingCampDto.getStartDate();
        var campEndDate = trainingCampDto.getEndDate();
        var regDeallineDate = trainingCampDto.getRegDeadline();
        var today =new Date();
        var campUserState = "";
        if(!hasInCamp){ //未报名
            if(today.before(regDeallineDate)) { //报名截止时间之前
                if(trainingCampDto.getUnlimitedNum().equals(0) || (trainingCampDto.getUnlimitedNum().equals(1) && !isCampFull)){
                    campUserState = "参与报名";
                }
                else{
                    campUserState = "已满员";
                }
            }
            else if(today.after(regDeallineDate) && (today.getTime() < campEndDate.getTime())){ //过了报名截止时间但是未到结束时间
                campUserState = "报名已截止";
            }
            else {
                campUserState = "已结束";
            }
        }
        else{ //已报名
            if(today.before(campStartDate)){
                campUserState = "已报名，等待开营中";
            }
            else if(today.after(campStartDate) && today.before(campEndDate)){
                campUserState = "前往打卡";
            }
            else {
                campUserState = "训练营已结束";
            }
        }
        trainingCampDto.setCampStateForUser(campUserState);
        //是否完成全部打卡内容
        trainingCampDetailDto.setAllCheckIn(isAllTaskCheckIn(trainingCampDetailDto));
        //是否发放问卷
        trainingCampDetailDto.setSurveyIssued(isSurveyIssued(trainingCampDetailDto));
        if(trainingCampDetailDto.getTrainingCamp().getIsSurvey() == 1){
            //获取问卷
            var surveyId = trainingCampDetailDto.getTrainingCamp().getSurveyId();
            var surveyDto = new SurveyDto();
            surveyDto.setId(surveyId);
            surveyDto.setSurveyName(trainingCampDetailDto.getTrainingCamp().getSurveyName());
            var surveyQuestions = surveyQuestionDao.getListBySurveyId(surveyId);
            surveyDto.setListQuestions(surveyQuestions);
            trainingCampDetailDto.setSurvey(surveyDto);
        }
        //是否发放证书
        trainingCampDetailDto.setCertShown(isCertShown(trainingCampDetailDto));
        return trainingCampDetailDto;
    }

    /**
     *  是否发放证书
     * @param trainingCampDetailDto  训练营实体对象
     * @return 是否发放证书标识
     */
    private boolean isCertShown(TrainingCampDetailDto trainingCampDetailDto){
        boolean isCertShown = false;
        var trainingCampDto = trainingCampDetailDto.getTrainingCamp();
        if( trainingCampDto.getCertEnabled() != null && trainingCampDto.getCertEnabled() == 1){ //启用证书
            //Date campEndDate = trainingCampDto.getEndDate();
            if(isAllTaskCheckIn(trainingCampDetailDto)){ //所有打卡完成并且训练营已结束
                isCertShown = true;
            }
        }
        return isCertShown;
    }

    /**
     *  验证是否可以发放问卷
     * @param trainingCampDetailDto 训练营实体对象
     * @return 是否发放问卷标识
     */
    private boolean isSurveyIssued(TrainingCampDetailDto trainingCampDetailDto){
        boolean isSurveyIssued = false;
        var trainingCampDto = trainingCampDetailDto.getTrainingCamp();
        if(trainingCampDto.getIsSurvey() != null && trainingCampDto.getIsSurvey() == 1){ //启用调查问卷
            if(trainingCampDto.getSurveyStartDate() == 1){ //问卷发放时间：训练营任务完成打卡
                isSurveyIssued = isAllTaskCheckIn(trainingCampDetailDto);
            }
            if(trainingCampDto.getSurveyStartDate() == 2){ //问卷发放时间：训练营结束前一天
                Date campEndDate = trainingCampDto.getEndDate();
                Date dayBeforeEnd = DateUtil.offsetDay(campEndDate, -1);
                Date today =new Date();
                isSurveyIssued =today.after(dayBeforeEnd) && today.before(trainingCampDto.getSurveyEndDate());
            }
        }
        return isSurveyIssued;
    }

    /**
     *  是否完成全部打卡
     * @param trainingCampDetailDto 训练营实体对象
     * @return 是否完成标识
     */
    private boolean isAllTaskCheckIn(TrainingCampDetailDto trainingCampDetailDto){
        boolean isCheckIn = false;
        for(TrainingCampTaskDto trainingCampTaskDto: trainingCampDetailDto.getTrainingCamp().getTasks()){
            var courses = trainingCampTaskDto.getCourses();
            for(TrainingCourseDto trainingCourseDto: courses){
                var checkInEntity = new TrainingCampCheckInEntity();
                checkInEntity.setCampId(trainingCampDetailDto.getTrainingCamp().getId());
                checkInEntity.setTaskId(trainingCampTaskDto.getId());
                checkInEntity.setCourseId(trainingCourseDto.getId());

                var userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
                checkInEntity.setUserId(userDto.getUserId());
                isCheckIn = trainingCampCheckInDao.isCheckIn(checkInEntity) > 0;
            }
        }
        return  isCheckIn;
    }

    /**
     *  在打卡界面获取训练营信息
     * @param campId 训练营id
     * @return 训练营对象
     */
    @Override
    public TrainingCampCheckInDto getByIdForTraining(Integer campId){
        var trainingCampCheckInDto = new TrainingCampCheckInDto();
        var trainingCampDto = trainingCampDao.get(campId);
        int totalCourseCount = 0;
        int checkInCount = 0;
        var userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
        for(TrainingCampTaskDto trainingCampTaskDto : trainingCampDto.getTasks()){
            List<TrainingCourseDto> courses = trainingCampTaskDto.getCourses()
                    .stream()
                    .map(trainingCourse ->{
                        var courseDto = new TrainingCourseDto();
                        courseDto.setId(trainingCourse.getId());
                        courseDto.setCourseName(trainingCourse.getCourseName());
                        courseDto.setCourseType(trainingCourse.getCourseType());
                        courseDto.setAttachmentType(trainingCourse.getAttachmentType());
                        courseDto.setAttachmentFileName(trainingCourse.getAttachmentFileName());
                        courseDto.setArticleContent(trainingCourse.getArticleContent());
                        courseDto.setAttachmentUrl(trainingCourse.getAttachmentUrl());
                        courseDto.setCommentEnabled(trainingCourse.getCommentEnabled());
                        courseDto.setDeliveryPlatform(trainingCourse.getDeliveryPlatform());

                        var checkInEntity = new TrainingCampCheckInEntity();
                        checkInEntity.setCampId(trainingCampDto.getId());
                        checkInEntity.setTaskId(trainingCampTaskDto.getId());
                        checkInEntity.setCourseId(trainingCourse.getId());

                        checkInEntity.setUserId(userDto.getUserId());
                        int isCheckIn = trainingCampCheckInDao.isCheckIn(checkInEntity) > 0 ?1 :0 ;
                        courseDto.setIsCheckIn(isCheckIn);
                        courseDto.setComments(trainingCourse.getComments());
                        return courseDto;
                    }).collect(Collectors.toList());

            Map<Integer, Integer> indexMap = new HashMap<>();
            String[] al = trainingCampTaskDto.getCourseIds().split(",");
            for (int i = 0; i < al.length; i++) {
                indexMap.put(Integer.valueOf(al[i]), i);
            }

            // 根据 task 中的 courseIds 排序 courses 列表
            Comparator<TrainingCourseDto> comparator = Comparator.comparingInt(course -> indexMap.getOrDefault(course.getId(), -1));
            courses.sort(comparator);

            trainingCampTaskDto.setCourses(courses);
            //开营解锁
            if(trainingCampDto.getTaskUnlockType().equals(1)){
                trainingCampTaskDto.setIsLocked(trainingCampTaskDto.getStartDate().after(new Date())? 1 : 0);
                trainingCampTaskDto.setIsAllCourseCheckIn(checkPreviousTaskCompleted(trainingCampTaskDto,userDto.getUserId())? 1: 0);
            }
            //次日解锁（前一日完成打卡）
            if(trainingCampDto.getTaskUnlockType().equals(2)){
                // 判断是否到了任务时间
                boolean isLocked = trainingCampTaskDto.getTaskDate().before(new Date());
                if(isLocked){
                    trainingCampTaskDto.setIsLocked(1);
                }
                // 判断前一日任务是否全部完成
                boolean previousTasksCompleted = trainingCampDto.getTasks().stream()
                        .filter(prevTask -> prevTask.getTaskDate().before(trainingCampTaskDto.getTaskDate()))
                        .allMatch(prevTask -> checkPreviousTaskCompleted(prevTask, userDto.getUserId()));
                trainingCampTaskDto.setIsLocked(!isLocked && previousTasksCompleted? 1 : 0);
            }
            // 计算课程总数和已打卡课程数
            totalCourseCount += trainingCampTaskDto.getCourses().size();
            checkInCount += trainingCampTaskDto.getCourses().stream()
                    .filter(course -> course.getIsCheckIn().equals(1))
                    .count();
        }

        trainingCampCheckInDto.setCourseTotalCount(totalCourseCount);
        trainingCampCheckInDto.setCourseCheckInCount(checkInCount);
        trainingCampCheckInDto.setCheckInRate(((double)checkInCount/totalCourseCount)*100 + "%");
        trainingCampCheckInDto.setCampLeftTime(getCampLeftTime(trainingCampDto.getEndDate()));
        trainingCampCheckInDto.setTrainingCamp(trainingCampDto);
        return trainingCampCheckInDto;
    }

    /**
     *  获取训练营离结束剩余时间
     * @param endDate 结束时间
     * @return 剩余时间
     */
    private String getCampLeftTime(Date endDate){
        var campLeftTime = "";
        LocalDateTime endDateTime = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime now = LocalDateTime.now();
        // 检查是否已经超过了结束日期
        if (now.isAfter(endDateTime)) {
            campLeftTime =  "已结束";
        }
        LocalDate today = LocalDate.now();
        LocalDate endLocalDate = endDateTime.toLocalDate();

        long daysUntilEnd = ChronoUnit.DAYS.between(today, endLocalDate);

        if (daysUntilEnd > 0) {
            campLeftTime =  "距离结束日期还有 " + daysUntilEnd + " 天";
        } else {
            // 当天剩余时间
            Duration duration = Duration.between(now, endDateTime);
            long hours = duration.toHours();
            long minutes = duration.minusHours(hours).toMinutes();
            long seconds = duration.minusHours(hours).minusMinutes(minutes).getSeconds();

            campLeftTime =  "距离结束时间还有 " + hours + " 小时 " + minutes + " 分钟 " + seconds + " 秒";
        }
        return  campLeftTime;
    }

    /**
     *  验证前一天的任务是否都打卡完成
     * @param trainingCampTaskDto 训练营任务实体对象
     * @param userId 当前用户id
     * @return 是否都完成打卡
     */
    private boolean checkPreviousTaskCompleted(TrainingCampTaskDto trainingCampTaskDto, Integer userId) {
        List<TrainingCourseDto> courses = trainingCampTaskDto.getCourses();
        var trainingCampCheckInEntity  = new TrainingCampCheckInEntity();
        trainingCampCheckInEntity.setCampId(trainingCampTaskDto.getCampId());
        trainingCampCheckInEntity.setTaskId(trainingCampTaskDto.getId());
        trainingCampCheckInEntity.setUserId(userId);
        return courses.stream()
                .map(course -> trainingCampCheckInDao.getAllCheckInCourses(trainingCampCheckInEntity)
                        .stream()
                        .filter(checkIn -> checkIn.getCourseId().equals(course.getId()))
                        .findFirst())
                .allMatch(Optional::isPresent);
    }

    /**
     *  查询训练营开始和结束时间
     * @param campId 训练营id
     * @return map
     */
    @Override
    public HashMap<String, Date> getCampDate(Integer campId){
        return trainingCampDao.getCampDate(campId);
    }

    /**
     *  删除
     * @param id 训练营id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return trainingCampDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  验证训练营名称是否重复
     * @param campName 训练营名称
     * @return 是否存在
     */
    @Override
    public int isCampNameExist(String campName){
        return trainingCampDao.isExist(campName);
    }

    /**
     *  更新训练营任务信息
     * @param deliveryPlatform 内容源平台
     * @param taskUnlockType 任务解锁形式
     * @param campId 训练营id
     * @return 影响行数
     */
    @Override
    public int updateTaskInfo(Integer deliveryPlatform, Integer taskUnlockType,Integer campId){
        HashMap<String,Integer> map = new HashMap<>();
        map.put("deliveryPlatform",deliveryPlatform);
        map.put("taskUnlockType",taskUnlockType);
        map.put("campId",campId);
        return trainingCampDao.updateTaskInfo(map);
    }

    /**
     *  获取训练营任务信息
     * @param campId 训练营id
     * @return 任务实体对象
     */
    @Override
    public TrainingCampEntity getTaskInfo(Integer campId){
        return  trainingCampDao.getTaskInfo(campId);
    }

    /**
     *  更新训练营证书信息
     * @param entity 证书实体类对象
     * @return 影响行数
     */
    @Override
    public int updateCertInfo(TrainingCampCertEntity entity){
        return trainingCampDao.updateCertInfo(entity);
    }

    /**
     *  获取训练营证书信息
     * @param campId 训练营id
     * @return 证书实体对象
     */
    @Override
    public TrainingCampCertEntity getCertInfo(Integer campId){
        return trainingCampDao.getCertInfo(campId);
    }

    /**
     * 更新训练营创建完成状态
     * @param id 训练营id
     * @return 影响行数
     */
    @Override
    public int updateCampDone(@Param("id") Integer id){
        return trainingCampDao.updateCampDone(id);
    }

    /**
     *  导入训练营学员信息
     * @param users 学员集合
     */
    @Override
    public List<ImportCampUserDto> importUser(List<ImportCampUserDto> users){
        var listImportUsers = new ArrayList<ImportCampUserDto>();
        var trainingCampDto = trainingCampDao.get(Integer.valueOf(users.get(0).getCampId()));
        for (ImportCampUserDto dto: users){
            var importUser = new ImportCampUserDto();
            var trainingcampUser = new TrainingCampUserEntity();
            var userId = userDao.getUserIdByLoginName(dto.getLoginName());
            if(userId != null && userId != 0){ //用户存在则验证用户是否已报名训练营
                var map = new HashMap<String,Integer>();
                map.put("campId", Integer.valueOf(dto.getCampId()));
                map.put("userId",userId);
                if(trainingCampUserDao.isUserExists(map) > 0){ //已报名
                    importUser.setCampId(dto.getCampId());
                    importUser.setLoginName(dto.getLoginName());
                    importUser.setState(0);
                    importUser.setMsg("用户已经报名，不可重复报名");
                }
                else {
                    if(trainingCampDto.getUnlimitedNum() == 1 && trainingCampUserDao.getRegNum(Integer.valueOf(dto.getCampId())) > trainingCampDto.getMaxRegNum()){ //超过报名限制人数
                        importUser.setCampId(dto.getCampId());
                        importUser.setLoginName(dto.getLoginName());
                        importUser.setState(0);
                        importUser.setMsg("导入失败：超过限制人数");
                    }
                    else{
                        trainingcampUser.setUserId(userId);
                        trainingcampUser.setCampId(Integer.valueOf(dto.getCampId()));
                        boolean isSuccess =  trainingCampUserDao.addCampUser(trainingcampUser) > 0;
                        importUser.setCampId(dto.getCampId());
                        importUser.setLoginName(dto.getLoginName());
                        importUser.setState(isSuccess?1:0);
                        importUser.setMsg(isSuccess ? "导入成功" : "导入失败：系统异常");
                    }
                }
            }
            else{
                importUser.setCampId(dto.getCampId());
                importUser.setLoginName(dto.getLoginName());
                importUser.setState(0);
                importUser.setMsg("导入失败：用户不存在");
            }
            listImportUsers.add(importUser);
        }
        return  listImportUsers;
    }

    /**
     *  H5端：获取其他心理训练营
     * @return 训练营集合
     */
    @Override
    public List<TrainingCampDto> getCampListForIndex(){
        var userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
        var dto = new TrainingCampDto();
        dto.setUserId(userDto.getUserId());
        return trainingCampDao.getCampListForIndex(dto);
    }

    /**
     *  H5端：获取我的心理训练营
     * @return 训练营集合
     */
    @Override
    public List<TrainingCampDto> getMyCampList(){
        var userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
        return trainingCampDao.getMyCampList(userDto.getUserId());
    }

    /**
     *  H5端：获取其他心理训练营：分页
     * @param dto 查询条件
     * @return 训练营集合
     */
    @Override
    public BSDatatableRes<TrainingCampDto> getCampListForIndexByPaged(TrainingCampDto dto){
        var dtRes = new BSDatatableRes<TrainingCampDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listCamps = getCampListForIndex();
        var trainingCampDto = new PageInfo<>(listCamps);
        dtRes.setData(trainingCampDto.getList());
        dtRes.setRecordsTotal((int) trainingCampDto.getTotal());
        dtRes.setRecordsFiltered((int) trainingCampDto.getTotal());
        return dtRes;
    }

    /***
     *  验证当前登录用户是否已报名训练营
     * @param campId 训练营id
     * @return 是否报名标识
     */
    @Override
    public boolean isUserInCamp(Integer campId){
        var userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
        HashMap<String,Integer> map = new HashMap<>();
        map.put("userId", userDto.getUserId());
        map.put("campId",campId);
        return trainingCampUserDao.isUserExists(map) > 0;
    }

    /**
     *  验证训练营是否满员
     * @param campId 训练营id
     * @return 是否满员标识
     */
    @Override
    public boolean isCampFull(Integer campId){
        var trainingCampDto = trainingCampDao.get(campId);
        return (trainingCampDto.getUnlimitedNum() == 1) && (trainingCampUserDao.getRegNum(campId) > trainingCampDto.getMaxRegNum());
    }

    /**
     *  判断训练营的调查问卷是否完成
     * @param campId 训练营id
     * @return 是否完成
     */
    @Override
    public boolean isSurveyDone(Integer userId,Integer campId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("campId",campId);
        return trainingCampDao.isSurveyDone(map) == 1 ;
    }

    /**
     *  发送训练营开营提醒
     * @param dto 训练营对象
     * @return 是否成功
     */
    @Override
    public boolean sendTrainingCampStartNotify(TrainingCampDto dto){
        boolean isSuccess = false;
        if(dto.getUsers() != null && dto.getUsers().size()> 0){
            for(TrainingCampUserDto user: dto.getUsers()){
                var phoneNumber = user.getMobile();
                if(StrUtil.isNotEmpty(phoneNumber)){
                    var sysConfig = sysConfigService.get();
                    SendSmsDto sendSmsDto = new SendSmsDto();
                    sendSmsDto.setPhoneNumber(phoneNumber);
                    sendSmsDto.setSignName(sysConfig.getSmsConfig().getSignName());
                    sendSmsDto.setTemplateCode(sysConfig.getSmsConfig().getTrainingCampStartTemplate());
                    sendSmsDto.setSmsContent("{ \"campName\":\"" + dto.getCampName() + "\"}");
                    sendSmsDto.setSmsType(2);
                    sendSmsService.sendNotify(sendSmsDto);
                    isSuccess = true;
                }
            }
        }
        return isSuccess;
    }

    /**
     *  导出调查问卷结果数据
     * @param dto 条件
     * @return 集合
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportSurveyResutl(ExportTrainingCampSurveyRecordDto dto){
        List<LinkedHashMap<String,Object>> questions =surveyQuestionDao.getListForExport(dto.getSurveyId() == null ? 0 : dto.getSurveyId());
        StringBuilder questionBuilder = new StringBuilder();
        for(LinkedHashMap<String,Object> questionMap: questions){
            questionBuilder.append(String.format("max(case when psq.q_number ='%s' then psr.item_id else 0 end) as 第%s题,",questionMap.get("q_number"),questionMap.get("q_number")));
        }
        String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
        dto.setSql(qNoStr);
        return trainingCampDao.getExportSurveyTestResult(dto);
    }
}
