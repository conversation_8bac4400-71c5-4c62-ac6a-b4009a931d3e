package cn.psycloud.psyplatform.serviceImpl.trainingcamp;

import cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampTaskDao;
import cn.psycloud.psyplatform.dto.trainingcamp.TrainingCampTaskDto;
import cn.psycloud.psyplatform.dto.trainingcamp.TrainingCourseDto;
import cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampTaskEntity;
import cn.psycloud.psyplatform.service.trainingcamp.TrainingCampTaskService;
import lombok.var;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class TrainingCampTaskServiceImpl implements TrainingCampTaskService {
    @Autowired
    private TrainingCampTaskDao trainingCampTaskDao;
    /**
     *  添加任务
     * @param entity 实体类对象
     * @return 影响行数
     */
    @Override
    public int add(TrainingCampTaskEntity entity){
        return trainingCampTaskDao.addCampTask(entity);
    }

    /**
     *  修改任务
     * @param entity 实体类对象
     * @return 影响行数
     */
    @Override
    public int update(TrainingCampTaskEntity entity){
        return trainingCampTaskDao.updateCampTask(entity);
    }

    /**
     *  删除任务
     * @param id 任务Id
     * @return 影响行数
     */
    @Override
    public int delete(@Param("id") Integer id){
        return trainingCampTaskDao.delCampTask(id);
    }

    /**
     *  获取训练营任务集合
     * @param campId 训练营id
     * @return 集合
     */
    public List<TrainingCampTaskDto> getList(Integer campId){
        var trainingCampTasks = new ArrayList<TrainingCampTaskDto>();
        var tasks = trainingCampTaskDao.getList(campId);
        for(TrainingCampTaskDto trainingCampTaskDto : tasks){
            Map<Integer, Integer> indexMap = new HashMap<>();
            String[] al = trainingCampTaskDto.getCourseIds().split(",");
            for (int i = 0; i < al.length; i++) {
                indexMap.put(Integer.valueOf(al[i]), i);
            }
            Comparator<TrainingCourseDto> comparator = Comparator.comparingInt(course -> indexMap.getOrDefault(course.getId(), -1));
            trainingCampTaskDto.getCourses().sort(comparator);
            trainingCampTasks.add(trainingCampTaskDto);
        }
        return trainingCampTasks;
    }

    /**
     *  验证当天是否存在任务
     * @param taskDate 任务日期
     * @param campId 训练营id
     * @return 数量
     */
    @Override
    public boolean isTaskDateExists(String taskDate, Integer campId){
        var map = new HashMap<String,Object>();
        map.put("taskDate",taskDate);
        map.put("campId",campId);
        return trainingCampTaskDao.isTaskDateExists(map) > 0;
    }
}
