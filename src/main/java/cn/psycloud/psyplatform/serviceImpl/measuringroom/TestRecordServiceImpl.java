package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import cn.psycloud.psyplatform.util.Base64Util;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.POIWordHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import com.deepoove.poi.data.PictureRenderData;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.hutool.core.date.DateUtil.ageOfNow;

@Service
public class TestRecordServiceImpl implements TestRecordService {
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private ScaleDao scaleDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private TestScoreService testScoreService;
    @Autowired
    private POIWordHelper poiWordHelper;
    @Value("${file.location}")
    String uploadPath;

    /**
     *  根据记录ID查询测评记录
     * @param recordId 记录id
     * @return 测评记录实体对象
     */
    @Override
    public TestRecordDto getById(Integer recordId) {
        var testRecord = testRecordDao.getById(recordId);
        var scale = scaleDao.getById(testRecord.getScaleId());
        List<ScaleQuestionEntity> listQuestions = scaleQuestionDao.getListByScaleIdForTestIng(scale.getId());
        scale.setListQuestions(listQuestions);
        var userDto = new UserDto();
        userDto.setUserId(testRecord.getUser().getUserId());
        var user = userDao.get(userDto);
        testRecord.setScale(scale);
        testRecord.setUser(user);
        return  testRecord;
    }

    /**
     *  查询测评记录集合：分页
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public BSDatatableRes<TestRecordDto> getListByPaged(TestRecordDto dto) {
        var dtRes = new BSDatatableRes<TestRecordDto>();
        if(dto.getStructId() != 0){
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getList(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     * 根据条件查询测评记录集合
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public List<TestRecordDto> getList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getList(dto);
    }

    /**
     *  获取我的测评记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    public BSDatatableRes<TestRecordDto> getMyRecords(TestRecordDto dto){
        var dtRes = new BSDatatableRes<TestRecordDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getMyRecords(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     *  保存测评记录
     * @param entity 测评记录实体对象
     * @return 测评记录id
     */
    @Override
    public int addRecord(TestRecordEntity entity) {
        entity.setId(0);
        testRecordDao.addTestRecord(entity);
        return  entity.getId();
    }

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return testRecordDao.deleteByRecordId(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  更新测评状态
     * @param recordId 记录id
     * @param state 测评状态：0-未完成 1-已完成 2-测谎未通过
     * @return 影响行数
     */
    @Override
    public int updateTestState(Integer recordId, Integer state) {
        var map = new HashMap<String, Integer>();
        map.put("recordId",recordId);
        map.put("state",state);
        return testRecordDao.updateTestState(map);
    }

    /**
     *  更新测评开始时间
     * @param recordId 记录id
     * @return 影响行数
     */
    @Override
    public int updateTestStartTime(Integer recordId) {
        var map = new HashMap<String, Object>();
        map.put("recordId",recordId);
        map.put("startTime",new Date());
        return testRecordDao.updateStartTime(map);
    }

    /**
     *  验证个人信息是否符合量表要求
     * @param dto 测评记录实体对象
     * @return 是否符合
     */
    @Override
    public boolean isUserInfoComplete(TestRecordDto dto) {
        boolean check = false;
        var limit = dto.getScale().getTestLimit();
        if (limit == null || "".equals(limit) ) {
            check = true;
        }
        else {
            var userDto = new UserDto();
            userDto.setUserId(dto.getUser().getUserId());
            var user = userDao.get(userDto);
            if (limit.contains("出生年月")) {
                if (user.getBirth() != null && user.getBirth() != "") {
                    String[] arrayAgeLimit = dto.getScale().getAgeLimit().split(",");
                    if ("0".equals(arrayAgeLimit[0]) && "0".equals(arrayAgeLimit[1]))
                        check = true;
                    else {
                        var age = ageOfNow(user.getBirth());
                        if (age >= Integer.parseInt(arrayAgeLimit[0]) && age <= Integer.parseInt(arrayAgeLimit[1])) {
                            check = true;
                        }
                    }
                }
            }
            if (limit.contains("性别")) check = user.getSex() != null && !"".equals(user.getSex());
            if (limit.contains("姓名")) check = user.getRealName() != null && !"".equals(user.getRealName());
        }
        return check;
    }

    /**
     *  判断是否异常
     * @param recordId 记录id
     * @return 异常标识
     */
    @Override
    public int isAbnormal(Integer recordId) {
        return testRecordDao.isAbnormal(recordId);
    }

    /**
     *  查询九型人格测评记录集合：分页
     * @param dto 九型人格测评实体对象
     * @return 记录集合
     */
    @Override
    public BSDatatableRes<NineHouseStatDto> getNineHouseList(NineHouseStatDto dto) {
        var dtRes = new BSDatatableRes<NineHouseStatDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<NineHouseStatDto> listRecords = testRecordDao.getNineHouseList(dto);
        PageInfo<NineHouseStatDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return  dtRes;
    }

    /**
     *  保存测评结果解释
     * @param entity 测评结果解释实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordExplain(TestRecordExplainEntity entity){
        return testRecordDao.saveTestRecordExplain(entity) > 0 ;
    }

    /**
     *  获取报告图表
     * @param recordId 测评记录id
     * @return 测评报告图表集合
     */
    public List<TestRecordChartsEntity> getReportCharts(Integer recordId) {
        return testRecordDao.getReportCharts(recordId);
    }

    /**
     *  保存测评报告里的图表
     * @param dto 测评报告图表实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordCharts(TestRecordChartsDto dto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(dto.getRecordId());
        if(dto.getChartsImg() != null) {
            for(String chartImg: dto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                Base64Util.generateImage(chartImg.replace("data:image/png;base64,",""),uploadPath+"/charts/"+fileName);
                var chartsEntity = new TestRecordChartsEntity();
                chartsEntity.setId(0);
                chartsEntity.setRecordId(dto.getRecordId());
                chartsEntity.setChartsImg(fileName);
                isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
            }
        }
        return isSuccess;
    }

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    @Override
    public List<ExportTestRecordDto> getExportTestRecordList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getExportTestRecordList(dto);
    }

    /**
     *  测评报告导出word
     * @param response 响应
     * @param request 请求
     * @param recordId 测评记录Id
     * @return 文件名
     */
    @Override
    public String exportTestReportToWord(HttpServletResponse response, HttpServletRequest request, Integer recordId, String folderName){
        String filePath = "";
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listTestScores = testScoreService.getTestScoreList(testScoreDto);
        if(listTestScores != null && listTestScores.size() > 0) {
            Map<String, Object> reportMap = new HashMap<>();
            var listFactorScoreMap = new ArrayList<HashMap<String, Object>>();
            for (TestScoreDto dto : listTestScores) {
                var map = new HashMap<String, Object>();
                map.put("factorNo", dto.getFactor().getFactorNo());
                map.put("factorName", dto.getFactor().getFactorName());
                var factorType = dto.getFactor().getFactorType() == 1 ? "普通因子" : "复合因子";
                map.put("factorType",factorType);
                map.put("originalScore", dto.getOriginalScore());
                map.put("score", dto.getScore());
                map.put("scoreAbnormalCondition", dto.getScoreAbnormalCondition());
                map.put("abnormalValue", dto.getAbnormalValue());
                if (dto.getTestRecord().getScale().getId() == ScaleIDDto.SDS) {
                    map.put("indexScore", String.format("%.2f", dto.getOriginalScore().divide(BigDecimal.valueOf(80),2, RoundingMode.HALF_UP)));
                }
                listFactorScoreMap.add(map);
            }
            reportMap.put("scores", listFactorScoreMap);

            reportMap.put("scaleName", listTestScores.get(0).getTestRecord().getScale().getScaleName());
            reportMap.put("loginName", listTestScores.get(0).getTestRecord().getUser().getLoginName());
            reportMap.put("realName", listTestScores.get(0).getTestRecord().getUser().getRealName());
            reportMap.put("structFullName", listTestScores.get(0).getTestRecord().getUser().getStructFullName());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            var startTime = sdf.format(listTestScores.get(0).getTestRecord().getStartTime());
            reportMap.put("startTime", startTime);
            reportMap.put("timeInterval", DateUtil.secondToTime(listTestScores.get(0).getTestRecord().getTimeInterval()));
            //获取测评报告图表
            var listTestRecordCharts = getReportCharts(recordId);
            int i = 1;
            for (TestRecordChartsEntity testRecordChartsEntity : listTestRecordCharts) {
                var chartImg = testRecordChartsEntity.getChartsImg() == null ? CommonHelper.getResourcesFilePath("/static/images/default-charts.jpg") : uploadPath + "charts/" + testRecordChartsEntity.getChartsImg();
                reportMap.put("chartImg" + i, new PictureRenderData(500, 330, chartImg));
                i++;
            }
            var interpretation = "<div style=\"font-family: 等线\">" + listTestScores.get(0).getTestRecord().getInterpretation() + "</div>";
            reportMap.put("interpretation", interpretation);
            String templatePath;
            if (listTestScores.get(0).getTestRecord().getScale().getId() == ScaleIDDto.SDS) {
                templatePath = CommonHelper.getResourcesFilePath("static/template/sds_report.docx");
            }
            else {
                if(listTestScores.size() == 1){
                    templatePath = CommonHelper.getResourcesFilePath("static/template/report_single.docx");
                }
                else{
                    templatePath = CommonHelper.getResourcesFilePath("static/template/report.docx");
                }
            }
            String fileName = listTestScores.get(0).getRecordId() + "_" + listTestScores.get(0).getTestRecord().getUser().getRealName() + "_" + listTestScores.get(0).getTestRecord().getScale().getScaleName() + ".docx";
            filePath = String.format("report/%s/%s",folderName, fileName);
            String fileAbPath=String.format(  uploadPath +"report/%s/%s", folderName, fileName);
            poiWordHelper.downloadWord(response, request, templatePath, fileAbPath, reportMap);
        }
        return  filePath;
    }

    /**
     *  批量导出测评报告
     * @param response 响应
     * @param request 请求
     * @param dto 查询条件
     * @return 文件路径
     */
    @Override
    public String batchExportReport(HttpServletResponse response, HttpServletRequest request, TestRecordDto dto) {
        String result = "";
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        var recordIds = testRecordDao.getRecordIdsForBatchExport(dto);
        if(!recordIds.isEmpty()) {
            result = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            for(Map<String,Integer> map: recordIds){
                exportTestReportToWord(response,request,map.get("id"), result);
            }
        }
        return  result;
    }
}
