package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.CheckAbnormalDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.stream.Collectors;

@Service
public class SDS extends BaseCalc{
    @Override
    public void compute() {
        var totalScore = computeTotalScore();
        //标准分
        BigDecimal standard_val = totalScore.multiply(BigDecimal.valueOf(1.25));
        //抑郁指数
        BigDecimal index = totalScore.divide(BigDecimal.valueOf(80),2, RoundingMode.HALF_UP);
        //结果解释
        var responseText = new StringBuilder();
        var listFactorExplains = scaleFactorExplainDao.getListById(getTotalScoreFactorId());
        for(ScaleFactorExplainEntity scaleFactorExplainEntity: listFactorExplains) {
            if (index.compareTo(scaleFactorExplainEntity.getStartValue()) >= 0 && index.compareTo(scaleFactorExplainEntity.getEndValue()) <= 0){
                var listAbnormalConditions = scaleFactorAbnormalConditionDao.getListByFactorId(scaleFactorExplainEntity.getFactorId())
                        .stream()
                        .sorted(Comparator.comparing(ScaleFactorAbnormalConditionEntity::getScoreValue).reversed())
                        .collect(Collectors.toList());
                //判断是否正常
                BigDecimal conditionValue = BigDecimal.valueOf(0);
                CheckAbnormalDto checkAbnormalDto = new CheckAbnormalDto();
                checkAbnormalDto.setFactorId(scaleFactorExplainEntity.getFactorId());
                checkAbnormalDto.setAge(age);
                checkAbnormalDto.setSex(sex);
                checkAbnormalDto.setFactorScore(index);
                checkAbnormalDto.setConditionValue(conditionValue);
                checkAbnormalDto.setListAbnormalConditions(listAbnormalConditions);
                isAbnormal = checkIsAbnormal(checkAbnormalDto);

                var testScoreEntity = new TestScoreEntity();
                testScoreEntity.setRecordId(recordId);
                testScoreEntity.setFactorId(scaleFactorExplainEntity.getFactorId());
                testScoreEntity.setOriginalScore(totalScore);
                testScoreEntity.setScore(standard_val);
                testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
                testScoreEntity.setAbnormalValue(checkAbnormalDto.getConditionValue());
                saveScore(testScoreEntity);

                var testRecordExplain = new TestRecordExplainEntity();
                testRecordExplain.setRecordId(recordId);
                testRecordExplain.setFactorId(scaleFactorExplainEntity.getFactorId());
                testRecordExplain.setInterpretation(String.format("标准分：%.2f；<br/>抑郁指数：%.2f%s。%s",
                        standard_val,
                        index,
                        "(isAbnormal == true ? \"异常\" : \"正常\")",
                        scaleFactorExplainEntity.getInterpretation()
                        ));
                testRecordDao.saveTestRecordExplain(testRecordExplain);

                responseText.append(String.format("<dt></dt><dd>标准分：%.2f；<br/>抑郁指数：%.2f(%s)<dd/><dd>%s<dd>",
                        standard_val,
                        index,
                        isAbnormal ? "<span class=\"text-danger\">异常</span>" : "正常",
                        scaleFactorExplainEntity.getInterpretation()));
            }
        }
        interpretation = responseText.toString();
    }
}
