package cn.psycloud.psyplatform.serviceImpl.datasync;

import cn.psycloud.psyplatform.dao.anteroom.StructsDao;
import cn.psycloud.psyplatform.dao.datasync.SyncUserDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.datasync.SyncUserEntity;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import cn.psycloud.psyplatform.service.datasync.SyncUserService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class SyncUserServiceImpl implements SyncUserService {
    @Autowired
    private SyncUserDao syncUserDao;
    @Autowired
    private UserService userService;
    @Autowired
    private StructsDao structsDao;
    @Value("${platform.scheduleconfig}")
    private boolean isScheduleSuEnabled;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean syncUsers(){
        var isSuccess = false;
        if(isScheduleSuEnabled){
            try{
                var users = syncUserDao.getSyncUsers();
                for(SyncUserEntity user: users){
                    if(userService.isSyncUserExists(user.getMobile()) == 0 ){
                        var userDto = new UserDto();
                        userDto.setLoginName(user.getMobile());
                        userDto.setRealName(user.getRealName());
                        userDto.setMobile(user.getMobile());
                        userDto.setIsMobileBind(1);
                        userDto.setSex(user.getSex());
                        var orgNo = user.getOrgNo();
                        var structId = structsDao.getStructIdByOrgCode(orgNo);
                        userDto.setStructId(structId);
                        userDto.setIsChecked(1);
                        userDto.setRoleId(3);
                        userDto.setSyncUserId(user.getUserId());
                        userService.addUser(userDto);
                    }
                    else{
                        var userDto = new UserDto();
                        userDto.setSyncUserId(user.getUserId());

                        var orgNo = user.getOrgNo();
                        var structId = structsDao.getStructIdByOrgCode(orgNo);
                        userDto.setStructId(structId);
                        userService.updateUserInfoBySyncUserId(userDto);
                    }
                }
                isSuccess = true;
            }
            catch (Exception e){
                log.error("同步用户数据时发生错误：{}",e.getMessage());
            }
        }
        return isSuccess;
    }
}
