package cn.psycloud.psyplatform.serviceImpl.datasync;

import cn.psycloud.psyplatform.dao.datasync.SyncTestRecordDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.entity.datasync.SyncTestRecordEntity;
import cn.psycloud.psyplatform.entity.datasync.SyncTestRecordExplainEntity;
import cn.psycloud.psyplatform.service.datasync.SyncTestRecordService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class SyncTestRecordServiceImpl implements SyncTestRecordService {
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private SyncTestRecordDao syncTestRecordDao;
    @Value("${platform.scheduleconfig}")
    private boolean isScheduleSuEnabled;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean syncTestRecord(){
        boolean isSuccess = false;
        if(isScheduleSuEnabled){
            var testRecords = testRecordDao.getSyncTestRecords();
            try{
                for(SyncTestRecordEntity testRecordEntity: testRecords){
                    var recordId = testRecordEntity.getRecordId();
                    if(syncTestRecordDao.isTestRecordExists(recordId) == 0){
                        syncTestRecordDao.addSyncTestRecord(testRecordEntity);
                        var testRecordExplains = testRecordDao.getSyncTestRecordExplains(recordId);
                        if(testRecordExplains.size() > 0){
                            for(SyncTestRecordExplainEntity syncTestRecordExplainEntity: testRecordExplains){
                                syncTestRecordDao.addSyncTestRecordExplain(syncTestRecordExplainEntity);
                            }
                        }
                    }
                    else{
                        if(testRecordEntity.getState() == 2){
                            syncTestRecordDao.updateSyncTestRecordState(testRecordEntity.getRecordId());
                        }
                    }
                }
                isSuccess = true;
            }
            catch (Exception e){
                log.error("推送测评数据发生错误：{}",e.getMessage());
            }
        }
        return isSuccess;
    }
}
