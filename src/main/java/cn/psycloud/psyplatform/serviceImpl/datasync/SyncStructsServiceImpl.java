package cn.psycloud.psyplatform.serviceImpl.datasync;

import cn.hutool.core.util.ObjectUtil;
import cn.psycloud.psyplatform.dao.anteroom.StructsDao;
import cn.psycloud.psyplatform.dao.datasync.SyncStructsDao;
import cn.psycloud.psyplatform.entity.anteroom.StructsEntity;
import cn.psycloud.psyplatform.service.datasync.SyncStructsService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.dom4j.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class SyncStructsServiceImpl implements SyncStructsService {
    @Autowired
    private SyncStructsDao syncStructsDao;
    @Autowired
    private StructsDao structsDao;
    @Autowired
    private SysConfigService sysConfigService;
    @Value("${platform.scheduleconfig}")
    private boolean isScheduleSuEnabled;

    /**
     *  添加顶级组织
     * @return 组织id
     */
    private int addTopStruct(){
        var structEntity = new StructsEntity();
        structEntity.setId(0);
        var sysConfig = sysConfigService.get();
        structEntity.setStructName(sysConfig.getPlatformName());
        structEntity.setParentId(0);
        structEntity.setIsValid(1);
        structsDao.add(structEntity);
        return structEntity.getId();
    }

    /*
    *  同步组织
    * */
    /*@Transactional(rollbackFor = Exception.class)
    @Override
    public boolean syncStructs(){
        boolean isSuccess = false;
        if(isScheduleSuEnabled){
            try {
                //先清空组织
                structsDao.truncateStructs();
                //添加顶级组织
                var topParentId = addTopStruct();
                var schools = syncStructsDao.getSchools();
                for (String schoolName : schools) {
                    var structEntity = new StructsEntity();
                    structEntity.setId(0);
                    structEntity.setStructName(schoolName);
                    structEntity.setParentId(topParentId);
                    structEntity.setIsValid(1);
                    structsDao.add(structEntity);
                    var schoolId = structEntity.getId();
                    var joinYears = syncStructsDao.getYearBySchool(schoolName);
                    for (String joinYear : joinYears) {
                        structEntity = new StructsEntity();
                        structEntity.setId(0);
                        structEntity.setStructName(joinYear);
                        structEntity.setParentId(schoolId);
                        structEntity.setIsValid(1);
                        structsDao.add(structEntity);
                        var yearId = structEntity.getId();
                        List<Map<String, Object>> orgMaps = syncStructsDao.getOrgByYearAndSchool(joinYear, schoolName);
                        for (Map<String, Object> orgMap : orgMaps) {
                            var orgNo = orgMap.get("org_no");
                            var orgName = orgMap.get("org_name");
                            structEntity = new StructsEntity();
                            structEntity.setId(0);
                            structEntity.setStructName(orgName.toString());
                            structEntity.setOrgCode(orgNo.toString());
                            structEntity.setParentId(yearId);
                            structEntity.setIsValid(1);
                            structsDao.add(structEntity);
                        }
                    }
                }
                isSuccess = true;
            }
            catch (Exception e){
                log.error("同步组织发生异常：{}",e.getMessage());
            }
        }
        return  isSuccess;
    }*/

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean syncStructs(){
        boolean isSuccess = false;
        if(isScheduleSuEnabled){
            try{
                var schools = syncStructsDao.getSchools();
                for(String schoolName : schools){
                    var schoolMap = new HashMap<String,Object>();
                    schoolMap.put("parentId",1);
                    schoolMap.put("structName",schoolName);
                    var schoolId= structsDao.getStructIdByStructName(schoolMap);
                    var structEntity = new StructsEntity();
                    if(schoolId==null || schoolId==0){
                        structEntity.setId(0);
                        structEntity.setStructName(schoolName);
                        structEntity.setParentId(1);
                        structEntity.setIsValid(1);
                        structsDao.add(structEntity);
                        schoolId = structEntity.getId();
                    }
                    var joinYears = syncStructsDao.getYearBySchool(schoolName);
                    for (String joinYear : joinYears){
                        var yearMap = new HashMap<String,Object>();
                        yearMap.put("parentId",schoolId);
                        yearMap.put("structName",joinYear);
                        var yearId = structsDao.getStructIdByStructName(yearMap);
                        if(yearId== null || yearId==0){
                            structEntity = new StructsEntity();
                            structEntity.setId(0);
                            structEntity.setStructName(joinYear);
                            structEntity.setParentId(schoolId);
                            structEntity.setIsValid(1);
                            structsDao.add(structEntity);
                            yearId = structEntity.getId();
                        }
                        List<Map<String, Object>> orgMaps = syncStructsDao.getOrgByYearAndSchool(joinYear, schoolName);
                        for (Map<String, Object> orgMap : orgMaps){
                            var orgNo = orgMap.get("org_no");
                            var orgName = orgMap.get("org_name");
                            var structId = structsDao.getStructIdByOrgCode(String.valueOf(orgNo));
                            if(structId == null || structId == 0){
                                structEntity = new StructsEntity();
                                structEntity.setId(0);
                                structEntity.setStructName(orgName.toString());
                                structEntity.setOrgCode(orgNo.toString());
                                structEntity.setParentId(yearId);
                                structEntity.setIsValid(1);
                                structsDao.add(structEntity);
                            }
                        }
                    }
                }
                isSuccess = true;
            }
            catch (Exception e){
                log.error("同步组织发生异常：{}",e.getMessage());
            }
        }
        return isSuccess;
    }
}
