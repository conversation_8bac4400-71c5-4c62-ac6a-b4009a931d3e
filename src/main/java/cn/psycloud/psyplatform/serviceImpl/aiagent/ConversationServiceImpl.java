package cn.psycloud.psyplatform.serviceImpl.aiagent;

import cn.psycloud.psyplatform.dao.aiagent.ConversationDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.aiagent.conversation.ConversationDto;
import cn.psycloud.psyplatform.entity.aiagent.conversation.ConversationEntity;
import cn.psycloud.psyplatform.service.aiagent.ConversationService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ConversationServiceImpl implements ConversationService {
    @Autowired
    private ConversationDao conversationDao;

    /**
     *  添加会话
     * @param entity 会话实体对象
     * @return 影响行数
     */
    @Override
    public int addConversation(ConversationEntity entity){
        var user = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setUserId(user.getUserId());
        entity.setChatDate(new Date());
        return conversationDao.addConversation(entity);
    }

    /**
     *  查询会话集合
     * @param dto 查询条件
     * @return 会话集合
     */
    @Override
    public List<ConversationDto> getList(ConversationDto dto){
        return conversationDao.getList(dto);
    }

    /**
     *  查询会话集合：分页
     * @param dto 查询条件
     * @return 会话集合
     */
    @Override
    public BSDatatableRes<ConversationDto> getListByPaged(ConversationDto dto){
        var dtRes = new BSDatatableRes<ConversationDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listConversations = conversationDao.getList(dto);
        var conversationDto = new PageInfo<>(listConversations);
        dtRes.setData(conversationDto.getList());
        dtRes.setRecordsTotal((int) conversationDto.getTotal());
        dtRes.setRecordsFiltered((int) conversationDto.getTotal());
        return dtRes;
    }

    /**
     * 根据会话ID获取完整对话内容
     * @param conversationId 会话ID
     * @return 对话记录列表
     */
    @Override
    public List<ConversationDto> getConversationByConversationId(String conversationId) {
        ConversationDto dto = new ConversationDto();
        dto.setConversationId(conversationId);
        return conversationDao.getList(dto);
    }
}
