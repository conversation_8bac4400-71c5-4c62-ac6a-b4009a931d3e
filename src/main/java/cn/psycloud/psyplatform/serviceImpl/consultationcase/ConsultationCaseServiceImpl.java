package cn.psycloud.psyplatform.serviceImpl.consultationcase;

import cn.psycloud.psyplatform.dao.consultationcase.ConsultationCaseDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationCaseService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;

@Service
public class ConsultationCaseServiceImpl implements ConsultationCaseService {
    @Autowired
    private ConsultationCaseDao consultationCaseDao;

    /**
     * 新增个案
     * @param entity 个案实体
     * @return 影响行数
     */
    @Override
    public int add(ConsultationCaseEntity entity){
        var userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setCounselor(userDto.getUserId());
        entity.setCreateTime(new Date());
        return consultationCaseDao.add(entity);
    }

    /**
     * 更新个案
     * @param entity 个案实体
     * @return 影响行数
     */
    @Override
    public int update(ConsultationCaseEntity entity){
        return consultationCaseDao.update(entity);
    }

    /**
     * 删除个案
     * @param id 个案ID
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return consultationCaseDao.delete(id);
    }

    /**
     * 批量删除个案
     * @param ids 个案ID集合
     * @return 影响行数
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for(var id : arrayIds){
            isSuccess = consultationCaseDao.delete(Integer.parseInt(id)) > 0;
        }
        return isSuccess;
    }

    /**
     * 根据条件查询个案列表
     * @param dto 个案查询条件
     * @return 个案集合
     */
    @Override
    public BSDatatableRes<ConsultationCaseDto> getListByPaged(ConsultationCaseDto dto){
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        var roleId = userDto.getRole().getRoleId();
        if (!Arrays.asList(1, 3, 4).contains(roleId)) {
            dto.setCounselor(userDto.getUserId());
        }
        if (roleId == 3 || roleId == 4) {
            dto.setCounselor(-1);
        }
        var dtRes = new BSDatatableRes<ConsultationCaseDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listConsultationCase = consultationCaseDao.getList(dto);
        var consultationCaseDto = new PageInfo<>(listConsultationCase);
        dtRes.setData(listConsultationCase);
        dtRes.setRecordsFiltered((int)consultationCaseDto.getTotal());
        dtRes.setRecordsTotal((int)consultationCaseDto.getTotal());
        return dtRes;
    }

    /**
     * 根据ID查询个案
     * @param id 个案ID
     * @return 个案实体
     */
    @Override
    public ConsultationCaseDto getById(Integer id){
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        var roleId = userDto.getRole().getRoleId();
        var map = new HashMap<String, Integer>();
        if (!Arrays.asList(1, 3, 4).contains(roleId)) {
            map.put("id", id);
            map.put("counselor", userDto.getUserId());
        }
        else if (roleId == 1) {
            map.put("id", id);
        }
        else {
            map.put("id", -1);
        }
        return consultationCaseDao.getById(map);
    }
}
