package cn.psycloud.psyplatform.serviceImpl.survey;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dao.survey.SurveyRecordDao;
import cn.psycloud.psyplatform.dto.activityroom.ActivitySurveyRecordDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto;
import cn.psycloud.psyplatform.dto.survey.ExportSurveyRecordDto;
import cn.psycloud.psyplatform.dto.trainingcamp.TrainingCampSurveyRecordDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivitySurveyEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyResultEntity;
import cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampSurveyEntity;
import cn.psycloud.psyplatform.service.survey.SurveyRecordService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
public class SurveyRecordServiceImpl implements SurveyRecordService {
    @Autowired
    private SurveyRecordDao surveyRecordDao;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;

    /**
     *  删除测评任务下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param taskId 任务id
     * @return 影响行数
     */
    @Override
    public int delRecordByTaskId(Integer userId, Integer taskId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("taskId",taskId);
        return surveyRecordDao.delRecordByTaskId(map);
    }

    /**
     *  删除问卷调查任务下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param taskId 任务id
     * @return 影响行数
     */
    @Override
    public int delRecordOfTaskSurveyByTaskId(Integer userId, Integer taskId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("taskId",taskId);
        return surveyRecordDao.delRecordOfTaskSurveyByTaskId(map);
    }

    /**
     *  删除训练营下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param campId 训练营id
     * @return 影响行数
     */
    @Override
    public int delRecordByCampId(Integer userId, Integer campId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("campId",campId);
        return surveyRecordDao.delRecordByCampId(map);
    }

    /**
     *  删除活动下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param activityId 活动id
     * @param surveyId 问卷id
     * @return 影响行数
     */
    @Override
    public int delRecordByActivityId(Integer userId, Integer activityId, Integer surveyId){
        HashMap<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("activityId",activityId);
        map.put("surveyId",surveyId);
        return surveyRecordDao.delRecordByActivityId(map);
    }

    /**
     *  添加问卷作答结果
     * @param listAnswers 结果选项集合
     * @param recordId 记录id
     */
    private void addSurveyResult(List<SurveyResultEntity> listAnswers,Integer recordId){
        for (SurveyResultEntity entity: listAnswers){
            entity.setRecordId(recordId);
            surveyRecordDao.addResult(entity);
        }
    }

    /**
     *  关联测评任务和问卷记录
     * @param taskId 任务Id
     * @param surveyRecordId 问卷记录id
     * @return 影响行数
     */
    public int addTaskSurvey(Integer taskId, Integer surveyRecordId){
        var map = new HashMap<String,Integer>();
        map.put("taskId",taskId);
        map.put("surveyRecordId",surveyRecordId);
        return surveyRecordDao.addTaskSurvey(map);
    }

    /**
     *  关联训练营和问卷记录
     * @param campId 训练营Id
     * @param surveyRecordId 问卷记录id
     * @return 影响行数
     */
    private int addTrainingCampSurvey(Integer campId, Integer surveyRecordId){
        var map = new HashMap<String,Integer>();
        map.put("campId",campId);
        map.put("surveyRecordId",surveyRecordId);
        return surveyRecordDao.addTrainingCampSurvey(map);
    }

    /**
     *  关联活动和问卷记录
     * @param activityId 活动Id
     * @param surveyRecordId 问卷记录id
     * @return 影响行数
     */
    private int addActivitySurvey(Integer activityId, Integer surveyRecordId){
        var map = new HashMap<String,Integer>();
        map.put("activityId",activityId);
        map.put("surveyRecordId",surveyRecordId);
        return surveyRecordDao.addActivitySurvey(map);
    }

    /**
     *  添加问卷作答记录：测评任务
     * @param entity 实体对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addRecord(TaskSurveyRecordEntity entity){
        try {
            //先删除未完成作答记录
            delRecordByTaskId(entity.getUserId(), entity.getTaskId());
            //添加作答记录
            entity.setId(0);
            entity.setRecordDate(new Date());
            surveyRecordDao.addRecord(entity);
            var recordId = entity.getId();
            //先删除未完成的作答结果
            surveyRecordDao.deleteResultByRecordId(recordId);
            //添加问卷选项答题结果
            addSurveyResult(entity.getListAnswers(), recordId);
            //关联测评任务和问卷记录
            addTaskSurvey(entity.getTaskId(),recordId);
            //更新完成状态
            surveyRecordDao.updateSurveyRecordState(recordId);
            return true;
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("添加问卷作答记录时发生异常：",e);
            return  false;
        }
    }

    /**
     *  更新问卷作答记录的日期
     * @param recordId 记录id
     * @param recordDate 作答日期
     * @return 影响行数
     */
    @Override
    public int updateSurveyRecordDate(Integer recordId, Date recordDate){
        var map = new HashMap<String,Object>();
        map.put("recordId",recordId);
        map.put("recordDate",recordDate);
        return surveyRecordDao.updateSurveyRecordDate(map);
    }

    /**
     *  添加问卷作答记录：问卷调查任务
     * @param entity 实体对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addRecordForTaskSurvey(TaskSurveyRecordEntity entity){
        var recordId = entity.getId();
        try {
            //先删除未完成的作答结果
            surveyRecordDao.deleteResultByRecordId(recordId);
            updateSurveyRecordDate(recordId,new Date());
            //添加问卷选项答题结果
            addSurveyResult(entity.getListAnswers(), recordId);
            //更新完成状态
            surveyRecordDao.updateSurveyRecordState(recordId);
            return true;
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("添加问卷作答记录时发生异常：",e);
            return  false;
        }
    }

    /**
     * 添加问卷作答记录：训练营
     * @param entity 实体对象
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addRecordForCamp(TrainingCampSurveyEntity entity){
        try {
            //先删除未完成作答记录
            delRecordByCampId(entity.getUserId(), entity.getCampId());
            //添加作答记录
            entity.setId(0);
            entity.setRecordDate(new Date());
            surveyRecordDao.addRecordForCamp(entity);
            var recordId = entity.getId();
            //先删除未完成的作答结果
            surveyRecordDao.deleteResultByRecordId(recordId);
            //添加问卷选项答题结果
            addSurveyResult(entity.getListAnswers(), recordId);
            //关联训练营和问卷记录
            addTrainingCampSurvey(entity.getCampId(), recordId);
            //更新完成状态
            surveyRecordDao.updateSurveyRecordState(recordId);
            return true;
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("添加问卷作答记录时发生异常：",e);
            return  false;
        }
    }

    /**
     *  添加问卷作答记录：活动
     * @param entity 实体对象
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addRecordForActivity(ActivitySurveyEntity entity){
        try{
            delRecordByActivityId(entity.getUserId(),entity.getActivityId(),entity.getSurveyId());
            entity.setId(0);
            entity.setRecordDate(new Date());
            surveyRecordDao.addRecordForActivity(entity);
            var recordId = entity.getId();
            surveyRecordDao.deleteResultByRecordId(recordId);
            addSurveyResult(entity.getListAnswers(), recordId);
            addActivitySurvey(entity.getActivityId(), recordId);
            surveyRecordDao.updateSurveyRecordState(recordId);
            return true;
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("添加问卷作答记录时发生异常：",e);
            return  false;
        }
    }

    /**
     *  查询测评任务里调查问卷记录集合：分页
     * @param dto 调查问卷记录实体对象
     * @return 问卷作答记录集合
     */
    @Override
    public BSDatatableRes<TaskSurveyDto> getListByPaged(TaskSurveyDto dto){
        var dtRes = new BSDatatableRes<TaskSurveyDto>();
        if(dto.getStructId() != 0){
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listRecords = surveyRecordDao.getList(dto);
        PageInfo<TaskSurveyDto> recordDto = new PageInfo<>(listRecords);
        dtRes.setData(recordDto.getList());
        dtRes.setRecordsTotal((int)recordDto.getTotal());
        dtRes.setRecordsFiltered((int)recordDto.getTotal());
        return dtRes;
    }

    @Override
    public BSDatatableRes<TaskSurveyDto> getListForTaskSurveyByPaged(TaskSurveyDto dto){
        var dtRes = new BSDatatableRes<TaskSurveyDto>();
        if(dto.getStructId() != 0){
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listRecords = surveyRecordDao.getListForTaskSurvey(dto);
        PageInfo<TaskSurveyDto> recordDto = new PageInfo<>(listRecords);
        dtRes.setData(recordDto.getList());
        dtRes.setRecordsTotal((int)recordDto.getTotal());
        dtRes.setRecordsFiltered((int)recordDto.getTotal());
        return dtRes;
    }

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return surveyRecordDao.delRecord(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  导出测评记录：测评任务
     * @param dto 查询条件
     * @return  测评记录集合
     */
    @Override
    public List<ExportSurveyRecordDto> getExportRecordList(TaskSurveyDto dto){
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return surveyRecordDao.getExportRecordList(dto);
    }

    /**
     *  导出问卷调查记录
     * @param dto 查询条件
     * @return 记录集合
     */
    @Override
    public List<ExportSurveyRecordDto> getExportTaskSurveyRecord(TaskSurveyDto dto){
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return surveyRecordDao.getExportTaskSurveyRecord(dto);
    }

    /**
     *  导出调查问卷结果数据
     * @param dto 条件
     * @return 集合
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportTaskSurveyResult(TaskSurveyDto dto){
        List<LinkedHashMap<String,Object>> questions =surveyQuestionDao.getListForExport(dto.getSurveyId() == null ? 0 : dto.getSurveyId());
        StringBuilder questionBuilder = new StringBuilder();
        for(LinkedHashMap<String,Object> questionMap: questions){
            questionBuilder.append(String.format("max(case when psq.q_number ='%s' then psr.item_id else 0 end) as 第%s题,",questionMap.get("q_number"),questionMap.get("q_number")));
        }
        String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
        dto.setSql(qNoStr);
        return surveyRecordDao.getExportTaskSurveyResult(dto);
    }

    /**
     *  获取训练营的问卷作答记录
     * @param campId 训练营id
     * @return 作答记录集合
     */
    @Override
    public List<TrainingCampSurveyRecordDto> getTrainingCampSurveyRecordList(Integer campId){
        return surveyRecordDao.getTrainingCampSurveyRecordList(campId);
    }

    /**
     *  获取训练营的问卷作答记录：分页
     * @param dto 查询条件
     * @return 作答记录集合
     */
    @Override
    public BSDatatableRes<TrainingCampSurveyRecordDto> getTrainingCampSurveyRecordListByPaged(TrainingCampSurveyRecordDto dto){
        var dtRes = new BSDatatableRes<TrainingCampSurveyRecordDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var campId = dto.getCampId();
        var listRecords = surveyRecordDao.getTrainingCampSurveyRecordList(campId);
        PageInfo<TrainingCampSurveyRecordDto> recordDto = new PageInfo<>(listRecords);
        dtRes.setData(recordDto.getList());
        dtRes.setRecordsTotal((int)recordDto.getTotal());
        dtRes.setRecordsFiltered((int)recordDto.getTotal());
        return dtRes;
    }

    /**
     *  获取活动的问卷作答记录：分页
     * @param dto 查询条件
     * @return 作答记录集合
     */
    @Override
    public BSDatatableRes<ActivitySurveyRecordDto> getActivitySurveyRecordListByPaged(ActivitySurveyRecordDto dto){
        var dtRes = new BSDatatableRes<ActivitySurveyRecordDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var activityId = dto.getActivityId();
        var listRecords = surveyRecordDao.getActivitySurveyRecordList(activityId);
        PageInfo<ActivitySurveyRecordDto> recordDto = new PageInfo<>(listRecords);
        dtRes.setData(recordDto.getList());
        dtRes.setRecordsTotal((int)recordDto.getTotal());
        dtRes.setRecordsFiltered((int)recordDto.getTotal());
        return dtRes;
    }

    /**
     *  获取我的问卷记录
     * @param dto 查询条件
     * @return 问卷记录集合
     */
    @Override
    public BSDatatableRes<TaskSurveyDto> getMyRecords(TaskSurveyDto dto){
        var dtRes = new BSDatatableRes<TaskSurveyDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<TaskSurveyDto> listRecords = surveyRecordDao.getMyRecords(dto);
        PageInfo<TaskSurveyDto> surveyRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(surveyRecordDto.getList());
        dtRes.setRecordsTotal((int)surveyRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)surveyRecordDto.getTotal());
        return dtRes;
    }

    /**
     *  获取问卷记录id
     * @param taskId 任务id
     * @param userId 用户id
     * @param surveyId 问卷id
     * @return 问卷记录id
     */
    @Override
    public int getSurveyRecordId(Integer taskId, Integer userId, Integer surveyId){
        HashMap<String, Integer> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("userId", userId);
        map.put("surveyId", surveyId);
        return surveyRecordDao.getSurveyRecordId(map);
    }
}
