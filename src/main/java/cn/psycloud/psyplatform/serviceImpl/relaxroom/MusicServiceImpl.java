package cn.psycloud.psyplatform.serviceImpl.relaxroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.relaxroom.MusicDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.relaxroom.MusicEntity;
import cn.psycloud.psyplatform.service.relaxroom.MusicService;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class MusicServiceImpl implements MusicService {
    @Autowired
    private MusicDao musicDao;

    /**
     *  根据专辑查询音乐列表
     * @param albumId 专辑id
     * @return 音乐集合
     */
    public List<MusicEntity> getListByAlbumId(Integer albumId){
        return musicDao.getListByAlbumId(albumId);
    }

    /**
     *  删除音乐
     * @param musicId 音乐id
     * @return 影响行数
     */
    public int delete(Integer musicId){
        return musicDao.delete(musicId);
    }

    /**
     *  上传音乐
     * @param entity 音乐实体对象
     * @return 影响行数
     */
    public int add(MusicEntity entity){
        entity.setUploadDate(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        var user = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setOperator(user.getUserId());
        return musicDao.insert(entity);
    }
}
