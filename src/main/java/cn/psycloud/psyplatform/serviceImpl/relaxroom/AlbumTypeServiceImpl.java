package cn.psycloud.psyplatform.serviceImpl.relaxroom;

import cn.psycloud.psyplatform.dao.relaxroom.AlbumTypeDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.relaxroom.AlbumTypeDto;
import cn.psycloud.psyplatform.entity.relaxroom.AlbumTypeEntity;
import cn.psycloud.psyplatform.service.relaxroom.AlbumTypeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AlbumTypeServiceImpl implements AlbumTypeService {
    @Autowired
    private AlbumTypeDao albumTypeDao;

    /**
     *  查询专辑类型集合
     * @return 专辑类型集合
     */
    @Override
    public List<AlbumTypeDto> getList(AlbumTypeDto dto){
        return albumTypeDao.getList(dto);
    }

    /**
     *  查询专辑类型集合：分页
     * @param dto 专辑类型实体对象
     * @return 专辑类型集合
     */
    @Override
    public BSDatatableRes<AlbumTypeDto> getListByPaged(AlbumTypeDto dto){
        var dtRes = new BSDatatableRes<AlbumTypeDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listAlbumTypes = getList(dto);
        var albumTypeDto = new PageInfo<>(listAlbumTypes);
        dtRes.setData(listAlbumTypes);
        dtRes.setRecordsTotal((int) albumTypeDto.getTotal());
        dtRes.setRecordsFiltered((int) albumTypeDto.getTotal());
        return dtRes;
    }

    /**
     *  将数据格式转换成select2格式
     * @param listAlbumTypes 专辑分类集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<AlbumTypeDto> listAlbumTypes) {
        var lsNode = new ArrayList<>();
        for (AlbumTypeDto dto : listAlbumTypes) {
            var select2Data = new Select2Data();
            select2Data.setId(dto.getId());
            select2Data.setText(dto.getAlbumTypeName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  查询专辑类型集合：select2
     * @return 专辑分类集合
     */
    @Override
    public List<Object> getListForSelect(AlbumTypeDto dto){
        var listAlbumTypes =albumTypeDao.getList(dto);
        return getSelect2Data(listAlbumTypes);
    }

    /**
     *  新增
     * @param entity 专辑类型实体对象
     * @return 影响行数
     */
    @Override
    public int add(AlbumTypeEntity entity){ return albumTypeDao.insert(entity);}

    /**
     *  更新
     * @param entity 专辑类型实体对象
     * @return 影响行数
     */
    @Override
    public int update(AlbumTypeEntity entity){return albumTypeDao.update(entity);}

    /**
     *  删除
     * @param id 栏目id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){ return albumTypeDao.delete(id);}

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }
}
