package cn.psycloud.psyplatform.serviceImpl.relaxroom;

import cn.psycloud.psyplatform.dao.relaxroom.AlbumDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleDto;
import cn.psycloud.psyplatform.dto.relaxroom.AlbumDto;
import cn.psycloud.psyplatform.dto.relaxroom.AlbumTypeDto;
import cn.psycloud.psyplatform.entity.relaxroom.AlbumEntity;
import cn.psycloud.psyplatform.service.relaxroom.AlbumService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;
import lombok.var;

@Service
public class AlbumServiceImpl implements AlbumService {
    @Autowired
    private AlbumDao albumDao;

    @Value("${file.location}")
    String uploadPath;
    /**
     *  添加专辑
     * @param entity 专辑实体对象
     * @return 影响行数
     */
    @Override
    public int addAlbum(AlbumEntity entity){
        return albumDao.insert(entity);
    }

    /**
     *  修改专辑
     * @param entity 专辑实体对象
     * @return 影响行数
     */
    @Override
    public int updateAlbum(AlbumEntity entity){
        return albumDao.update(entity);
    }

    /**
     *  删除专辑
     * @param id 专辑id
     * @return 影响行数
     */
    @Override
    public int delAlbum(Integer id){
        var albumDto = getById(id);
        if(albumDto.getCoverImg() !=null && !"".equals(albumDto.getCoverImg())) {
            var filePath = uploadPath + "music_cover/thumbnail/"+albumDto.getCoverImg();
            File file = new File(filePath);
            if(file.exists()){
                file.delete();
            }
        }
        return albumDao.delete(id);
    }

    /**
     *  查询专辑集合
     * @return 集合
     */
    @Override
    public List<AlbumDto> getList(){
        return albumDao.getList(new AlbumDto());
    }

    /**
     *  根据ID查询专辑信息
     * @param id 专辑id
     * @return 专辑实体对象
     */
    @Override
    public AlbumDto getById(Integer id){
        return albumDao.getById(id);
    }

    /**
     *  查询专辑集合：分页
     * @param dto 专辑实体对象
     * @return 专辑集合
     */
    @Override
    public BSDatatableRes<AlbumDto> getListByPaged(AlbumDto dto){
        var dtRes = new BSDatatableRes<AlbumDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listAlbums = getList();
        var albumDto = new PageInfo<>(listAlbums);
        dtRes.setData(listAlbums);
        dtRes.setRecordsTotal((int) albumDto.getTotal());
        dtRes.setRecordsFiltered((int) albumDto.getTotal());
        return dtRes;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delAlbum(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  获取专辑类别下的专辑集合
     * @param albumTypeId 专辑分类Id
     * @return 专辑集合
     */
    @Override
    public List<AlbumDto> getListByType(Integer albumTypeId) {
        var albumDto = new AlbumDto();
        albumDto.setAlbumType(albumTypeId);
        return albumDao.getList(albumDto);
    }
}
