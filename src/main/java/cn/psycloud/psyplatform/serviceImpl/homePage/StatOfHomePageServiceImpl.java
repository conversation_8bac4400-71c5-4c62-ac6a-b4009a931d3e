package cn.psycloud.psyplatform.serviceImpl.homePage;

import cn.psycloud.psyplatform.dao.homePage.StatOfHomePageDao;
import cn.psycloud.psyplatform.dto.homePage.StatOfHomePageDto;
import cn.psycloud.psyplatform.service.homePage.StatOfHomePageService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StatOfHomePageServiceImpl implements StatOfHomePageService {
    @Autowired
    private StatOfHomePageDao statOfHomePageDao;
    /**
     *  平台首页数据
     * @return 平台首页数据实体对象
     */
    public StatOfHomePageDto get(){
        var dto = new StatOfHomePageDto();
        dto.setVisitorNum(statOfHomePageDao.getVisitorCount());
        dto.setCounselorNum(statOfHomePageDao.getCounselorCount());
        dto.setMeasuringRecordNum(statOfHomePageDao.getMeasuringRecordCount());
        dto.setCounselingNum(statOfHomePageDao.getCounselingCount());
        dto.setTestRecordCapacitys(statOfHomePageDao.getTestRecordCapacitys());
        dto.setCounselingCapacitys(statOfHomePageDao.getCounselingCapacity());
        return  dto;
    }
}
