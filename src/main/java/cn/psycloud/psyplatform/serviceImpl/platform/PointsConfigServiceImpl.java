package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.PointsConfigDao;
import cn.psycloud.psyplatform.entity.platform.PointsConfigEntity;
import cn.psycloud.psyplatform.service.platform.PointsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PointsConfigServiceImpl  implements PointsConfigService {
    @Autowired
    private PointsConfigDao pointsConfigDao;
    /**
     *  保存积分参数配置信息
     * @param entity 积分配置实体对象
     * @return 影响行数
     */
    @Override
    public int saveConfig(PointsConfigEntity entity){
        return pointsConfigDao.save(entity);
    }
}
