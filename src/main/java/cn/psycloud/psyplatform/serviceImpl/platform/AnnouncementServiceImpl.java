package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.AnnouncementDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.platform.AnnouncementDto;
import cn.psycloud.psyplatform.entity.platform.AnnouncementEntity;
import cn.psycloud.psyplatform.service.platform.AnnouncementService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.util.List;

@Service
public class AnnouncementServiceImpl implements AnnouncementService {
    @Autowired
    private AnnouncementDao announcementDao;
    /**
     *  获取全部公告集合
     * @param dto 平台公告实体对象
     * @return 平台公告实体对象
     */
    public List<AnnouncementDto> getList(AnnouncementDto dto){
        return announcementDao.getList(dto);
    }

    /**
     *  获取公告集合：分页
     * @param dto 实体对象
     * @return 平台公告集合
     */
    @Override
    public BSDatatableRes<AnnouncementDto> getListByPaged(AnnouncementDto dto){
        var dtRes = new BSDatatableRes<AnnouncementDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listAnnouncements = getList(dto);
        var announcementDto = new PageInfo<>(listAnnouncements);
        dtRes.setData(announcementDto.getList());
        dtRes.setRecordsTotal((int) announcementDto.getTotal());
        dtRes.setRecordsFiltered((int) announcementDto.getTotal());
        return dtRes;
    }

    /**
     *  根据条件查询公告信息
     * @param dto 平台公告实体对象
     * @return 平台公告实体对象
     */
    @Override
    public AnnouncementDto get(AnnouncementDto dto){
        return announcementDao.get(dto);
    }

    /**
     *  添加
     * @param entity 平台公告实体对象
     * @return 影响行数
     */
    @Override
    public int add(AnnouncementEntity entity){
        return announcementDao.add(entity);
    }

    /**
     *  修改
     * @param entity 平台公告实体对象
     * @return 影响行数
     */
    @Override
    public int update(AnnouncementEntity entity){
        return  announcementDao.update(entity);
    }

    /**
     *  删除
     * @param id 公告id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return  announcementDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }
}