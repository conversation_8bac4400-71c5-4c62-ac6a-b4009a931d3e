package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dao.platform.PointsConfigDao;
import cn.psycloud.psyplatform.dao.platform.SmsConfigDao;
import cn.psycloud.psyplatform.dao.platform.SysConfigDao;
import cn.psycloud.psyplatform.dto.platform.SysConfigDto;
import cn.psycloud.psyplatform.entity.platform.SysConfigEntity;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SysConfigServiceImpl implements SysConfigService {
    @Autowired
    private SysConfigDao sysConfigDao;
    @Autowired
    private SmsConfigDao smsConfigDao;
    @Autowired
    private PointsConfigDao pointsConfigDao;
    //是否启用redis缓存
    @Value("${spring.redis.isEnabled}")
    private boolean isRedisEnabled;

    /**
     *  获取平台参数信息
     * @return 平台参数实体对象
     */
    @Override
    public SysConfigDto get() {
        var sysConfigDto = new SysConfigDto();
        //启用Redis缓存情况下，优先从缓存里取值
        if(isRedisEnabled && JedisUtil.exists("sysConfig")){
            sysConfigDto =  JSONUtil.toBean(JedisUtil.get("sysConfig"), SysConfigDto.class);
        }
        else{
            sysConfigDto = sysConfigDao.get();
            sysConfigDto.setSmsConfig(smsConfigDao.get());
            sysConfigDto.setPointsConfig(pointsConfigDao.get());
        }
        return sysConfigDto;
    }

    /**
     *  更新平台参数信息
     * @param entity 平台参数实体对象
     * @return 影响行数
     */
    public int update(SysConfigEntity entity) {
        return sysConfigDao.update(entity);
    }
}
