package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dto.anteroom.*;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.CommonData;
import cn.psycloud.psyplatform.dto.core.Select2UserData;
import cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity;
import cn.psycloud.psyplatform.entity.anteroom.UserEntity;
import cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity;
import cn.psycloud.psyplatform.service.anteroom.SysFunctionService;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class UserServiceImpl implements UserService {
    @Autowired
    private UserDao userDao;
    @Autowired
    private SysFunctionService sysFunctionService;
    @Value("${platform.password-forced-expire.expire-time}")
    private Integer pwdExpireTime;
    /**
     * 账号密码登录
     * @param login 登录实体对象
     * @return 用户实体对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDto signIn(LoginDto login) {
        var user = new UserDto();
        try {
            login.setPwd(DigestUtil.md5Hex(login.getPwd().getBytes()));
            user = userDao.signIn(login);
            if(user != null) {
                var listSysFunctions = sysFunctionService.getGrantByRole(user.getRole().getRoleId());
                user.getRole().setListFunctions(listSysFunctions);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            log.error("【账号密码登录】发生异常：",e);
        }
        return user;
    }

    /**
     *  短信登录
     * @param mobile 手机号码
     * @return 用户实体类对象
     */
    @Override
    public UserDto smsLogin(String mobile){
        var userDto = userDao.smsLogin(mobile);
        if(userDto != null) {
            var listSysFunctions = sysFunctionService.getGrantByRole(userDto.getRole().getRoleId());
            userDto.getRole().setListFunctions(listSysFunctions);
        }
        return userDto;
    }

    /**
     *  手机号码登录
     * @param mobile 手机号码
     * @return 用户实体对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDto mobileLogin(String mobile){
        var userDto = userDao.mobileLogin(mobile);
        try{
            if(userDto != null) {
                var listSysFunctions = sysFunctionService.getGrantByRole(userDto.getRole().getRoleId());
                userDto.getRole().setListFunctions(listSysFunctions);
            }
        }
        catch (Exception ex){
            log.error("手机号登录失败：",ex);
        }
        return userDto;
    }

    /**
     *  查询用户集合
     * @param dto 查询条件
     * @return 用户集合
     */
    @Override
    public List<UserDto> getList(UserDto dto, boolean checkPrivilege){
        if (checkPrivilege) {
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        return userDao.getUserList(dto);
    }

    /**
     *  获取推荐咨询师集合(下拉列表)
     * @return 用户集合
     */
    public List<UserDto> getCounselorListForSelect(boolean checkPrivilege){
        var dto = new UserDto();
        if (checkPrivilege) {
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        return userDao.getCounselorListForSelect(dto);
    }

    /**
     *  查询用户集合：转换成select所需格式
     * @return 对象集合
     */
    @Override
    public List<Object> getCounselorListForSelect() {
        var users =getCounselorListForSelect(true);
        return getSelect2Data(users);
    }

    /**
     *  根据条件查询用户集合
     * @param dto 查询条件对象
     * @param userType 查询用户类型
     * @param checkPrivilege 是否验证数据权限
     * @return 用户集合
     */
    @Override
    public BSDatatableRes<UserDto> getUserListByPaged(UserDto dto, String userType, boolean checkPrivilege) {
        if(checkPrivilege) {
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        var dtRes = new BSDatatableRes<UserDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize() +1,dto.getPageSize());
        List<UserDto> listUsers = new ArrayList<>();
        if("admin".equals(userType)) {
            listUsers = userDao.getAdminUsers(dto);
        }
        if("counselor".equals(userType)){
            listUsers = userDao.getCounselorList(dto);
        }
        if("visitor".equals(userType)){
            listUsers = userDao.getVisitorList(dto);
        }
        PageInfo<UserDto> userDto = new PageInfo<>(listUsers);
        dtRes.setData(userDto.getList());
        dtRes.setRecordsTotal((int)userDto.getTotal());
        dtRes.setRecordsFiltered((int)userDto.getTotal());
        return dtRes;
    }

    /**
     *  网站首页推荐咨询师列表
     * @return 咨询师集合
     */
    @Override
    public List<UserDto> getRecommendCounselorList() {
        return userDao.getRecommendCounselorList();
    }

    /**
     *  将数据格式转换成select2格式
     * @param listUsers 用户集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<UserDto> listUsers) {
        var lsNode = new ArrayList<>();
        for (UserDto dto : listUsers) {
            var select2UserData = new Select2UserData();
            select2UserData.setId(dto.getUserId());
            select2UserData.setText(dto.getRealName());
            lsNode.add(select2UserData);
        }
        return lsNode;
    }

    /**
     *  根据ID查询用户信息
     * @param userId 用户id
     * @return 用户实体对象
     */
    @Override
    public UserDto getById(Integer userId){
        var dto = new UserDto();
        dto.setUserId(userId);
        dto.setIsChecked(1);
        return userDao.get(dto);
    }

    /**
     *  根据用户名查询用户信息
     * @param loginName 用户名
     * @return 用户实体对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDto getUserByName(String loginName){
        var userDto = userDao.getUserByName(loginName);
        setUserSysFunctions(userDto);
        return userDto;
    }

    /**
     *  预约咨询验证来访者信息
     * @param loginName 来访者用户名
     * @return 用户id
     */
    @Override
    public String checkUserInfo(String loginName){
        return userDao.checkUserInfo(loginName);
    }

    /**
     *  获取用户权限
     * @param user 用户实体对象
     */
    private void setUserSysFunctions(UserDto user) {
        var listSysFunctions = sysFunctionService.getGrantByRole(user.getRoleId());
        user.getRole().setListFunctions(listSysFunctions);
    }

    /**
     *  验证旧密码
     * @param dto 修改密码实体对象
     * @return 验证成功是否
     */
    @Override
    public boolean verifyPassword(ModifyPwdDto dto) {
        var originalPwd = DigestUtil.md5Hex(dto.getOriginalPwd().getBytes());
        var currentPwd = userDao.getPwdById(dto.getUserId());
        return originalPwd.equals(currentPwd);
    }

    /**
     *  修改密码
     * @param dto 修改密码实体对象
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean modifyPassword(ModifyPwdDto dto){
        dto.setNewPwd(DigestUtil.md5Hex(dto.getNewPwd().getBytes()));
        try {
            userDao.modifyPassword(dto);
            userDao.updateLastChangePwdDate(dto.getUserId());
            return true;
        }
        catch (Exception e){
            log.error("修改密码时发生异常：{}",e.getMessage());
            return  false;
        }
    }

    /**
     *  修改密码时校验旧密码
     * @param dto 修改密码实体对象
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean modifyPasswordByOriginalPwd(ModifyPwdDto dto){
        dto.setNewPwd(DigestUtil.md5Hex(dto.getNewPwd().getBytes()));
        dto.setOriginalPwd(DigestUtil.md5Hex(dto.getOriginalPwd().getBytes()));
        try{
            userDao.modifyPasswordByOriginalPwd(dto);
            userDao.updateLastChangePwdDate(dto.getUserId());
            return  true;
        }
        catch (Exception e){
            log.error("修改密码时发生异常：{}",e.getMessage());
            return  false;
        }
    }

    /**
     *  判断用户的密码是否过期
     * @param userId 用户Id
     * @return 是否过期
     */
    @Override
    public boolean checkPasswordExpiry(Integer userId){
        var lastPasswordChangeDate = userDao.getLastPasswordChangeDate(userId);
        if(lastPasswordChangeDate == null) return  true;
        else
            return DateUtil.betweenDay(lastPasswordChangeDate, new Date(),false) > pwdExpireTime ;
    }

    /**
     * 更新用户登录时间和IP地址
     * @param params 传参
     */
    @Override
    public  void updateLoginDateAndIp(Map<String, String> params){
        userDao.updateLoginDateAndIp(params);
    }

    /**
     *  保存用户信息
     * @param dto 用户信息实体对象
     * @return 是否执行成功
     */
    public boolean insertUserInfo(UserDto dto) {
        boolean isSuccess = false;
        try {
            var userEntity = new UserEntity();
            userEntity.setLoginName(dto.getLoginName());
            userEntity.setPwd(DigestUtil.md5Hex(CommonData.DEFAULT_PASSWORD.getBytes()));
            userEntity.setUserId(0);
            userDao.addUser(userEntity);
            Integer userId = userEntity.getUserId();
            if (userId > 0) {
                dto.setUserId(userId);
                String birth;
                if (StrUtil.isEmpty(dto.getBirth())) {
                    Date currentDate = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    birth = sdf.format(currentDate);
                }
                else {
                    birth = dto.getBirth();
                }
                dto.setBirth(birth);
                UserDto currentUser = (UserDto) SessionUtil.getSession().getAttribute("user");
                dto.setOperator(currentUser == null ? 1 :currentUser.getUserId());
                dto.setRegDate(new Date());

                userDao.deleteUserRole(userId);
                Map<String, String> map = new HashMap<>();
                map.put("userId", String.valueOf(userId));
                map.put("roleId", dto.getRoleId().toString());
                userDao.addUserRole(map);

                userDao.addUserInfo(dto);
                if(dto.getRoleId()!=1 && dto.getRoleId()!=3&&dto.getRoleId()!=4 && dto.getCounselorInfo() != null){
                    dto.getCounselorInfo().setUserId(userId);
                    updateCounselorInfo(dto.getCounselorInfo());
                }
                isSuccess = true;
            }
        }
        catch (Exception e) {
            log.error("保存用户信息发生异常："+ e.getMessage());
        }
        return isSuccess;
    }

    /**
     *  添加用户
     * @param dto 用户实体对象
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUser(UserDto dto){
        return  insertUserInfo(dto);
    }

    /**
     *  修改用户
     * @param dto 用户实体对象
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserDto dto){
        boolean isSuccess = false;
        if(userDao.updateUser(dto.getLoginName(),dto.getUserId())>0){
            userDao.updateUserInfo(dto);
            Map<String,String> map = new HashMap<>();
            map.put("userId",String.valueOf(dto.getUserId()));
            map.put("roleId",dto.getRoleId().toString());
            userDao.updateUserRole(map);
            if (dto.getRoleId() != 1 && dto.getRoleId() != 3 && dto.getRoleId() != 4){
                updateCounselorInfo(dto.getCounselorInfo());
                updateAvatar(dto.getUserId(), dto.getHeadPic());
            }
            isSuccess = true;
        }
        return isSuccess;
    }

    /**
     *  简易修改个人信息
     * @return 影响行数
     */
    public int updateForScale(UserDto dto) {
        Map<String,Object> map = new HashMap<>();
        map.put("realName", dto.getRealName());
        map.put("sex", dto.getSex());
        map.put("birth", dto.getBirth());
        map.put("userId", dto.getUserId());
        return userDao.updateForScale(map);
    }

    /**
     *  验证用户名是否存在
     * @param loginName 用户名
     * @return 是否存在
     */
    @Override
    public int isLoginNameExist(String loginName){
        return userDao.isLoginNameExist(loginName);
    }

    /**
     *  删除用户
     * @param userId 用户id
     * @return 影响行数
     */
    @Override
    public int delUser(Integer userId){
        return userDao.deleteById(userId);
    }

    /**
     *  批量删除
     * @param ids 用户id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDelUser(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delUser(id) > 0;
        }
        return isSuccess;
    }

    /**
     *  注册审核
     * @param userId 用户id
     * @return 影响行数
     */
    @Override
    public int check(Integer userId) {
        return userDao.checkUser(userId);
    }

    /**
     *  批量注册审核
     * @param ids 用户id集合
     * @return 是否成功
     */
    @Override
    public boolean batchCheck(String ids) {
        boolean isSuccess = false;
        var array_ids = ids.split(",");
        for (String arrayId : array_ids) {
            var id =Integer.parseInt(arrayId);
            isSuccess = check(id) > 0;
        }
        return isSuccess;
    }

    /**
     *  更换头像
     * @param userId 用户Id
     * @param headPic 头像
     * @return 影响行数
     */
    @Override
    public int updateAvatar(Integer userId, String headPic) {
        Map<String, String> map = new HashMap<>();
        map.put("userId", userId.toString());
        map.put("headPic", headPic);
        return userDao.updateAvatar(map);
    }

    /**
     *  批量开通：预览
     * @param dto 用户实体对象
     * @return 用户集合
     */
    public List<UserDto> getPreviewUserList(BatchAddDto dto) {
        List<UserDto> listUsers = new ArrayList<>();
        Integer a = 1;
        for (int i = dto.getStart(); i <= dto.getEnd(); i++) {
            String rep = String.valueOf(i);
            for (int j = rep.length(); j <dto.getLen(); j++) {
                rep = "0" + rep;
            }
            var user = new UserDto();
            user.setUserId(a);
            user.setLoginName(dto.getLoginName().replace("*", rep));
            user.setPwd(CommonData.DEFAULT_PASSWORD);
            user.setStructId(dto.getStructId());
            user.setRoleId(dto.getRoleId());
            user.setDescription(isLoginNameExist(dto.getLoginName()) > 0 ? "用户名已存在" : "用户名可以使用");
            listUsers.add(user);
            a++;
        }
        return listUsers;
    }

    /**
     *  批量开通
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAdd(List<BatchAddDto> list) {
        boolean isSuccess = false;
        try {
            for (BatchAddDto dto: list) {
                var userDto = new UserDto();
                userDto.setLoginName(dto.getLoginName());
                userDto.setStructId(dto.getStructId());
                userDto.setBirth("");
                userDto.setIsChecked(1);
                userDto.setRoleId(3);

                isSuccess = insertUserInfo(userDto);
                if (!isSuccess)
                    break;
            }
        }
        catch(Exception e){
            log.error("批量开通发生错误："+ e.getMessage());
        }
        return isSuccess;
    }
    /**
     *  更新咨询师附加信息
     * @param entity 咨询师信息实体对象
     */
    public void updateCounselorInfo(CounselorInfoEntity entity) {
        var counselorInfoEnity = userDao.getCounselorInfoByUserId(entity.getUserId());
        if(counselorInfoEnity != null && counselorInfoEnity.getUserId() > 0)
            userDao.updateCounselorInfo(entity);
        else
            userDao.addCounselorInfo(entity);
    }

    /**
     *  绑定手机号码
     * @param mobile 手机号码
     * @return 影响行数
     */
    public int bindMobile(String mobile) {
        UserDto currentUser = (UserDto) SessionUtil.getSession().getAttribute("user");
        Map<String,String> map = new HashMap<>();
        map.put("userId", currentUser.getUserId().toString());
        map.put("mobile", mobile);
        return userDao.bindMobile(map);
    }

    /**
     *  验证手机号码是否被绑定
     * @param mobile 手机号码
     * @return 影响行数
     */
    public int checkMobile(String mobile) {
        return userDao.checkMobile(mobile);
    }

    /**
     *  手机号码登录/注册
     * @param mobile 手机号码
     * @return 用户实体对象
     */
    public UserDto MobileLogin(String mobile) {
        var userDto = new UserDto();
        userDto.setIsChecked(1);
        userDto.setLoginName(mobile);
        userDto = userDao.get(userDto);
        if (userDto.getUserId() == null || userDto.getUserId() == 0) {
            var userEntity = new UserEntity();
            userEntity.setLoginName(mobile);
            userEntity.setPwd(DigestUtil.md5Hex(CommonData.DEFAULT_PASSWORD));
            userEntity.setUserId(0);
            userDao.addUser(userEntity);
            Integer userId = userEntity.getUserId();

            userDao.deleteUserRole(userId);
            Map<String,String> map = new HashMap<>();
            map.put("userId",String.valueOf(userId));
            map.put("roleId", "3");
            userDao.addUserRole(map);

            userDto.setUserId(userId);

            userDto.setStructId(1);
            userDto.setMobile(mobile);
            userDto.setIsMobileBind(1);
            userDto.setIsChecked(1);
            userDao.addUserInfo(userDto);
            return userDto;
        }
        else {
            return userDto;
        }
    }

    /**
     *  批量更新用户所属角色
     * @param roleId 角色id
     * @param userIds 用户id集合
     * @return 是否执行成功
     */
    public boolean batchUpdate(Integer roleId, String userIds) {
        boolean isSuccess = false;
        var array_userIds = userIds.split(",");
        for (String arrayUserId : array_userIds) {
            Integer userId = Integer.parseInt(arrayUserId);
            isSuccess = userDao.updateUserRole(userId, roleId) > 0;
        }
        return isSuccess;
    }

    /**
     *  用户集合
     * @param users  用户集合
     * @return 导入结果集合
     */
    public List<ImportUserDto> importVisitor(List<ImportUserDto> users, Integer structId) {
        var listImportUsers = new ArrayList<ImportUserDto>();
        for (ImportUserDto dto: users) {
            var importUser = new ImportUserDto();
            if ("".equals(dto.getLoginName())) {
                importUser.setLoginName("");
                importUser.setRealName(dto.getRealName());
                importUser.setState(0);
                importUser.setMsg("用户名为空");
            }
            else {
                var loginName = dto.getLoginName();
                if (isLoginNameExist(loginName) > 0) {
                    importUser.setLoginName(dto.getLoginName());
                    importUser.setRealName(dto.getRealName());
                    importUser.setState(0);
                    importUser.setMsg("用户名已存在");
                }
                else {
                    if (dto.getBirth() == null || dto.getBirth().isEmpty()) {
                        Date currentDate = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        String birth = sdf.format(currentDate);
                        dto.setBirth(birth);
                    }
                    if (CommonHelper.isDateValid(dto.getBirth())) {
                        var userDto = new UserDto();
                        userDto.setLoginName(dto.getLoginName());
                        userDto.setRealName(dto.getRealName());
                        userDto.setStructId(structId);
                        userDto.setSex(dto.getSex());
                        userDto.setBirth(dto.getBirth());
                        userDto.setMobile(dto.getMobile());
                        userDto.setIsMobileBind(1);
                        userDto.setIsChecked(1);
                        userDto.setRoleId(3);
                        boolean isSuccess = insertUserInfo(userDto);
                        importUser.setLoginName(dto.getLoginName());
                        importUser.setRealName(dto.getRealName());
                        importUser.setState(1);
                        importUser.setMsg(isSuccess ? "导入成功" : "导入失败");
                    }
                    else {
                        importUser.setLoginName(dto.getLoginName());
                        importUser.setRealName(dto.getRealName());
                        importUser.setState(0);
                        importUser.setMsg("日期格式错误");
                    }
                }
            }
            listImportUsers.add(importUser);
        }
        return listImportUsers;
    }

    /**
     *  根据用户ID查询用户手机号码
     */
    public String getMobileByUserId() {
        var userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
        var userId = userDto.getUserId();
        return userDao.getMobileByUserId(userId);
    }

    @Override
    public int isSyncUserExists(String mobile){
        return userDao.isSyncUserExists(mobile);
    }

    @Override
    public int updateUserInfoBySyncUserId(UserDto dto){
        return userDao.updateUserInfoBySyncUserId(dto);
    }

    @Override
    public int deleteBySyncUserId(Integer syncUserId){
        return userDao.deleteBySyncUserId(syncUserId);
    }

    /**
     *  添加微信用户信息
     * @param entity 微信用户信息实体
     * @return 影响行数
     * */
    @Override
    public int addWechatUser(WechatUserEntity entity){
        return userDao.addWechatUser(entity);
    }

    /**
     * 根据unionId查询用户信息
     * @param unionId 微信unionId
     * @return 用户信息
     */
    @Override
    public UserDto getUserInfoByWechatUnionId(String unionId){
        return userDao.getUserInfoByWechatUnionId(unionId);
    }

    /**
     * 绑定微信用户
     * @param loginName 登录名
     * @param realName 真实姓名
     * @return 影响行数
     */
    @Override
    public UserDto bindWechatUser(String loginName, String realName){
        var map = new HashMap<String, String>();
        map.put("loginName",loginName);
        map.put("realName",realName);
        var userDto = userDao.getUserInfoByLoginNameAndRealName(map);
        if(userDto != null && userDto.getUserId() > 0){
            var wechatUser = JSONUtil.toBean((String) SessionUtil.getSession().getAttribute("wechatUserInfo"), WechatUserEntity.class);
            wechatUser.setUserId(userDto.getUserId());
            addWechatUser(wechatUser);
        }
        return userDto;
    }
}