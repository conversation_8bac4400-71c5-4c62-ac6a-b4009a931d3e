package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.hutool.core.collection.CollUtil;
import cn.psycloud.psyplatform.dao.anteroom.StructsDao;
import cn.psycloud.psyplatform.dao.anteroom.SysFunctionDao;
import cn.psycloud.psyplatform.dto.anteroom.StructsDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.ZtreeData;
import cn.psycloud.psyplatform.entity.anteroom.StructsEntity;
import cn.psycloud.psyplatform.service.anteroom.StructsService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StructsServiceImpl implements StructsService {
    @Autowired
    private StructsDao structsDao;
    @Autowired
    private SysFunctionDao sysFunctionDao;
    /**
     *  添加组织
     * @param entity 组织实体对象
     * @return 影响行数
     */
    @Override
    public int addStruct(StructsEntity entity){
        return structsDao.add(entity);
    }

    /**
     *  修改组织
     * @param entity 组织实体对象
     * @return 影响行数
     */
    @Override
    public int updateStruct(StructsEntity entity){
        return structsDao.update(entity);
    }

    /**
     *  删除组织
     * @param id 组织id
     * @return 影响行数
     */
    @Override
    public int delStruct(Integer id){
        return structsDao.delete(id);
    }

    /**
     *  清空组织
     */
    @Override
    public void truncateStructs(){
        structsDao.truncateStructs();
    }

    /**
     *  根据条件查询用户负责的组织集合返回ztree所需格式
     * @param userId 用户id
     * @param checkPrivilege  组织集合
     * @return 组织集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Object> getListForZtree(Integer userId, boolean checkPrivilege){
        List<Object> listObject;
        List<StructsDto> listStructs = new ArrayList<>();
        //region 验证查看权限
        if (checkPrivilege)
        {
            UserDto user = (UserDto) SessionUtil.getSession().getAttribute("user");
            var dataPrivilege = PermissonHelper.getDataPrivilege(user);
            if (dataPrivilege == 2){
                listStructs = structsDao.getList(new StructsEntity());
            }
            if(dataPrivilege == 1){
                listStructs = getChild(user.getStructId());
            }
            if(dataPrivilege == 0){
                listStructs = new ArrayList<>();
            }
        }
        else
        {
            listStructs = structsDao.getList(new StructsEntity());
        }
        //endregion
        listObject = fmtListToZtree(listStructs);
        return listObject;
    }

    /**
     *  获取子组织集合
     * @param structId 组织id
     * @return 子组织集合
     */
    public List<StructsDto> getChild(int structId){
        List<StructsDto> listStructs =  structsDao.getChildList(structId);
        var struct = getById(structId);
        listStructs.add(struct);
        listStructs = forTree(listStructs, new ArrayList<StructsDto>());
        return listStructs;
    }

    private List<StructsDto> forTree(List<StructsDto> children,List<StructsDto> allLists){
        for (StructsDto child : children) {
            allLists.add(child);
            if(CollUtil.isNotEmpty(child.getChild())){
                forTree(child.getChild(),allLists);
            }
        }
        return allLists;
    }

    /**
     *  将list的id拼接成字符串
     * @param list 集合
     * @return 拼接后的字符串
     */
    public List<Integer> getChildIds(List<StructsDto> list){
        return list.stream().map( StructsDto::getId ).collect(Collectors.toList());
    }

    /**
     *  根据用户名获取用户权限
     * @param loginName 用户名
     * @return 功能集合
     */
    public List<SysFunctionDto> getSysFunctionByName(String loginName){
        return  sysFunctionDao.getSysFunctionByName(loginName);
    }

    /**
     *  把数据形式转换成zTree的json数据格式
     * @param listStructs 组织集合
     * @return 对象集合
     */
    private List<Object> fmtListToZtree(List<StructsDto> listStructs)
    {
        var lsNode = new ArrayList<>();
        for(StructsDto struct : listStructs)
        {
            var zTreeData = new ZtreeData() ;
            zTreeData.setId(struct.getId().toString());
            zTreeData.setPId(struct.getParentId().toString());
            zTreeData.setName(struct.getStructName());
            lsNode.add(zTreeData);
        }
        return lsNode;
    }

    /**
     *  根据组织ID查询组织信息
     * @param structId 组织id
     * @return 组织实体对象
     */
    @Override
    public StructsDto getById(Integer structId){
        return structsDao.getById(structId);
    }

    /**
     *  移动组织
     * @param targetNode 目标节点
     * @param node 源节点
     * @return 影响行数
     */
    @Override
    public int moveStruct(Integer targetNode, Integer node){
        return structsDao.moveStruct(targetNode,node);
    }
}