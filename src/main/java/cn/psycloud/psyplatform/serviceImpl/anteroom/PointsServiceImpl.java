package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.psycloud.psyplatform.dao.anteroom.PointsDao;
import cn.psycloud.psyplatform.dto.anteroom.PointsDetailDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.anteroom.PointsDetailEntity;
import cn.psycloud.psyplatform.service.anteroom.PointsService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;

@Service
public class PointsServiceImpl implements PointsService {
    @Autowired
    private PointsDao pointsDao;
    /**
     *  更新积分余额
     * @param userId 用户Id
     * @param points 积分
     * @return 影响行数
     */
    @Override
    public int updatePointsBalance(Integer userId, Integer points){
        var map = new HashMap<String,Integer>();
        map.put("userId",userId);
        map.put("point", points);
        return pointsDao.updatePoints(map);
    }

    /**
     *  用户积分充值和消费(批量操作)
     * @param dto 积分明细实体对象
     * @return 执行是否成功
     */
    @Override
    public boolean updatePoints(PointsDetailDto dto){
        boolean isSuccess = false;
        var arrayId = dto.getUserIds().split(",");
        for(String id: arrayId){
            Integer userId = Integer.parseInt(id);
            if(updatePointsBalance(userId,dto.getPoint()) > 0){
                var pointsDetailEntity = new PointsDetailEntity();
                pointsDetailEntity.setUserId(userId);
                pointsDetailEntity.setPoint(dto.getPoint());
                var currentUser = (UserDto) SessionUtil.getSession().getAttribute("user");
                pointsDetailEntity.setOperator(currentUser.getUserId());
                pointsDetailEntity.setOperateDate(new Date());
                pointsDetailEntity.setChargeType(dto.getChargeType());
                pointsDetailEntity.setSourceId(dto.getSourceId());
                isSuccess = insertPoints(pointsDetailEntity) > 0;
            }
        }
        return isSuccess;
    }

    /**
     *  保存积分明细
     * @param entity 积分明细实体对象
     * @return 影响行数
     */
    @Override
    public int insertPoints(PointsDetailEntity entity){

        return pointsDao.insertPoints(entity);
    }

    /**
     *  查询用户集合：分页查询
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<PointsDetailDto> getListByPaged(PointsDetailDto dto){
        var dtRes = new BSDatatableRes<PointsDetailDto>();
        dto.setChildStructs(PermissonHelper.getChildStructIds(dto.getStructId()));
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listPointsDetails = pointsDao.getList(dto);
        var pointsDetailDto = new PageInfo<>(listPointsDetails);
        dtRes.setData(pointsDetailDto.getList());
        dtRes.setRecordsTotal((int)pointsDetailDto.getTotal());
        dtRes.setRecordsFiltered((int)pointsDetailDto.getTotal());
        return dtRes;
    }
}
