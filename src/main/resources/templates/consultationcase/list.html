<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询个案管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询个案管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <button type="button" id="btnSearch" class="btn btn-primary mr-1">
                                <i class="fa fa-search mr-1"></i>搜索
                            </button>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbConsultationCase" class="table table-striped" width="100%">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th align="left">咨询日期</th>
                            <th align="left">咨询师姓名</th>
                            <th align="left">来访者工号</th>
                            <th align="left">来访者姓名</th>
                            <th align="left">咨询时长(分钟)</th>
                            <th align="left">咨询形式</th>
                            <th align="left">咨询领域</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- 筛选条件模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="searchModalLabel">筛选条件</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="searchForm">
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询日期：</label>
                            <div class="col-8">
                                <input type="date" class="form-control" id="consultationDate" name="consultationDate">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询师：</label>
                            <div class="col-8">
                                <select class="form-control" id="counselorId" name="counselorId">
                                    <option value="">全部</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">来访者工号：</label>
                            <div class="col-8">
                                <input type="text" class="form-control" id="loginName" name="loginName">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">来访者姓名：</label>
                            <div class="col-8">
                                <input type="text" class="form-control" id="realName" name="realName">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询类型：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationType" name="consultationType">
                                    <option value="">全部</option>
                                    <option value="1">首次咨询</option>
                                    <option value="2">第二次咨询</option>
                                    <option value="3">第三次咨询</option>
                                    <option value="4">第四次咨询</option>
                                    <option value="5">第五次咨询</option>
                                    <option value="6">第六次及以上咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询形式：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationForm" name="consultationForm">
                                    <option value="">全部</option>
                                    <option value="1">驻场咨询</option>
                                    <option value="2">线上咨询</option>
                                    <option value="3">门店咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询领域：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationField" name="consultationField">
                                    <option value="">全部</option>
                                    <option value="1">心理健康</option>
                                    <option value="2">情绪压力</option>
                                    <option value="3">人际关系</option>
                                    <option value="4">恋爱情感</option>
                                    <option value="5">家庭关系</option>
                                    <option value="6">亲子教育</option>
                                    <option value="7">职场发展</option>
                                    <option value="8">个人成长</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">性别：</label>
                            <div class="col-8">
                                <select class="form-control" id="visitorGender" name="visitorGender">
                                    <option value="">全部</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnQuery">查询</button>
                    <button type="button" class="btn btn-light" id="btnReset">重置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="viewDetailModal" tabindex="-1" role="dialog" aria-labelledby="viewDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="viewDetailModalLabel">咨询个案详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 p-3">
                            <p><strong>咨询日期：</strong><span id="detailConsultationDate"></span></p>
                            <p><strong>咨询形式：</strong><span id="detailConsultationForm"></span></p>
                            <p><strong>咨询时长：</strong><span id="detailConsultationDuration"></span>分钟</p>
                            <p><strong>咨询类型：</strong><span id="detailConsultationType"></span></p>
                            <p><strong>来访年龄：</strong><span id="detailVisitorAge"></span></p>
                            <p><strong>来访性别：</strong><span id="detailVisitorGender"></span></p>
                            <p><strong>婚姻状态：</strong><span id="detailMaritalStatus"></span></p>
                            <p><strong>有无子女：</strong><span id="detailHasChildren"></span></p>
                            <p><strong>咨询领域：</strong><span id="detailConsultationField"></span></p>
                            <p><strong>问题概述：</strong><span id="detailProblemSummary"></span></p>
                            <p><strong>咨询关键词：</strong><span id="detailConsultationKeywords"></span></p>
                            <p><strong>是否职场问题：</strong><span id="detailIsWorkplaceIssue"></span></p>
                            <p id="workplaceDescriptionRow" style="display: none;"><strong>职场问题描述：</strong><span id="detailWorkplaceDescription"></span></p>
                            <p><strong>是否有心理风险：</strong><span id="detailHasPsychologicalRisk"></span></p>
                            <p id="riskDescriptionRow" style="display: none;"><strong>风险描述：</strong><span id="detailRiskDescription"></span></p>
                            <p><strong>后续建议：</strong><span id="detailFollowUpSuggestion"></span></p>
                            <p id="otherSuggestionRow" style="display: none;"><strong>其他建议：</strong><span id="detailOtherSuggestion"></span></p>
                            <p><strong>添加时间：</strong><span id="detailCreateTime"></span></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        // 枚举值转换函数
        const EnumConverter = {
            consultationForm: {
                1: '驻场咨询',
                2: '线上咨询',
                3: '门店咨询'
            },
            consultationType: {
                1: '首次咨询',
                2: '第二次咨询',
                3: '第三次咨询',
                4: '第四次咨询',
                5: '第五次咨询',
                6: '第六次及以上咨询'
            },
            consultationField: {
                1: '心理健康',
                2: '情绪压力',
                3: '人际关系',
                4: '恋爱情感',
                5: '家庭关系',
                6: '亲子教育',
                7: '职场发展',
                8: '个人成长'
            },
            visitorAge: {
                1: '20岁及以下',
                2: '21-25岁',
                3: '26-30岁',
                4: '31-35岁',
                5: '36-40岁',
                6: '41-45岁',
                7: '46-50岁',
                8: '50岁以上'
            },
            maritalStatus: {
                1: '未婚',
                2: '已婚'
            },
            hasChildren: {
                1: '有',
                2: '无'
            },
            followUpSuggestion: {
                1: '无需跟进',
                2: '定期咨询',
                3: '转介就医',
                4: '其他'
            }
        };

        // 获取枚举值对应的文本
        function getEnumText(enumObj, value) {
            return enumObj[value] || '';
        }

        // 处理条件显示字段
        function handleConditionalFields(data) {
            // 处理职场问题
            const isWorkplaceIssue = data.isWorkplaceIssue === 1 ? '是' : '否';
            $("#workplaceDescriptionRow").toggle(data.isWorkplaceIssue === 1);
            $("#detailIsWorkplaceIssue").text(isWorkplaceIssue);
            $("#detailWorkplaceDescription").text(data.workplaceDescription || '');

            // 处理心理风险
            const hasPsychologicalRisk = data.hasPsychologicalRisk === 1 ? '有' : '无';
            $("#riskDescriptionRow").toggle(data.hasPsychologicalRisk === 1);
            $("#detailHasPsychologicalRisk").text(hasPsychologicalRisk);
            $("#detailRiskDescription").text(data.riskDescription || '');

            // 处理后续建议
            const followUpSuggestion = getEnumText(EnumConverter.followUpSuggestion, data.followUpSuggestion);
            $("#otherSuggestionRow").toggle(data.followUpSuggestion === 4);
            $("#detailFollowUpSuggestion").text(followUpSuggestion);
            $("#detailOtherSuggestion").text(data.otherSuggestion || '');
        }

        // 设置基本信息
        function setBasicInfo(data) {
            $("#detailConsultationDate").text(data.consultationDate || '');
            $("#detailCounselorName").text(data.counselorName || '');
            $("#detailConsultationDuration").text(data.consultationDuration || '');
            $("#detailConsultationForm").text(getEnumText(EnumConverter.consultationForm, data.consultationForm));
            $("#detailConsultationType").text(getEnumText(EnumConverter.consultationType, data.consultationType));
            $("#detailConsultationField").text(getEnumText(EnumConverter.consultationField, data.consultationField));
            $("#detailVisitorAge").text(getEnumText(EnumConverter.visitorAge, data.visitorAge));
            $("#detailVisitorGender").text(data.visitorGender || '');
            $("#detailMaritalStatus").text(getEnumText(EnumConverter.maritalStatus, data.maritalStatus));
            $("#detailHasChildren").text(getEnumText(EnumConverter.hasChildren, data.hasChildren));
        }

        // 设置描述信息
        function setDescriptionInfo(data) {
            $("#detailProblemSummary").text(data.problemSummary || '');
            
            // 处理咨询关键词，使用badge样式
            const keywords = data.consultationKeywords ? data.consultationKeywords.split(',') : [];
            const keywordsHtml = keywords.map(keyword => 
                `<span class="badge badge-info mr-1">${keyword.trim()}</span>`
            ).join('');
            $("#detailConsultationKeywords").html(keywordsHtml);
            
            $("#detailCreateTime").text(data.createTime || '');
        }

        $(function () {
            //初始化页面权限
            initPage();
            
            // 筛选按钮点击事件
            $("#btnSearch").click(function () {
                $("#searchModal").modal('show');
            });
            
            // 查询按钮点击事件
            $("#btnQuery").click(function () {
                $('#tbConsultationCase').DataTable().ajax.reload();
                $("#searchModal").modal('hide');
            });

            // 重置按钮点击事件
            $("#btnReset").click(function () {
                resetSearchForm();
            });
            // 加载咨询师列表
            initSelect('#counselorId', '/anteroom/user/getCounselorList_for_select',{});

            // 新增按钮点击事件
            $("#btnAdd").click(function () {
                location.href = "/counselingroom/consultationcase/add";
            });

            // 修改按钮点击事件
            $("#tbConsultationCase").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/counselingroom/consultationcase/update?id=" + data.id;
            });

            // 查看详情按钮点击事件
            $("#tbConsultationCase").on('click', '.view-detail', function () {
                let data = oTable.row($(this).parents('tr')).data();
                
                // 设置基本信息
                setBasicInfo(data);
                
                // 设置描述信息
                setDescriptionInfo(data);
                
                // 处理条件显示字段
                handleConditionalFields(data);
                
                // 显示模态框
                $("#viewDetailModal").modal('show');
            });

            // 全选/取消全选
            $("#chkall").change(function () {
                $('.checklist').prop("checked", $(this).prop("checked"));
            });

            // 批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = $("input[name='checklist']:checked").map(function() {
                            return $(this).val();
                        }).get().join(',');
                        
                        if (!ids) {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        
                        $.post("/counselingroom/consultationcase/batch_del", {ids: ids}, function (res) {
                            layer.msg(res.resultMsg, { 
                                icon: res.resultCode === 200 ? 1 : 2, 
                                time: 2000 
                            });
                            if (res.resultCode === 200) {
                                oTable.draw();
                            }
                        }, 'json');
                    }
                });
            });

            // 初始化DataTable
            $("#tbConsultationCase").bsDataTables({
                columns: columns,
                url: '/counselingroom/consultationcase/get_list_by_paged',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType":"application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            fnCallback(res);
                        }
                    });
                }
            });
        });

        // 页面初始化函数
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };

        // DataTable列定义
        let columns = [
            {
                "data": "id", 
                "render": function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox">' +
                           '<input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '">' +
                           '<label class="custom-control-label" for="lbl' + data + '"></label>' +
                           '</div>';
                }, 
                "bSortable": false
            },
            { "data": "consultationDate", "bSortable": false },
            { "data": "counselorName", "bSortable": false },
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "consultationDuration", "bSortable": false },
            {
                "data": "consultationForm", 
                "bSortable": false,
                "render": function(data) {
                    return getEnumText(EnumConverter.consultationForm, data);
                }
            },
            {
                "data": "consultationField", 
                "bSortable": false,
                "render": function(data) {
                    return getEnumText(EnumConverter.consultationField, data);
                }
            }
        ];

        // DataTable列定义
        let columnDefs = [{
            targets: 8,
            render: function (data, type, row, meta) {
                let buttons = '';
                if ('[[${canUpdate}]]' === 'true') {
                    buttons += '<button type="button" class="btn btn-outline-warning btn-sm mr-1 update">' +
                              '<i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                }
                buttons += '<button type="button" class="btn btn-outline-primary btn-sm view-detail">' +
                          '<i class="fa fa-eye mr-1"></i>查看</button>';
                return buttons;
            }
        }];

        // 获取查询条件
        let getQueryCondition = function (data) {
            let param = {};
            // 组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            
            // 组装筛选条件
            const consultationDate = $("#consultationDate").val();
            if (consultationDate) {
                param.consultationDate = consultationDate;
            }
            param.counselorId = $("#counselorId").val();
            param.consultationType = $("#consultationType").val();
            param.consultationForm = $("#consultationForm").val();
            param.consultationField = $("#consultationField").val();
            param.visitorGender = $("#visitorGender").val();
            param.loginName = $("#loginName").val();
            param.realName = $("#realName").val();
            return param;
        };

        // 重置查询条件
        function resetSearchForm() {
            $("#searchForm")[0].reset();
            $('#tbConsultationCase').DataTable().ajax.reload();
        }
    </script>
</th:block>
</body>
</html>