<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .keyword-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }

        .tag-item {
            padding: 5px 15px;
            border-radius: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .tag-item:hover {
            background-color: #e9ecef;
        }

        .tag-item.selected {
            background-color: #727cf5;
            color: white;
            border-color: #727cf5;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询个案管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">编辑咨询个案</a></li>
                    </ol>
                </div>
                <h4 class="page-title">编辑咨询个案</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <form id="frmConsultationCase" action="#" class="col-lg-8">
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label for="loginName">来访者工号：</label>
                                <input type="text" id="loginName" name="loginName" class="form-control" placeholder="请输入来访者工号">
                                <input type="hidden" id="userId" name="userId" value="0">
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationDate">咨询日期：</label>
                                <input type="date" id="consultationDate" name="consultationDate" class="form-control" required>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationForm">咨询形式：</label>
                                <select id="consultationForm" name="consultationForm" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">驻场咨询</option>
                                    <option value="2">线上咨询</option>
                                    <option value="3">门店咨询</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationDuration">咨询时长(分钟)：</label>
                                <input type="number" id="consultationDuration" name="consultationDuration" class="form-control" required>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationType">咨询类型：</label>
                                <select id="consultationType" name="consultationType" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">首次咨询</option>
                                    <option value="2">第二次咨询</option>
                                    <option value="3">第三次咨询</option>
                                    <option value="4">第四次咨询</option>
                                    <option value="5">第五次咨询</option>
                                    <option value="6">第六次及以上咨询</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="visitorGender">来访性别：</label>
                                <select id="visitorGender" name="visitorGender" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="visitorAge">来访年龄：</label>
                                <select id="visitorAge" name="visitorAge" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">20岁及以下</option>
                                    <option value="2">21-25</option>
                                    <option value="3">26-30</option>
                                    <option value="4">31-35</option>
                                    <option value="5">36-40</option>
                                    <option value="6">41-45</option>
                                    <option value="7">46-50</option>
                                    <option value="8">50岁以上</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="maritalStatus">婚姻状态：</label>
                                <select id="maritalStatus" name="maritalStatus" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">未婚</option>
                                    <option value="2">已婚</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="hasChildren">来访有无子女：</label>
                                <select id="hasChildren" name="hasChildren" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">有</option>
                                    <option value="2">无</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationField">咨询领域：</label>
                                <select id="consultationField" name="consultationField" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">心理健康</option>
                                    <option value="2">情绪压力</option>
                                    <option value="3">人际关系</option>
                                    <option value="4">恋爱情感</option>
                                    <option value="5">家庭关系</option>
                                    <option value="6">亲子教育</option>
                                    <option value="7">职场发展</option>
                                    <option value="8">个人成长</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="problemSummary">问题概述：</label>
                                <textarea id="problemSummary" name="problemSummary" class="form-control" required minlength="100" maxlength="500" rows="6" placeholder="请详细描述问题，至少100字"></textarea>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationKeywords">咨询关键词：</label>
                                <div class="keyword-tags">
                                    <div class="tag-item" data-value="焦虑">焦虑</div>
                                    <div class="tag-item" data-value="抑郁">抑郁</div>
                                    <div class="tag-item" data-value="强迫">强迫</div>
                                    <div class="tag-item" data-value="人际冲突">人际冲突</div>
                                    <div class="tag-item" data-value="躯体化">躯体化</div>
                                    <div class="tag-item" data-value="原生家庭">原生家庭</div>
                                    <div class="tag-item" data-value="绩效压力">绩效压力</div>
                                    <div class="tag-item" data-value="班组调整">班组调整</div>
                                </div>
                                <input type="hidden" id="consultationKeywords" name="consultationKeywords" value="">
                            </div>
                            <div class="form-group mb-3 form-inline">
                                <label>是否属于职场类相关问题：</label>
                                <div class="radio-group form-inline">
                                    <div class="custom-control custom-radio mr-1">
                                        <input type="radio" id="isWorkplaceIssue1" name="isWorkplaceIssue" class="custom-control-input" value="1" onchange="toggleWorkplaceDesc()">
                                        <label class="custom-control-label" for="isWorkplaceIssue1">是</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="isWorkplaceIssue0" name="isWorkplaceIssue" class="custom-control-input" value="0" onchange="toggleWorkplaceDesc()" checked>
                                        <label class="custom-control-label" for="isWorkplaceIssue0">否</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3" id="workplaceDescGroup" style="display: none;">
                                <label for="workplaceDescription">职场问题描述：</label>
                                <textarea id="workplaceDescription" name="workplaceDescription" class="form-control" row="5" maxlength="500"></textarea>
                            </div>
                            <div class="form-group mb-3 form-inline">
                                <label>来访是否有心理风险：</label>
                                <div class="radio-group form-inline">
                                    <div class="custom-control custom-radio mr-1">
                                        <input type="radio" id="hasPsychologicalRisk1" name="hasPsychologicalRisk" class="custom-control-input" value="1" onchange="toggleRiskDesc()">
                                        <label class="custom-control-label" for="hasPsychologicalRisk1">是</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="hasPsychologicalRisk0" name="hasPsychologicalRisk" class="custom-control-input" value="0" onchange="toggleRiskDesc()" checked>
                                        <label class="custom-control-label" for="hasPsychologicalRisk0">否</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3" id="riskDescGroup" style="display: none;">
                                <label for="riskDescription">风险程度简要描述：</label>
                                <textarea id="riskDescription" name="riskDescription" class="form-control" rows="5" maxlength="500"></textarea>
                            </div>
                            <div class="form-group mb-3">
                                <label for="followUpSuggestion">后续建议：</label>
                                <select id="followUpSuggestion" name="followUpSuggestion" class="form-control" required onchange="toggleOtherSuggestion()">
                                    <option value="">请选择</option>
                                    <option value="1">无需跟进</option>
                                    <option value="2">定期咨询</option>
                                    <option value="3">转介就医</option>
                                    <option value="4">其他</option>
                                </select>
                            </div>
                            <div class="form-group mb-3" id="otherSuggestionGroup" style="display: none;">
                                <label for="otherSuggestion">其他建议：</label>
                                <textarea id="otherSuggestion" name="otherSuggestion" class="form-control" rows="5" maxlength="500"></textarea>
                            </div>
                        </div>
                        <div class="card-footer">
                            <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                            <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        let id = getUrlParam("id");
        $(function () {
            //初始化页面
            initPage();
            $("#frmConsultationCase").validate({
                rules: {
                    loginName: { remote: {
                            type: "GET",
                            url: "/anteroom/user/getByLoginName",
                            dataType: "json",
                            contentType: "application/json",
                            data: {
                                loginName: function() {
                                    return $.trim($("#loginName").val());
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data === null || data === "") {
                                    return false;
                                }
                                else{
                                    let res = JSON.parse(data);
                                    $("#userId").val(res);
                                    return true;
                                }
                            }
                        }
                    },
                    consultationDate: { required: true },
                    consultationForm: { required: true },
                    consultationDuration: { required: true, number: true, min: 1 },
                    consultationType: { required: true },
                    visitorGender: { required: true },
                    visitorAge: { required: true },
                    maritalStatus: { required: true },
                    hasChildren: { required: true },
                    consultationField: { required: true },
                    problemSummary: { required: true, minlength: 100, maxlength: 500 },
                    followUpSuggestion: { required: true }
                },
                messages: {
                    loginName: {remote: "该来访者工号不存在"},
                    consultationDate: { required: "请选择咨询日期" },
                    consultationForm: { required: "请选择咨询形式" },
                    consultationDuration: { 
                        required: "请输入咨询时长",
                        number: "请输入有效的数字",
                        min: "咨询时长必须大于0"
                    },
                    consultationType: { required: "请选择咨询类型" },
                    visitorGender: { required: "请选择来访性别" },
                    visitorAge: { required: "请选择来访年龄" },
                    maritalStatus: { required: "请选择婚姻状态" },
                    hasChildren: { required: "请选择是否有子女" },
                    consultationField: { required: "请选择咨询领域" },
                    problemSummary: { 
                        required: "请填写问题概述",
                        minlength: "问题概述至少需要100字",
                        maxlength: "问题概述不能超过500字"
                    },
                    followUpSuggestion: { required: "请选择后续建议" }
                },
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": id,
                        "userId": $("#userId").val(),
                        "consultationDate": $("#consultationDate").val(),
                        "consultationForm": $("#consultationForm").val(),
                        "consultationDuration": $("#consultationDuration").val(),
                        "consultationType": $("#consultationType").val(),
                        "visitorGender": $("#visitorGender").val(),
                        "visitorAge": $("#visitorAge").val(),
                        "maritalStatus": $("#maritalStatus").val(),
                        "hasChildren": $("#hasChildren").val(),
                        "consultationField": $("#consultationField").val(),
                        "problemSummary": $("#problemSummary").val(),
                        "consultationKeywords": getCheckedKeywords(),
                        "isWorkplaceIssue": $("input[name='isWorkplaceIssue']:checked").val(),
                        "workplaceDescription": $("#workplaceDescription").val(),
                        "hasPsychologicalRisk": $("input[name='hasPsychologicalRisk']:checked").val(),
                        "riskDescription": $("#riskDescription").val(),
                        "followUpSuggestion": $("#followUpSuggestion").val(),
                        "otherSuggestion": $("#otherSuggestion").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/consultationcase/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });

        function initPage() {
            // 获取个案数据
            $.get("/counselingroom/consultationcase/get?id=" + id, function (data) {
                // 填充表单数据
                $("#loginName").val(data.loginName);
                $("#userId").val(data.userId);
                $("#consultationDate").val(data.consultationDate);
                $("#consultationForm").val(data.consultationForm);
                $("#consultationDuration").val(data.consultationDuration);
                $("#consultationType").val(data.consultationType);
                $("#visitorGender").val(data.visitorGender);
                $("#visitorAge").val(data.visitorAge);
                $("#maritalStatus").val(data.maritalStatus);
                $("#hasChildren").val(data.hasChildren);
                $("#consultationField").val(data.consultationField);
                $("#problemSummary").val(data.problemSummary);

                // 设置关键词
                if (data.consultationKeywords) {
                    const keywords = data.consultationKeywords.split(',');
                    keywords.forEach(keyword => {
                        $(`.tag-item[data-value="${keyword}"]`).addClass('selected');
                    });
                    updateKeywords();
                }

                // 设置职场问题
                if (data.isWorkplaceIssue === 1) {
                    $("#isWorkplaceIssue1").prop('checked', true);
                    $("#workplaceDescription").val(data.workplaceDescription);
                    toggleWorkplaceDesc();
                }

                // 设置心理风险
                if (data.hasPsychologicalRisk === 1) {
                    $("#hasPsychologicalRisk1").prop('checked', true);
                    $("#riskDescription").val(data.riskDescription);
                    toggleRiskDesc();
                }

                // 设置后续建议
                $("#followUpSuggestion").val(data.followUpSuggestion);
                if (data.followUpSuggestion === 4) {
                    $("#otherSuggestion").val(data.otherSuggestion);
                    toggleOtherSuggestion();
                }
            });

            // 初始化字数统计
            $('textarea').on('input', function() {
                const $textarea = $(this);
                const maxLength = $textarea.attr('maxlength');
                const currentLength = $textarea.val().length;
                const $counter = $textarea.siblings('.char-counter');
                
                if ($counter.length === 0) {
                    $textarea.after(`<span class="char-counter">${currentLength}/${maxLength}</span>`);
                } else {
                    $counter.text(`${currentLength}/${maxLength}`);
                }
            });

            // 初始化标签选择
            $('.tag-item').on('click', function() {
                $(this).toggleClass('selected');
                updateKeywords();
            });
        }

        function updateKeywords() {
            const selectedKeywords = [];
            $('.tag-item.selected').each(function() {
                selectedKeywords.push($(this).data('value'));
            });
            $('#consultationKeywords').val(selectedKeywords.join(','));
        }

        function getCheckedKeywords() {
            return $('#consultationKeywords').val();
        }

        function toggleWorkplaceDesc() {
            const isWorkplace = $('input[name="isWorkplaceIssue"]:checked').val();
            $('#workplaceDescGroup').toggle(isWorkplace === '1');
        }

        function toggleRiskDesc() {
            const hasRisk = $('input[name="hasPsychologicalRisk"]:checked').val();
            $('#riskDescGroup').toggle(hasRisk === '1');
        }

        function toggleOtherSuggestion() {
            const suggestion = $('select[name="followUpSuggestion"]').val();
            $('#otherSuggestionGroup').toggle(suggestion === '4');
        }
    </script>
</th:block>
</body>
</html>