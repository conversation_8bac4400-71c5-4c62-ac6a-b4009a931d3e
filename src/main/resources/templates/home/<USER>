<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* 表格样式已移至 style.css */
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <div class="row">
        <div class="col-12">
            <h4 class="mb-4">快捷功能</h4>
        </div> <!-- end col -->
    </div>
    <div class="row">
        <div class="col-lg-3 col-sm-3">
            <a th:href="@{/measuringroom/testing/index}">
                <div class="card text-white bg-info overflow-hidden">
                    <div class="card-body">
                        <div class="toll-free-box text-center">
                            <span><i class="fa fa-heartbeat mr-2 font-large"></i><b class="font-24">心理测试</b></span>
                        </div>
                    </div> <!-- end card-body-->
                </div>
            </a>
        </div> <!-- end col-->
        <div class="col-lg-3 col-sm-3">
            <a th:href="@{/counselingroom/counselingquestion/index}">
                <div class="card text-white bg-danger overflow-hidden">
                    <div class="card-body">
                        <div class="toll-free-box text-center">
                            <span> <i class="fa fa-commenting-o mr-2 font-large"></i><b class="font-24">问题答疑</b></span>
                        </div>
                    </div> <!-- end card-body-->
                </div>
            </a>
        </div> <!-- end col-->
        <div class="col-lg-3 col-sm-3">
            <a th:href="@{/counselingroom/counseling/order}">
                <div class="card text-white bg-warning overflow-hidden">
                    <div class="card-body">
                        <div class="toll-free-box text-center">
                            <span> <i class="fa fa-calendar mr-2 font-large"></i><b class="font-24">预约咨询</b></span>
                        </div>
                    </div> <!-- end card-body-->
                </div>
            </a>
        </div> <!-- end col-->
        <div class="col-lg-3 col-sm-3">
            <a th:href="@{/homepage/article(categoryId=1)}" target="_blank">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="toll-free-box text-center">
                            <span> <i class="fa fa-book mr-2 font-large"></i><b class="font-24">心理科普</b></span>
                        </div>
                    </div> <!-- end card-body-->
                </div>
            </a>
        </div> <!-- end col-->
    </div>
    <div class="row admin hide">
        <div class="col-12">
            <h4 class="mb-4">平台概况</h4>
        </div>
    </div>
    <div class="row admin hide">
        <div class="col-12">
            <div class="card widget-inline">
                <div class="card-body p-0">
                    <div class="row no-gutters">
                        <div class="col-sm-6 col-xl-3">
                            <div class="card shadow-none m-0">
                                <div class="card-body text-center">
                                    <i class="fa fa-user-o text-danger font-30"></i>
                                    <h3><span th:text="${statOfHomePage.visitorNum}"></span></h3>
                                    <p class="text-muted font-15 mb-0">来访者</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-xl-3">
                            <div class="card shadow-none m-0 border-left">
                                <div class="card-body text-center">
                                    <i class="fa fa-user-md text-primary font-30"></i>
                                    <h3><span th:text="${statOfHomePage.counselorNum}"></span></h3>
                                    <p class="text-muted font-15 mb-0">咨询师</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-xl-3">
                            <div class="card shadow-none m-0 border-left">
                                <div class="card-body text-center">
                                    <i class="fa fa-heartbeat text-warning font-30"></i>
                                    <h3><span th:text="${statOfHomePage.measuringRecordNum}"></span></h3>
                                    <p class="text-muted font-15 mb-0">测评次数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-xl-3">
                            <div class="card shadow-none m-0 border-left">
                                <div class="card-body text-center">
                                    <i class="fa fa-comments-o text-info font-30"></i>
                                    <h3><span th:text="${statOfHomePage.counselingNum}"></span></h3>
                                    <p class="text-muted font-15 mb-0">咨询次数</p>
                                </div>
                            </div>
                        </div>
                    </div> <!-- end row -->
                </div>
            </div> <!-- end card-box-->
        </div> <!-- end col-->
    </div>
    <div class="row admin hide">
        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3 font-weight-normal">最近一周平台测评量统计</h4>
                    <div class="chartjs-chart">
                        <div id="testChartContainer" style="height:280px;"></div>
                    </div>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div> <!-- end col -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3 font-weight-normal">最近一周平台咨询量统计</h4>
                    <div class="chartjs-chart">
                        <div id="counselingChartContainer" style="height:280px;"></div>
                    </div>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
    <div class="row visitor hide">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body">
                    <div class="float-right">
                        <a th:href="@{/measuringroom/task/my_tasks}" class="font-14">
                            查看更多
                        </a>
                    </div>
                    <h4 class="header-title">我的测评任务</h4>
                    <table class="table table-striped" id="myTaskList">
                        <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                    </table>
                </div> <!-- end card body-->
            </div> <!-- end card -->
        </div><!-- end col-->

        <div class="col-xl-12">
            <div class="card">
                <div class="card-body">
                    <div class="float-right">
                        <a th:href="@{/measuringroom/testing/get_my_records}" class="font-14">
                            查看更多
                        </a>
                    </div>
                    <h4 class="header-title">我的测评记录</h4>
                    <table class="table table-striped" id="tbRecordList">
                        <thead>
                        <tr>
                            <th>量表</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div> <!-- end card body-->
            </div> <!-- end card -->
        </div><!-- end col-->
    </div>
    <!-- modal.测评任务量表 start-->
    <div id="modal-scale" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="taskModelTitle"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-centered nowrap" id="tbScale">
                        <thead>
                        <tr>
                            <th>名称</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.测评任务量表 end-->
    <input type="hidden" id="hidTaskID" value="" />
    <input type="hidden" id="hidTaskType" value="" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            let roleId = '[[${user.role.roleId}]]';
            if(roleId === '3'){
                $(".visitor").removeClass('hide');
            }
            else{
                $(".admin").removeClass('hide');
            }
            initTestRecordCapacityChart();
            initCounselingCapacityChart();
            //我的测评任务
            initTaskDT();
            $("#myTaskList").on('click', '.scale', function () {
                let data = otableTask.row($(this).parents('tr')).data();
                $("#hidTaskID").val(data.id);
                $("#hidTaskType").val(data.taskType);
                $("#taskModelTitle").html('测评任务【' + '' + data.taskName +'】');
                initTaskScale(data.scales);
                $("#modal-scale").modal();
            });
            $("#tbScale").on('click', '.start', function () {
                let data = oTableScale.row($(this).parents('tr')).data();
                $.post('/measuringroom/testing/is_scale_done', { taskId: $("#hidTaskID").val(), scaleId: data.id }, function (res) {
                    if (res.id === 0 || (res.id !== 0 && res.state !== 1 && res.state !== 2)) {
                        location.href = '/measuringroom/testing/guide?taskId=' + $("#hidTaskID").val() + '&type=' + $("#hidTaskType").val() + '&scaleId=' + data.id + '&recordId=' + res.id + '';
                    }
                    else {
                        layer.msg("您已经完成该测试！", { icon: 0, time: 2000 });
                    }
                });
            });
            //我的测评记录
            initTestRecordList();
        });
        //统计每天测评量
        let dateArray = [];
        let testRecordCapacityArray = [];
        let testRecordCapacityChart;
        let initTestRecordCapacityChart = function () {
            dateArray.splice(0);
            testRecordCapacityArray.splice(0);
            let  list = '[[${testRecordCapacitys}]]';
            list = list.replace(/&quot;/g,"\"")
            list = JSON.parse(list);

            for (let jsonObj in list) {
                dateArray.push(list[jsonObj].dt.substring(5,10));
                testRecordCapacityArray.push(list[jsonObj].capacity);
            }
            //统计图表：每日测评量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            counselingchart = Highcharts.chart('testChartContainer', {
                chart: {
                    type: 'areaspline'
                },
                title: {
                    text: '',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                credits: { enabled: false },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: dateArray,
                    labels: { style: { color: '#000' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#000' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '测评量',
                    color: '#727cf5',
                    data: testRecordCapacityArray
                }
                ]
            });
        };
        //统计每天咨询量
        let counselingCapacityArray = [];
        let counselingCapacityChart;
        let initCounselingCapacityChart = function () {
            dateArray.splice(0);
            counselingCapacityArray.splice(0);
            let list = '[[${counselingCapacitys}]]';
            list = list.replace(/&quot;/g,"\"")
            list = JSON.parse(list);
            for (var jsonObj in list) {
                dateArray.push(list[jsonObj].dt.substring(5,10));
                counselingCapacityArray.push(list[jsonObj].capacity);
            }
            //统计图表：每日咨询量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            counselingCapacityChart = Highcharts.chart('counselingChartContainer', {
                chart: {
                    type: 'areaspline'
                },
                title: {
                    text: '',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                credits: { enabled: false },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: dateArray,
                    labels: { style: { color: '#000' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#000' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '咨询量',
                    color: '#727cf5',
                    data: counselingCapacityArray
                }
                ]
            });
        };
        let otableTask;
        let initTaskDT = function () {
            let columns = [
                { "data": "taskName", "bSortable": false },
                {"data": "startTime", "bSortable": false},
                {"data": "endTime", "bSortable": false}
            ];
            let columnDefs_task = [
                {
                    targets: 3,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        let startDate = row.startTime;
                        let endDate = row.endTime;
                        if (startDate > getDateNowFormat()) {
                            labels = '<span class="badge badge-light">未开始</span>';
                        }
                        if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                            labels = '<span class="badge badge-success">进行中</span>';
                        }
                        if (getDateNowFormat() >= endDate) {
                            labels = '<span class="badge badge-warning">已结束</span>';
                        }
                        return labels;
                    }
                },
                {
                    targets: 4,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        let startDate = row.startTime;
                        let endDate = row.endTime;
                        if (startDate > getDateNowFormat()) {
                            labels += '<span class="badge badge-danger">测评时间未到</span>';
                        }
                        if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                            labels += '<button class="btn btn-outline-success btn-rounded btn-sm scale" title="参与测评"><i class="fa fa-angle-double-right"></i></button>';
                        }
                        return labels;
                    }
                }
            ];
            let getQueryCondition = function (data) {
                let param = {};
                param.taskName = "";
                //组装分页参数
                $.each(data, function (i, item) {
                    if (item.name === "iDisplayLength") {
                        param.pageSize = item.value;
                    }
                    if (item.name === "iDisplayStart") {
                        param.pageIndex = item.value;
                    }
                });
                return param;
            };
            $("#myTaskList").bsDataTables({
                columns: columns,
                url: '/measuringroom/task/my_tasks',
                columnDefs: columnDefs_task,
                paging: true,
                pageLength: 3,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                            $(".dataTables_info").hide();
                            $(".dataTables_paginate").hide();
                        }
                    });
                }
            });
            otableTask = oTable;
        };
        let oTableScale = null;
        let initTaskScale = function (scales) {
            if (oTableScale != null) {
                oTableScale.destroy();
            }
            oTableScale = $("#tbScale").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "data": scales,
                "columns": [
                    { "data": "scaleName", "bSortable": false }
                ],
                "columnDefs": [
                    {
                        targets: 1,
                        render: function (data, type, row, meta) {
                            return '<button class="btn btn-outline-success btn-sm start"><i class="fa fa-chevron-circle-right mr-1"></i>开始测评</button>';
                        }
                    }
                ],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        let getQueryCondition_testrecord = function (data) {
            let param = {};
            param.userId = '[[${user.userId}]]';
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        var columns_testrecord = [
            { "data": "scaleName", "bSortable": false },
            { "data": "startTime","bSortable": false},
            { "data": "endTime", "bSortable": false},
            { "data": "state", "bSortable": false,
                render: function (data, type, row, meta) {
                    switch (data) {
                        case 0:
                            return '<span class="badge badge-secondary-lighten badge-pill">未完成</span>';
                            break;
                        case 1:
                            if (row.scaleId === 10000163) {
                                return '<span class="badge badge-info badge-pill mr-2">完成</span><span class="badge badge-info badge-pill cursor-pointer mr-2"><a class="text-white" href="/measuringroom/testing/report_aqy?recordId=' + row.id + '" target="_blank">测评报告</a></span>';
                            }
                            else if (row.scaleId === 10000167) {
                                return '<span class="badge badge-info badge-pill mr-2">完成</span><span class="badge badge-info badge-pill cursor-pointer mr-2"><a class="text-white" href="/measuringroom/testing/report_yys?recordId=' + row.id + '" target="_blank">测评报告</a></span>';
                            }
                            else {
                                return '<span class="badge badge-info badge-pill mr-2">完成</span><span class="badge badge-info badge-pill cursor-pointer mr-2"><a class="text-white" href="/measuringroom/testing/report?recordId=' + row.id + '" target="_blank">测评报告</a></span>';
                            }
                            break;
                        case 2:
                            return '<span class="badge badge-danger badge-pill mr-1">测谎未通过</span>';
                            break;
                    }
                }
            }];
        let initTestRecordList = function () {
            $("#tbRecordList").bsDataTables({
                columns: columns_testrecord,
                url: '/measuringroom/testing/get_my_records',
                columnDefs: [],
                pageLength: 3,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_testrecord(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                            $(".dataTables_info").hide();
                            $(".dataTables_paginate").hide();
                        }
                    });
                }
            });
        };
    </script>
</th:block>
</body>
</html>