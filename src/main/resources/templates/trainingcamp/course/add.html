<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">心理训练营</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">课程管理</a></li>
                        <li class="breadcrumb-item active">添加课程</li>
                    </ol>
                </div>
                <h4 class="page-title">添加训练营课程</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <h5 class="mb-3 card-header bg-light"><i class="fa fa-plus mr-1"></i>添加课程</h5>
                <div class="card-body">
                    <form id="frmCourse" class="form-horizontal col-8">
                        <div class="form-group row mb-3">
                            <label for="courseName">课程名称</label>
                            <input type="text" id="courseName" name="courseName" class="form-control" autocomplete="off" maxlength="20" />
                        </div>
                        <div class="form-group row mb-3">
                            <label for="courseType">课程类型</label>
                            <select class="form-control" name="courseType" id="courseType">
                                <option value="">请选择</option>
                                <option value="1">视频</option>
                                <option value="2">音频</option>
                                <option value="3">文章</option>
                            </select>
                        </div>
                        <div class="form-group row mb-3" id="div-delivery">
                            <label for="deliveryPlatform">课程交付平台</label>
                            <select class="form-control" name="deliveryPlatform" id="deliveryPlatform">
                                <option value="1" selected>平台内部</option>
                                <option value="2">第三方平台</option>
                            </select>
                        </div>
                        <div class="form-group row mb-3 hide" id="div-url">
                            <label for="url">外部资源URL地址</label>
                            <input type="text" class="form-control" name="url" id="url" />
                        </div>
                        <div class="form-group row hide" id="div-attachment">
                            <label>上传课件</label>
                            <input type="file" name="file" id="txt_file" class="file-loading" />
                            <input type="hidden" name="file" id="filename" value="" />
                        </div>
                        <div class="form-group row hide" id="div-article">
                            <label>文章内容</label>
                            <div id="article" class="editor"></div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="mr-2">是否开启评论</label>
                            <div class="custom-control custom-switch pl-0">
                                <input type="checkbox" id="commentEnabled" data-switch="success" />
                                <label for="commentEnabled" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group mb-0 row">
                            <input type="submit" id="btnSave" class="btn btn-primary" value="保存" />
                            <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script type="text/javascript">
        let editor_article;
        //初始化fileinput：课程附件
        let fileInputCategory = function () {
            let oFile = {};
            oFile.Init = function (ctrlName, hidCtrlName, flag) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=course',
                    allowedFileExtensions: ['mp3', 'wav', 'm4a', 'mp4', 'mpg', 'wmv'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    maxFileSize: 0,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $('#' + ctrlName).on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#" + hidCtrlName).val(res.resultMsg);
                        layer.msg("上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
        $(function () {
            // 创建 ClassicEditor 实例
            ClassicEditor
                .create(document.querySelector('#article'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_article = editor;
                });
            let oFileInput = new fileInputCategory();
            oFileInput.Init("txt_file", "filename", "course");
            $("#courseType").on('change',function () {
                if ($(this).val() === '1' || $(this).val() === '2'){
                    $("#div-delivery").show();
                    if($("#deliveryPlatform").val()==='1'){ //平台内部
                        $("#div-attachment").removeClass('hide').addClass('show');
                        $("#div-article").removeClass('show').addClass('hide');
                    }
                    else{
                        $("#div-attachment").removeClass('show').addClass('hide');
                        $("#div-article").removeClass('show').addClass('hide');
                        $("#div-url").removeClass('hide').addClass('show');
                    }
                }
                if($(this).val() === '3' ){
                    $("#div-delivery").hide();
                    $("#div-article").removeClass('hide').addClass('show');
                    $("#div-attachment").removeClass('show').addClass('hide');
                    $("#div-url").removeClass('show').addClass('hide');
                }
            });
            $("#deliveryPlatform").on('change',function (){
                if($(this).val()==='1'){
                    $("#div-attachment").removeClass('hide').addClass('show');
                    $("#div-url").removeClass('show').addClass('hide');
                }
                if($(this).val()==='2'){
                    $("#div-attachment").removeClass('show').addClass('hide');
                    $("#div-url").removeClass('hide').addClass('show');
                }
            });
            $("#frmCourse").validate({
                rules: {
                    courseName: {required: true},
                    courseType: {required: true},
                },
                messages: {
                    courseName: {required: "请填写课程名称"},
                    courseType: {required: "请选择课程类型"},
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.courseName = $("#courseName").val();
                    jsonObj.courseType = $("#courseType").val();
                    jsonObj.commentEnabled = $("#commentEnabled").prop("checked") ? 1 : 0;
                    jsonObj.deliveryPlatform = $("#deliveryPlatform").val();
                    jsonObj.attachmentType = $("#courseType").val();
                    jsonObj.attachmentFileName = $("#filename").val();
                    jsonObj.articleContent = editor_article.getData();
                    let btnSave = $("#btnSave");
                    btnSave.val("请稍后……");
                    btnSave.attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/trainingcamp/course/add',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            btnSave.val("保存");
                            btnSave.attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert("操作成功", { icon: 1, closeBtn: 0 }, function () {
                                    location.href = '/trainingcamp/course/list';
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>