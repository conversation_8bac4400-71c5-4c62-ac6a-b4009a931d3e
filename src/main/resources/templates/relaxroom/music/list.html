<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style type="text/css">
        #player {
            width: 575px;
            height: 150px;
            position: relative;
            margin-bottom: 20px;
        }

        #player .cover {
            position: absolute;
            top: 0px;
            overflow: hidden;
            height: 150px;
        }

        #player .cover img {
            height: 150px;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }

        #player .player_ctrl {
            height: 150px;
            margin-left: 225px;
            line-height: 14px;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        #player .player_ctrl .tag strong, #player .player_ctrl .tag span {
            display: block;
            text-overflow: ellipsis;
        }

        #player .player_ctrl .tag span {
            font-size: 12px;
            margin-top: 5px;
            color: #ccc;
        }

        #player .player_ctrl .icon_repeat {
            background-repeat: no-repeat;
            background-position: center;
            display: inline-block;
            opacity: 0.6;
            cursor: pointer;
            width: 24px;
            height: 24px;
            -moz-transition: 0.3s;
            -webkit-transition: 0.3s;
            -o-transition: 0.3s;
            transition: 0.3s;
            -moz-user-select: none;
            -khtml-user-select: none;
            -webkit-user-select: none;
            -o-user-select: none;
            user-select: none;
        }

        #player .player_ctrl .icon_repeat:hover, #player .player_ctrl .icon_repeat.player_enable {
            opacity: 1;
        }

        #player .player_ctrl .icon_repeat:active {
            opacity: 0.3;
        }

        #player .player_ctrl .control {
            margin-top: 0px;
            height: 25px;
        }

        #player .player_ctrl .control .volume .player_slider {
            margin-top: 5px;
            margin-left: 2px;
            width: 100px;
        }

        #player .player_ctrl .player_progress {
            margin-top: 0px;
        }

        #player .player_ctrl .player_progress .timer {
            font-size: 12px;
            color: #ccc;
            margin-top: 8px;
        }

        #player .player_ctrl .player_progress .player_repeat, #player .player_ctrl .player_progress .shuffle {
            background-position: center bottom;
            color: #ccc;
            margin-top: 8px;
        }

        #player .player_ctrl .player_progress .player_repeat.player_once, #player .player_ctrl .player_progress .player_repeat.player_all {
            opacity: 1;
        }

        #player .player_ctrl .player_progress .player_repeat.player_once {
            position: relative;
        }

        #player .player_ctrl .player_progress .player_repeat.player_once:before {
            content: "1";
            position: absolute;
            top: -2px;
            right: -2px;
            font-size: 8px;
        }

        .player_slider {
            background: #dddcdc;
            height: 5px;
            position: relative;
            cursor: pointer;
            -moz-border-radius: 5px;
            -webkit-border-radius: 5px;
            -o-border-radius: 5px;
            -ms-border-radius: 5px;
            -khtml-border-radius: 5px;
            border-radius: 5px;
        }

        .player_slider:hover a, .player_slider.player_enable a {
            opacity: 1;
        }

        .player_slider a {
            background: #fff;
            margin-left: -2.5px;
            position: absolute;
            opacity: 0;
            width: 6px;
            height: 6px;
            -moz-border-radius: 4px;
            -webkit-border-radius: 4px;
            -o-border-radius: 4px;
            -ms-border-radius: 4px;
            -khtml-border-radius: 4px;
            border-radius: 50%;
            -moz-transition: opacity 0.3s;
            -webkit-transition: opacity 0.3s;
            -o-transition: opacity 0.3s;
            transition: opacity 0.3s;
        }

        .player_slider .loaded, .player_slider .pace {
            position: absolute;
            height: 100%;
            opacity: 0.7;
            -moz-border-radius: 4px;
            -webkit-border-radius: 4px;
            -o-border-radius: 4px;
            -ms-border-radius: 4px;
            -khtml-border-radius: 4px;
            border-radius: 4px;
        }

        .player_slider .loaded {
            background: #ccc;
        }

        .player_slider .pace {
            background: #b0aeae;
        }

        #playlist {
            background: #fff;
            list-style: none;
        }

        #playlist li {
            color: #787879;
            font-size: 14px;
            line-height: 2;
            padding-left: 25px;
            cursor: pointer;
            text-overflow: ellipsis;
            -moz-transition: 0.3s;
            -webkit-transition: 0.3s;
            -o-transition: 0.3s;
            transition: 0.3s;
        }

        #playlist li:hover {
            color: #6b5eae;
        }

        #playlist li.playing {
            background: url(/static/images/playing.png) no-repeat 0 center;
            color: #6b5eae;
            font-weight: bold;
        }

        .delete {
            display: none;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">放松室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">放松音乐</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-music mr-1"></i>放松音乐</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row mb-2">
        <div class="col-8">
            <div class="text-lg-left form-inline">
                <th:block th:if="${canAdd eq true}">
                    <a th:href="@{/relaxroom/music/add(id=${album.Id})}" class="btn btn-danger mb-2 mr-1"><i class="fa fa-plus mr-1"></i>上传音乐</a>
                </th:block>
                <a href="#" onclick="javascript:history.go(-1);" class="btn btn-outline-warning mb-2"><i class="fa fa-mail-reply mr-1"></i>返回专辑列表</a>
            </div>
        </div>
        <div class="col-lg-4">
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div id="player">
                <div class="cover">
                    <th:block th:if="${album.coverImg eq null or album.coverImg eq ''}">
                        <img class="img-fluid" th:src="@{/static/images/nopic.png}" alt="" />
                    </th:block>
                    <th:block th:unless="${album.coverImg eq null or album.coverImg eq ''}">
                        <img class="img-fluid" th:src="|@{/static/upload/music_cover/thumbnail/}${album.coverImg}|" />
                    </th:block>
                </div>
                <div class="player_ctrl bg-warning p-2">
                    <div class="tag mt-1 mb-3">
                        <strong class="text-white font-15 current-play mb-2"></strong>
                        <span class="text-white album">专辑：<th:block th:text="${album.albumName}" /> </span>
                    </div>
                    <div class="control">
                        <div class="pull-left">
                            <div class="rewind icon_repeat"><i class="fa fa-backward text-white"></i></div>
                            <div class="playback icon_repeat"><i class="fa fa-play text-white"></i></div>
                            <div class="fastforward icon_repeat"><i class="fa fa-forward text-white"></i></div>
                        </div>
                        <div class="volume pull-right">
                            <div class="mute icon_repeat pull-left"><i class="fa fa-volume-up text-white"></i></div>
                            <div class="player_slider pull-left">
                                <div class="pace"></div>
                            </div>
                        </div>
                    </div>
                    <div class="player_progress">
                        <div class="player_slider">
                            <div class="loaded"></div>
                            <div class="pace"></div>
                        </div>
                        <div class="timer pull-left text-white">0:00</div>
                        <div class="pull-right">
                            <div class="player_repeat icon_repeat text-white"><i class="fa fa-repeat"></i></div>
                            <div class="shuffle icon_repeat text-white"><i class="fa fa-random"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card">
                <!-- Start .panel -->
                <div class="card-header bg-light pl-2 pt-1 pb-1">
                    <h5><i class="fa fa-list-ul mr-1"></i>曲目列表</h5>
                </div>
                <div class="card-body">
                    <th:block th:if="${album.listMusic eq null or album.listMusic.size() eq 0}">
                        <div class="title"><i class="fa fa-info-circle mr-1"></i>该专辑还没有曲目，前去<a th:href="@{/relaxroom/music/add(id=${album.id})}" class="text-primary pl-1 pr-1 font-weight-bold"><i class="fa fa-upload mr-1"></i>上传</a>吧！</div>
                    </th:block>
                    <th:block th:unless="${album.listMusic eq null or album.listMusic.size() eq 0}">
                        <ul id="playlist">
                            <th:block th:each="music:${album.listMusic}">
                                <li>
                                    <th:block th:text="${music.musicName}"></th:block>
                                    <th:block th:if="${canDelete eq true}">
                                        <span class="pull-right delete" th:onclick="|del(${music.id})|"><i class="fa fa-trash text-danger" title="删除"></i></span>
                                    </th:block>
                                </li>
                            </th:block>
                        </ul>
                    </th:block>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/vendor/jquery-ui-1.10.4.min.js}"></script>
    <script type="text/javascript">
        let playList ={};
        let playListSize = [[${album.listMusic.size()}]];
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").removeClass('hide').addClass('show') : "";
            $.getJSON("/relaxroom/music/get?albumId="+getUrlParam('albumId'),function(res){
                playList= res.listMusic;
                loadMusic(0);
                $(".current-play").html(playList[0].musicName);
                $('#playlist li').each(function (i) {
                    let _i = i;
                    $(this).on('click', function () {
                        switchTrack(_i);
                        $(".current-play").html(playList[i].musicName);
                    });
                });
            });
        };
        let loadMusic = function (i) {
            let newaudio = $('<audio class="muted">').html('<source src="/static/upload/music/'+playList[i].fileName+'">').appendTo('#player');
            $('#playlist li').removeClass('playing').eq(i).addClass('playing');
            audio = newaudio[0];
            audio.volume = $('.mute').hasClass('player_enable') ? 0 : volume;
            audio.addEventListener('progress', beforeLoad, false);
            audio.addEventListener('durationchange', beforeLoad, false);
            audio.addEventListener('canplay', afterLoad, false);
            audio.addEventListener('ended', ended, false);
        };
        // Settings
        let repeat = localStorage.repeat || 0,
            shuffle = localStorage.shuffle || 'false',
            continous = true,
            autoplay = false;
        let time = new Date(),
            currentTrack = shuffle === true ? time.getTime() % playListSize : 0,
            trigger = false,
            audio, timeout, isPlaying = true, playCounts;

        let play = function () {
            audio.play();
            $('.playback').addClass('playing');
            $('.playback').html("<i class='fa fa-pause text-white'></i>");
            timeout = setInterval(updateProgress, 500);
            isPlaying = true;
        }
        let pause = function () {
            audio.pause();
            $('.playback').removeClass('playing');
            $('.playback').empty();
            $('.playback').html("<i class='fa fa-play text-white'></i>");
            clearInterval(updateProgress);
            isPlaying = false;
        }
        let setProgress = function (value) {
            let currentSec = parseInt(value % 60) < 10 ? '0' + parseInt(value % 60) : parseInt(value % 60),
                ratio = value / audio.duration * 100;

            $('.timer').html(parseInt(value / 60) + ':' + currentSec);
            $('.player_progress .pace').css('width', ratio + '%');
            $('.player_progress .player_slider a').css('left', ratio + '%');
        }
        let updateProgress = function () {
            setProgress(audio.currentTime);
        }
        // Progress slider
        $('.player_progress .player_slider').slider({
            step: 0.1, slide: function (event, ui) {
                $(this).addClass('player_enable');
                setProgress(audio.duration * ui.value / 100);
                clearInterval(timeout);
            }, stop: function (event, ui) {
                audio.currentTime = audio.duration * ui.value / 100;
                $(this).removeClass('player_enable');
                timeout = setInterval(updateProgress, 500);
            }
        });
        // Volume slider
        let setVolume = function (value) {
            audio.volume = localStorage.volume = value;
            $('.volume .pace').css('width', value * 100 + '%');
            $('.volume .player_slider a').css('left', value * 100 + '%');
        }
        let volume = localStorage.volume || 0.5;
        $('.volume .player_slider').slider({
            max: 1, min: 0, step: 0.01, value: volume, slide: function (event, ui) {
                setVolume(ui.value);
                $(this).addClass('player_enable');
                $('.mute').removeClass('player_enable');
            }, stop: function () {
                $(this).removeClass('player_enable');
            }
        }).children('.pace').css('width', volume * 100 + '%');

        $('.mute').click(function () {
            if ($(this).hasClass('player_enable')) {
                setVolume($(this).data('volume'));
                $(this).removeClass('player_enable');
                $(this).empty();
                $(".mute").html("<i class='fa fa-volume-up'></i>");
            } else {
                $(this).data('volume', audio.volume).addClass('player_enable');
                $(".mute").empty();
                $(".mute").html("<i class='fa fa-volume-off'></i>");
                setVolume(0);
            }
        });

        // Switch track
        let switchTrack = function (i) {
            if (i < 0) {
                track = currentTrack = playListSize - 1;
            } else if (i >= playListSize) {
                track = currentTrack = 0;
            } else {
                track = i;
            }

            $('audio').remove();
            loadMusic(track);
            if (isPlaying === true) play();
        }

        // Shuffle
        let shufflePlay = function () {
            let time = new Date(),
                lastTrack = currentTrack;
            currentTrack = time.getTime() % playListSize;
            if (lastTrack === currentTrack)++currentTrack;
            switchTrack(currentTrack);
        }

        // Fire when track ended
        let ended = function () {
            pause();
            audio.currentTime = 0;
            playCounts++;
            if (continous === true) isPlaying = true;
            if (repeat === 1) {
                play();
            } else {
                if (shuffle === true) {
                    shufflePlay();
                } else {
                    if (repeat === 2) {
                        switchTrack(++currentTrack);
                    } else {
                        if (currentTrack < playListSize) switchTrack(++currentTrack);
                    }
                }
            }
        }

        let beforeLoad = function () {
            let endVal = this.seekable && this.seekable.length ? this.seekable.end(0) : 0;
            $('.player_progress .loaded').css('width', (100 / (this.duration || 1) * endVal) + '%');
        }

        // Fire when track loaded completely
        let afterLoad = function () {
            if (autoplay === true) play();
        }

        $('.playback').on('click', function () {
            if ($(this).hasClass('playing')) {
                pause();
            } else {
                play();
            }
        });
        $('.rewind').on('click', function () {
            if (shuffle === true) {
                shufflePlay();
            } else {
                switchTrack(--currentTrack);
            }
        });
        $('.fastforward').on('click', function () {
            if (shuffle === true) {
                shufflePlay();
            } else {
                switchTrack(++currentTrack);
            }
        });

        if (shuffle === 'true') $('.shuffle').addClass('player_enable');
        if (repeat === 1) {
            $('.player_repeat').addClass('player_once');
        } else if (repeat === 2) {
            $('.player_repeat').addClass('player_all');
        }

        $('.player_repeat').on('click', function () {
            if ($(this).hasClass('player_once')) {
                repeat = localStorage.repeat = 2;
                $(this).removeClass('player_once').addClass('player_all');
            } else if ($(this).hasClass('player_all')) {
                repeat = localStorage.repeat = 0;
                $(this).removeClass('player_all');
            } else {
                repeat = localStorage.repeat = 1;
                $(this).addClass('player_once');
            }
        });

        $('.shuffle').on('click', function () {
            if ($(this).hasClass('player_enable')) {
                shuffle = localStorage.shuffle = 'false';
                $(this).removeClass('player_enable');
            } else {
                shuffle = localStorage.shuffle = 'true';
                $(this).addClass('player_enable');
            }
        });
        let del = function (obj) {
            layer.confirm('确定删除吗？', {
                time: 0,
                icon: 7,
                btn: ['确定', '取消'],
                yes: function (index) {
                    $.post("/relaxroom/music/delete?id=" + obj, '', function (res) {
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            location.reload();
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }, 'json');
                }
            });
        };
        $(function () {
            initPage();
            $("#playlist").on("mouseover", "li", function () {
                if ('[[${canDelete}]]' === 'true') {
                    $(this).find("span").removeClass("hide").addClass("show");
                }
            });
            $("#playlist").on("mouseout", "li", function () {
                $(this).find("span").removeClass("show").addClass("hide");
            });
        });
    </script>
</th:block>
</body>
</html>