<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">放松室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">文章管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">文章管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group">
                                    <select id="sr-category" class="form-control mr-1">
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbArticle" class="table table-striped nowrap" width="100%">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th align="left">栏目名称</th>
                                <th align="left">标题</th>
                                <th align="left">是否banner</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.文章管理 start -->
    <div id="article-modal" class="modal fade" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="modal-article-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmArticle" class="pl-3 pr-3" action="#">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="category">栏目</label>
                            <select id="category" name="category" class="form-control">
                                <option></option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="title">标题</label>
                            <input id="title" name="title" class="form-control" type="text" maxlength="25" aria-describedby="titleHelp">
                            <small id="titleHelp" class="form-text text-muted"><i class="fa fa-info-circle mr-1"></i>限制25个字数以内</small>
                        </div>
                        <div class="form-group">
                            <label for="editor_content">内容</label>
                            <div id="editor_content"></div>
                        </div>
                        <div class="form-group thumbnail hide">
                            <img id="thumbnail" th:src="@{/static/images/nopic.png}" class="img-responsive" style="width:200px;" />
                        </div>
                        <div class="form-group">
                            <label>封面缩略图</label>
                            <input type="file" name="file" id="txt_file" class="file-loading" />
                            <small class="text-secondary"><i class="fa fa-info-circle mr-1"></i>建议像素大小：1000*600px</small>
                            <input id="hidThumbnail" type="hidden" value="" />
                        </div>
                        <div class="form-group form-inline">
                            <label>设为首页轮播图</label>
                            <div class="form-group">
                                <input type="checkbox" id="isBanner" data-switch="danger" />
                                <label for="isBanner" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group banner-thumbnail hide">
                            <img id="thumbnail_banner" th:src="@{/static/images/nopic.png}" class="img-responsive" style="width:200px;" />
                        </div>
                        <div class="form-group banner-thumbnail hide">
                            <label>上传轮播图</label>
                            <input type="file" name="file" id="txt_file_banner" class="file-loading" />
                            <input id="hidBanner" type="hidden" value="" />
                        </div>
                        <input id="hidID" type="hidden" value="0" />
                        <input type="reset" hidden />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                        <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                    </div>
                </form>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.文章管理 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let editor_content;
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbArticle').DataTable().ajax.reload();
            });
            //datatables
            $("#tbArticle").bsDataTables({
                columns: columns,
                url: '/relaxroom/article/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //新增
            $("#btnAdd").click(function () {
                resetForm();
                $("#modal-article-title").html('<i class="fa fa-plus mr-1"></i>新增文章');
                $("#article-modal").modal();
            });
            //修改
            $("#tbArticle").on('click', '.update', function () {
                resetForm();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidID").val(data.id);
                $("#category").val(data.categoryId);
                $("#title").val(data.title);
                editor_content.setData(data.content);
                $("#thumbnail").attr("src", data.coverImg == "" ? "/static/images/nopic.png" : "/static/upload/article/thumbnail/large/" + data.coverImg);
                $(".thumbnail").removeClass('hide').addClass('show');
                $("#hidThumbnail").val(data.coverImg);
                $("#modal-article-title").html('<i class="fa fa-pencil-square-o mr-1"></i>修改文章');
                if (data.isBanner === 1) {
                    $("#isBanner").attr("checked", true);
                    $(".banner-thumbnail").removeClass('hide').addClass('show');
                }
                else {
                    $("#isBanner").attr("checked", false);
                    $(".banner-thumbnail").removeClass('show').addClass('hide');
                }
                $("#thumbnail_banner").attr("src", data.banner == "" ? "/static/images/nopic.png" : "/static/upload/banner/" + data.banner);
                $("#article-modal").modal();
            });
            //删除
            $("#tbArticle").on('click', '.delete', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.id = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/relaxroom/article/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids == "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/relaxroom/article/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //全选
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });

            $("#frmArticle").validate({
                rules: {
                    category:{ required: true },
                    title: { required: true }
                },
                messages: {
                    category:{ required: "请选择栏目" },
                    title: { required: "请填写标题" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": $("#hidID").val(),
                        "title": $.trim($("#title").val()),
                        "content": editor_content.getData(),
                        "categoryId": $("#category").val(),
                        "coverImg": $("#hidThumbnail").val(),
                        "isBanner": $("#isBanner").prop("checked") ? 1 : 0,
                        "banner": $("#hidBanner").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    let url = $("#hidID").val() == "0" ? "/relaxroom/article/add" : "/relaxroom/article/update";
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#article-modal").modal('hide');
                                    oTable.draw();
                                    layer.closeAll();
                                    $(".fileinput-remove-button").click();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#isBanner").on("change",function () {
                if ($(this).prop("checked")) {
                    $(".banner-thumbnail").removeClass("hide").addClass("show");
                }
                else {
                    $(".banner-thumbnail").removeClass("show").addClass("hide");
                }
            });
        });
        let resetForm = function () {
            $("#frmArticle input").removeClass("error");
            $("label.error").hide();
            $("#frmArticle input[type='reset']").click();
            $("#thumbnail").removeClass('hide').addClass('show');
            $("#thumbnail").attr("src", '/static/images/nopic.png');
            $(".banner-thumbnail").removeClass('show').addClass('hide');
            $("#thumbnail_banner").attr("src", '/static/images/nopic.png');
            $("#hidID").val(0);
            editor_content.setData("");
        };
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
            ClassicEditor
                .create(document.querySelector('#editor_content'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_content = editor;
                });
            initSelect("#sr-category", "/relaxroom/articlecategory/get_for_select", "", "", "选择栏目");
            let oFileInput = new FileInput();
            oFileInput.Init("txt_file");
            let oFileInputBanner = new bannerFileInput();
            oFileInputBanner.Init("txt_file_banner");
            initSelect("#category", "/relaxroom/articlecategory/get_for_select", "");
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "categoryName", "bSortable": false },
            { "data": "title", "bSortable": false },
            { "data": "isBanner", "render":
                    function (data, type, full, meta) {
                        if (full.isBanner === 1) {
                            return '<span class="badge badge-success badge-pill">是</span>';
                        }
                        else {
                            return '<span class="badge badge-light badge-pill">否</span>';
                        }
                    }, "bSortable": false
            }
        ];
        let columnDefs = [{
            targets: 4, render: function (data, type, row, meta) {
                let buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                if ('[[${canDelete}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-danger btn-sm mr-1 delete"><i class="fa fa-trash-o mr-1"></i>删除</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.categoryId = $("#sr-category").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let FileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=article',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-secondary", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    maxFileSize: 100,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidThumbnail").val(res.resultMsg);
                        $("#thumbnail").attr("src", res.resultMsg == "" ? "/static/images/nopic.png" : "/static/upload/article/thumbnail/large/" + res.resultMsg);
                        $(".thumbnail").removeClass('hide').addClass('show');
                        layer.msg("上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
        let bannerFileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=banner',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-secondary", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file_banner").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidBanner").val(res.resultMsg);
                        $("#thumbnail_banner").attr("src", res.resultMsg == "" ? "/static/images/nopic.png" : "/static/upload/banner/" + res.resultMsg);
                        $(".banner-thumbnail").removeClass('hide').addClass('show');
                        layer.msg("上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
    </script>
</th:block>
</body>
</html>