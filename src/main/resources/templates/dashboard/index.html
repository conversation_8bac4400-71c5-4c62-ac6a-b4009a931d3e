<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head th:fragment="common_header">
    <title  th:text="|数据看板 - ${ pageData.sysConfigDto.platformName }|"></title>
    <link rel="shortcut icon" th:href="@{/static/images/favicon.ico}">
    <link th:href="@{/static/dashboard/css/style.css}" rel="stylesheet"/>
</head>
<body>
<div class="bnt">
    <h1 class="tith1 fl" style="font-size:32px;" th:text="${pageData.sysConfigDto.platformName}"></h1>
</div>
<!-- bnt end -->
<div class="puleft fl">
    <div class="pulefttop">
        <h2 class="tith2"><span>最近一周咨询量</span></h2>
        <div class="box pbox" style="margin-top:20px;">
            <div class="lefttoday_bar pulefttoday_bar fl">
                <div class="puleftbox2bott_cont" id="counselingChart"></div>
            </div>
            <div class="pvr fr pulefttoday_bar2">
                <div class="puleftbox2bott_cont" id="counselingTypeChart"></div>
            </div>
        </div>
        <!-- lefttoday_number end -->
    </div>
    <div class="puleftboxtmidd">
        <h2 class="tith2">最近30天测评量</h2>
        <div class="box pbox" style="margin-top:20px;">
            <div class="purightboxtopcont" id="testRecordCapacityChart" style="height: 100%; margin-left: 20px;  width:95%;"></div>
        </div>
    </div>
    <div class="puleftboxtbott">
        <h2 class="tith2 pt1">最近一周在线答疑量</h2>
        <div class="box pbox" style="margin-top: 20px;">
            <div class="purightboxtopcont" id="counselingQuestionCapacityChart" style="height: 100%; margin-left: 20px;  width:95%;"></div>
        </div>
    </div>
</div>
<!--  left1 end -->
<div class="fl pt6 puleft2">
    <div class="pmidd_bott">
        <div class="pumiddboxttop1 fl">
            <h2 class="tith2 pt3">平台用户</h2>
            <div class="puleft2height" style="margin-top:20px;">
                <div class="widget-inline-box text-center ">
                    <p>总用户数</p>
                    <h3 class=" ceeb1fd f30"><span id="totalUserCount"></span></h3>
                    <h4 class="text-muted ">占比 <span id="totalUserCountRate"></span> %</h4>
                </div>
                <div class="widget-inline-box text-center ">
                    <p>来访者</p>
                    <h3 class=" c24c9ff f30"><span id="visitorCount"></span></h3>
                    <h4 class="text-muted ">占比 <span id="visitorCountRate"></span> %</h4>
                </div>
                <div class="widget-inline-box text-center ">
                    <p>咨询师</p>
                    <h3 class=" cffff00 f30"><span id="counselorCount"></span></h3>
                    <h4 class="text-muted ">占比 <span id="counselorCountRate"></span> %</h4>
                </div>
                <div class="widget-inline-box text-center ">
                    <p>家属</p>
                    <h3 class=" ceeb1fd f30"><span id="familyMemberCount"></span></h3>
                    <h4 class="text-muted ">占比 <span id="familyMemberCountRate"></span> %</h4>
                </div>
            </div>
        </div>
        <!-- 最新答疑 start -->
        <div class="pumiddboxttop2 fl">
            <h2 class="tith2 pt3">最新答疑</h2>
            <div class="left2_table pumiddboxttop2_cont" style="margin-top:20px;">
                <ul id="question-list">
                </ul>
            </div>
        </div>
        <!-- 最新答疑 end-->
    </div>
    <!--  amidd_bott end-->
    <div class="puleftboxtbott">
        <h2 class="tith2 pt1">心理科普</h2>
        <div class="box pbox" style="margin-top:20px;">
            <div class="fl pumiddboxtbott1cont" id="articleChart" style="height: 100%; margin-left: 20px;  width:95%;"></div>
        </div>
    </div>
    <!-- amidd_bott end -->
</div>
<!-- mrbox_top end -->
<div class="mr_right fl">
    <div class="aleftboxttop">
        <h2 class="tith2 pt3">测评情况</h2>
        <div class="lefttoday_number" style="margin-top:20px;">
            <div class="widget-inline-box text-center fl">
                <p style="margin-bottom:30px;">测评总数</p>
                <h3 class="ceeb1fd"><span id="testTotalCount"></span></h3>
            </div>
            <div class="widget-inline-box text-center fl">
                <p style="margin-bottom:30px">完成数</p>
                <h3 class="c24c9ff"><span id="testDoneCount"></span></h3>
            </div>
            <div class="widget-inline-box text-center fl">
                <p style="margin-bottom:30px">未完成数</p>
                <h3 class="cffff00"><span id="testUndoneCount"></span></h3>
            </div>
            <div class="widget-inline-box text-center fl">
                <p style="margin-bottom:30px">预警数</p>
                <h3 class="c11e2dd"><span id="testAbnormalCount"></span></h3>
            </div>
        </div>
        <!-- lefttoday_number end -->
    </div>
    <div class="purightboxmidd">
        <h2 class="tith2 pt12">施测率</h2>
        <div class="lefttoday_tit"></div>
        <div class="purightboxbottcont" id="testRateChart"></div>
    </div>
    <div class="purightboxmidd">
        <h2 class="tith2 pt12">异常率</h2>
        <div class="lefttoday_tit"></div>
        <div class="purightboxbottcont" id="testAbnormalChart"></div>
    </div>
</div>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/dashboard/js/jquery-3.2.0.min.js}"></script>
    <script th:src="@{/static/dashboard/js/echarts.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/pages/main.js}"></script>
    <script type="text/javascript">
        let init = function () {
            $.getJSON('/dashboard/init', function (res) {
                $("#totalUserCount").html(res.totalUserCount);
                $("#totalUserCountRate").html('100');
                $("#visitorCount").html(res.visitorCount);
                $("#visitorCountRate").html(Math.round(res.visitorCount / res.totalUserCount * 10000) / 100);
                $("#counselorCount").html(res.counselorCount);
                $("#counselorCountRate").html(Math.round(res.counselorCount / res.totalUserCount * 10000) / 100);
                $("#familyMemberCount").html(res.familyMemberCount);
                $("#familyMemberCountRate").html(Math.round(res.familyMemberCount / res.totalUserCount * 10000) / 100);
                initQuestions(res);
                initCounselingChart(res);
                initCounselingTypeCapacityChart(res);
                initCounselingQuestionCapacityChart(res);
                initTestRecordCapacityChart(res);
                $("#testTotalCount").html(res.totalCount);
                $("#testDoneCount").html(res.doneCount);
                $("#testUndoneCount").html(res.unDoCount);
                $("#testAbnormalCount").html(res.abnormalCount);
                initTestChart(res);
                initArticleChart(res);
            });
        };
        //最新心理答疑
        let initQuestions = function (obj) {
            let wrapper = $("#question-list");
            wrapper.empty();
            let str = '';
            for (let i = 0; i < obj.counselingQuestions.length; i++) {
                let counselingQuestion = obj.counselingQuestions[i];
                if (i % 2 === 0) {
                    str += '<li><p class="text_l"> ' + counselingQuestion.title + '</p><p class="text_l">' + counselingQuestion.questionerLoginName.substring(0, 1) + ' ****  ' + counselingQuestion.questionerLoginName.substring(0, 1) + ' ' + counselingQuestion.addDate + '</p></li>';
                }
                else {
                    str += '<li class="bg"><p class="text_l"> ' + counselingQuestion.title + '</p><p class="text_l">' + counselingQuestion.questionerLoginName + '  ' + counselingQuestion.addDate + '</p></li>';
                }
            }
            wrapper.append(str);
        };
        //每日咨询量
        let dateArray = [];
        let counselingArray = [];
        let counselingchart;
        let initCounselingChart = function (res) {
            dateArray.splice(0);
            counselingArray.splice(0);
            for (let jsonObj in res.counselingCapacitys) {
                dateArray.push(res.counselingCapacitys[jsonObj].dt.substring(5, 10));
                counselingArray.push(res.counselingCapacitys[jsonObj].capacity);
            }
            //统计图表：每日咨询量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            counselingchart = Highcharts.chart('counselingChart', {
                chart: {
                    type: 'line',
                    backgroundColor: 'rgba(1,202,217,.2)'
                },
                credits: { enabled: false },
                title: {
                    text: '每日咨询量',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: dateArray,
                    labels: { style: { color: '#2EBBD9' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#2EBBD9' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '咨询量',
                    color: '#00BFFF',
                    data: counselingArray
                }
                ]
            });
        };
        //按问题类型统计咨询量
        let counselingTypeArray = [];
        let counselingTypeCapacityArray = [];
        let counselingTypeCapacityChart;
        let initCounselingTypeCapacityChart = function (res) {
            counselingTypeArray.splice(0);
            counselingTypeCapacityArray.splice(0);
            for (let jsonObj in res.counselingTypeCapacitys) {
                counselingTypeArray.push(res.counselingTypeCapacitys[jsonObj].counselingType);
                counselingTypeCapacityArray.push(res.counselingTypeCapacitys[jsonObj].capacity);
            }
            //统计图表：按照问题类型统计每日咨询量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            counselingTypeCapacityChart = Highcharts.chart('counselingTypeChart', {
                chart: {
                    type: 'bar',
                    backgroundColor: 'rgba(1,202,217,.2)'
                },
                credits: { enabled: false },
                title: {
                    text: '',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: counselingTypeArray,
                    labels: { style: { color: '#2EBBD9' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#2EBBD9' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '咨询量',
                    color: '#00BFFF',
                    data: counselingTypeCapacityArray
                }
                ]
            });
        };
        //每日在线答疑量
        let counselingQuestionCapacityArray = [];
        let counselingQuestionCapacityChart;
        let initCounselingQuestionCapacityChart = function (res) {
            dateArray.splice(0);
            counselingQuestionCapacityArray.splice(0);
            for (let jsonObj in res.counselingQuestionCapacitys) {
                dateArray.push(res.counselingQuestionCapacitys[jsonObj].dt.substring(5, 10));
                counselingQuestionCapacityArray.push(res.counselingQuestionCapacitys[jsonObj].capacity);
            }
            //统计图表：每日在线答疑量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            counselingQuestionCapacityChart = Highcharts.chart('counselingQuestionCapacityChart', {
                chart: {
                    type: 'spline',
                    backgroundColor: 'rgba(1,202,217,.2)'
                },
                credits: { enabled: false },
                title: {
                    text: '',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: dateArray,
                    labels: { style: { color: '#2EBBD9' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#2EBBD9' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '答疑量',
                    color: '#00BFFF',
                    data: counselingQuestionCapacityArray
                }
                ]
            });
        };
        //统计每天测评量
        let testRecordCapacityArray = [];
        let testRecordCapacityChart;
        let initTestRecordCapacityChart = function (res) {
            dateArray.splice(0);
            testRecordCapacityArray.splice(0);
            for (let jsonObj in res.testRecordCapacitys) {
                dateArray.push(res.testRecordCapacitys[jsonObj].dt.substring(5, 10));
                testRecordCapacityArray.push(res.testRecordCapacitys[jsonObj].capacity);
            }
            //统计图表：每日测评量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            counselingchart = Highcharts.chart('testRecordCapacityChart', {
                chart: {
                    type: 'areaspline',
                    backgroundColor: 'rgba(1,202,217,.2)'
                },
                credits: { enabled: false },
                title: {
                    text: '',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: dateArray,
                    labels: { style: { color: '#2EBBD9' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#2EBBD9' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '测评量',
                    color: '#00BFFF',
                    data: testRecordCapacityArray
                }
                ]
            });
        };
        //测评情况
        let initTestChart = function (res) {
            let testTotalCount = res.totalCount;
            let testUndoneCount = res.unDoCount;
            let testDoneCount = res.doneCount;
            let testAbnormalCount = res.abnormalCount;
            let chartoptions = {
                title: "",
                chart: {
                    renderTo: '',
                    backgroundColor: 'rgba(1,202,217,.2)'
                },
                colors: ['#7ecef4', '#e5ffc7'],
                tooltip: {
                    headerFormat: '{series.name}<br>',
                    pointFormat: '{point.name}: <b>{point.percentage:.1f}%</b>'
                },
                legend: {
                    itemStyle: { color: "#ffffff" }
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: false
                        },
                        showInLegend: true,
                        size: 160
                    }
                },
                series: [{ type: "pie", name: "", data: [] }]
            };
            //测评情况
            let data = [];
            data.push(["已测数", testDoneCount]);
            data.push(["已测数", testDoneCount]);
            data.push(["已测数", testDoneCount]);
            chartoptions.chart.renderTo = "testRateChart";
            chartoptions.series[0].data = data;
            chartoptions.series[0].name = "测评情况";
            let testChart = new Highcharts.Chart(chartoptions);
            //施测率
            let data2 = [];
            data2.push(["测评总数", testTotalCount]);
            data2.push(["已测数", testDoneCount]);
            chartoptions.chart.renderTo = "testRateChart";
            chartoptions.series[0].data = data2;
            chartoptions.series[0].name = "施测率";
            let testRateChart = new Highcharts.Chart(chartoptions);
            //异常率
            let data3 = [];
            data3.push(["正常人数", testDoneCount - testAbnormalCount]);
            data3.push(["异常人数", res.abnormalCount]);
            chartoptions.chart.renderTo = "testAbnormalChart";
            chartoptions.series[0].data = data3;
            chartoptions.series[0].name = "异常率";
            let testAbnormalChart = new Highcharts.Chart(chartoptions);
        };
        //文章统计
        let initArticleChart = function (res) {
            let articleCategoryArray = [];
            let articleCategoryCapacityArray = [];
            let artlcleCapacity;
            articleCategoryArray.splice(0);
            articleCategoryCapacityArray.splice(0);
            for (let jsonObj in res.articleCapacitys) {
                articleCategoryArray.push(res.articleCapacitys[jsonObj].category);
                articleCategoryCapacityArray.push(res.articleCapacitys[jsonObj].capacity);
            }
            //统计图表：按照问题类型统计每日咨询量
            Highcharts.setOptions({
                lang: {
                    loading: '数据载入中...'
                }
            });
            artlcleCapacity = Highcharts.chart('articleChart', {
                chart: {
                    type: 'column',
                    backgroundColor: 'rgba(1,202,217,.2)'
                },
                title: {
                    text: '',
                    x: -20,
                    style: { color: "#fff", fontSize: "12px" }
                },
                subtitle: {
                    text: '',
                    x: -20
                },
                xAxis: {
                    categories: articleCategoryArray,
                    labels: { style: { color: '#2EBBD9' } }
                },
                yAxis: {
                    title: { text: '' },
                    labels: { style: { color: '#2EBBD9' } },
                    plotLines: [{
                        value: 0,
                        width: 1,
                        color: '#fff'
                    }]
                },
                tooltip: {
                    crosshairs: true,
                    shared: true
                },
                legend: {
                    enabled: false
                },
                series: [{
                    name: '数量',
                    color: '#00BFFF',
                    data: articleCategoryCapacityArray
                }
                ]
            });
        };
        $(function () {
            init();
            setInterval(function () {
                init();
            }, 60000);
        });
    </script>
</th:block>
</body>
</html>