<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">心理课堂</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">课程列表</a></li>
                    </ol>
                </div>
                <h4 class="page-title">课程列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2 mr-1">
                                    <select class="form-control" style="width:100%;" name="sr-courseType" id="sr-courseType">
                                        <option></option>
                                    </select>
                                </div>
                                <div class="form-group mb-2 mr-1">
                                    <input type="text" class="form-control" id="sr-courseName" placeholder="课程名称..." autocomplete="off">
                                </div>
                                <button type="button" class="btn btn-primary mb-2" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <a id="btnAdd" href="/course/course/edit" class="btn btn-primary mb-2 mr-1" title="创建课程"><i class="fa fa-plus mr-1"></i>创建课程</a>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbCourse" class="table table-striped nowrap" width="100%">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th align="left">课程名称</th>
                                <th align="left">课程分类</th>
                                <th align="left">是否推荐</th>
                                <th align="left">创建者</th>
                                <th align="left">创建时间</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbCourse').DataTable().ajax.reload();
            });
            //datatables
            $("#tbCourse").bsDataTables({
                columns: columns,
                url: '/course/course/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //修改
            $("#tbCourse").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.courseId = data.id;
                jsonObj.state = 0;
                $.ajax({
                    type: 'POST',
                    url: '/course/course/update_publish_state',
                    data: jsonObj,
                    dataType: "json",
                    success: function (res) {
                        if (res.resultCode === 200) {
                            location.href = "/course/course/edit";
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
            //删除
            $("#tbCourse").on('click', '.delete', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.id = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/course/course/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, {icon: 1, time: 2000});
                                oTable.draw();
                            } else {
                                layer.msg(res.resultMsg, {icon: 2, time: 2000});
                            }
                        }, 'json');
                    }
                });
            });
            //删除(批量)
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/course/course/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });

            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });
        let initPage = function () {
            '[[${canAdd}]]' == 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' == 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' == 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
            initSelect("#sr-courseType", "/course/coursetype/get_for_select", "" ,"","选择课程分类");
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            {
                "data": "courseName", "bSortable": false, "render":
                    function (data, type, full, meta) {
                        if (full.thumbnail === "") {
                            return '<img src="/static/images/nopic.png" class="mr-2 avatar-md rounded" width="80">' + full.courseName + '';
                        }
                        else {
                            return '<img src="/static/upload/course_cover/thumbnail/' + full.thumbnail + '" class="mr-2 avatar-md rounded" width="80">' + full.courseName + '';
                        }
                    }
            },
            { "data": "courseTypeName", "bSortable": false },
            {
                "data": "isRecommend", "render":
                    function (data, type, full, meta) {
                        if (full.isRecommend === 1) {
                            return '<span class="badge badge-success badge-pill">是</span>';
                        }
                        else {
                            return '<span class="badge badge-light badge-pill">否</span>';
                        }
                    }, "bSortable": false
            },
            { "data": "operatorRealName", "bSortable": false },
            {"data": "addDate", "bSortable": false}
        ];
        let columnDefs = [{
            targets: 6, render: function (data, type, row, meta) {
                let buttons = '';
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                if ('[[${canDelete}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-danger btn-sm mr-1 delete"><i class="fa fa-trash-o mr-1"></i>删除</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.courseName = $("#sr-courseName").val();
            param.courseTypeId = $("#sr-courseType").val();
            param.IsPublish = 1;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>