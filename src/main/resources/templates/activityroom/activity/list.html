<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">活动列表</a></li>
                    </ol>
                </div>
                <h4 class="page-title">活动列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-activityType" class="sr-only">活动类型：</label>
                                        <select class="form-control" id="sr-activityType" name="sr-activityType" style="width:150px;">
                                            <option value="">选择活动类型</option>
                                            <option value="1">驻场咨询（轻咨询）</option>
                                            <option value="2">驻场咨询（50分钟以上）</option>
                                            <option value="3">团体辅导</option>
                                            <option value="4">心理关爱活动</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-activityName" class="sr-only">活动主题：</label>
                                        <input type="text" class="form-control" id="sr-activityName" name="sr-activityName" placeholder="活动主题" autocomplete="off" style="width:200px;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-primary mr-1" title="创建活动"><i class="fa fa-plus mr-1"></i>创建活动</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mr-1" title="删除"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbActivityList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>活动主题</th>
                                <th>活动类型</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>驻场咨询师</th>
                                <th>签到人数</th>
                                <th>调查问卷</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <!-- modal.二维码 start -->
    <div id="qrcode-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog large">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary text-light">
                    <h5 class="modal-title">活动专属二维码</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div id="qrcode" style="width: 250px; height: 250px; margin: 0px auto; border: 25px solid rgb(255, 255, 255);">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light mr-2" data-dismiss="modal">关闭</button>
                    <input type="button" class="btn btn-primary" id="download" value="下载" />
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.二维码 end -->
    <!-- modal.活动图库 start -->
    <div id="gallery-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-full-width modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary text-light">
                    <h5 class="modal-title" id="modalActivityName"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light mr-2" data-dismiss="modal">关闭</button>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.活动图库 end -->
    <!-- 全屏查看原图 start -->
    <div id="fullscreen-view" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9); z-index: 9999; cursor: pointer;">
        <div style="position: relative; width: 100%; height: 100%;">
            <img id="fullscreen-image" src="" style="max-width: 100%; max-height: 100%; margin: auto; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <div id="prev-image" style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); color: white; font-size: 30px; cursor: pointer; z-index: 10000; opacity: 0.5; transition: opacity 0.3s;">
                <i class="fa fa-chevron-left"></i>
            </div>
            <div id="next-image" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); color: white; font-size: 30px; cursor: pointer; z-index: 10000; opacity: 0.5; transition: opacity 0.3s;">
                <i class="fa fa-chevron-right"></i>
            </div>
        </div>
    </div>
    <!-- 全屏查看原图 end -->
    <!-- modal.问卷作答记录 start-->
    <div id="myModalSurveyRecord" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">调查问卷作答记录</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped nowrap" id="tbSurveyRecord">
                            <thead>
                            <tr>
                                <th>问卷名称</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>作答时间</th>
                                <th>状态</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="btnExportSurvey">导出结果</button>
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                    <input type="hidden" id="hidActivityId" value="0" />
                    <input type="hidden" id="hidSurveyId" value="0" />
                </div>
            </div>
        </div>
    </div>
    <!-- modal.问卷作答记录 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/qrcode/qrcode.min.js}"></script>
    <script type="text/javascript">
        let users;
        $(function () {
            //初始化页面权限
            initPage();
            //添加活动
            $("#btnAdd").click(function () {
                location.href = "/activityroom/activity/add";
            });
            //查询
            $("#btnQuery").click(function () {
                oTable.draw();
            });
            //活动列表Datatables
            $("#tbActivityList").bsDataTables({
                columns: columns,
                url: '/activityroom/activity/get_list_by_paged',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //查看已签到人员清单
            $("#tbActivityList").on('click', '.user', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/clocking/list?activityId=" + data.id;
            });
            //修改
            $("#tbActivityList").on('click', '.edit', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/activity/update?id=" + data.id;
            });
            //调查问卷记录
            $("#tbActivityList").on('click', '.survey', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidActivityId").val(data.id);
                $("#hidSurveyId").val(data.surveyId);
                initSurveyRecord();
                $("#myModalSurveyRecord").modal();
            });
            $("#tbActivityList").on('click', '.report', function (){
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/activity/report?activityId=" + data.id;
            });
            $("#btnExportSurvey").click(function () {
                layer.msg('数据处理中…', {
                    icon: 17, shade: 0.05, time: false
                });
                $("#btnExportSurvey").attr("Disabled", true);
                let jsonObj = {};
                jsonObj.activityId = $("#hidActivityId").val();
                jsonObj.surveyId = $("#hidSurveyId").val();
                $.ajax({
                    type: 'POST',
                    url: '/export/activity_survey_result',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnExportSurvey").attr("Disabled", false);
                        layer.closeAll();
                        if(res.resultCode ===200) {
                            location.href="/static/upload/temp/"+res.resultMsg;
                        }
                        else {
                            layer.msg('导出失败!',{ icon: 2, time: 2000 });
                        }
                    }
                });
            });
            //活动图库
            $("#tbActivityList").on('click', '.gallery', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#modalActivityName").text(data.activityName);
                $("#gallery-modal .modal-body").empty();
                $.ajax({
                    type: "POST",
                    url: "/activityroom/activity_pic/getPicList",
                    data: 'activityId=' + data.id,
                    dataType: "json",
                    success: function(res) {
                        if(res.length > 0) {
                            let html = '<div class="row">';
                            res.forEach(function(pic) {
                                html += '<div class="col-md-3 mb-3">';
                                html += '<div class="card">';
                                html += '<img src="/static/upload/activity/mobile/' + pic.fileName + '" class="card-img-top view-original" alt="活动图片" style="height: 300px; object-fit: cover; cursor: pointer;" data-original="/static/upload/activity/' + pic.fileName + '">';
                                html += '</div></div>';
                            });
                            html += '</div>';
                            $("#gallery-modal .modal-body").html(html);

                            // 存储所有图片信息
                            window.currentImages = res;
                            window.currentImageIndex = 0;
                            
                            // 绑定图片点击事件
                            $(".view-original").click(function() {
                                let imgSrc = $(this).data("original");
                                window.currentImageIndex = $(this).closest('.col-md-3').index();
                                $("#fullscreen-image").attr("src", imgSrc);
                                $("#fullscreen-view").fadeIn(200);
                                updateNavigationButtons();
                            });
                        } else {
                            $("#gallery-modal .modal-body").html('<div class="text-center text-muted"><i class="fa fa-picture-o fa-3x mb-3"></i><p>暂无活动图片</p></div>');
                        }
                        $("#gallery-modal").modal();
                    },
                    error: function() {
                        layer.msg('获取图片列表失败', { icon: 2, time: 2000 });
                    }
                });
            });
            // 更新导航按钮状态
            function updateNavigationButtons() {
                if (window.currentImages.length <= 1) {
                    $("#prev-image, #next-image").hide();
                } else {
                    $("#prev-image, #next-image").show();
                    if (window.currentImageIndex === 0) {
                        $("#prev-image").hide();
                    }
                    if (window.currentImageIndex === window.currentImages.length - 1) {
                        $("#next-image").hide();
                    }
                }
            }
            // 上一张图片
            $("#prev-image").click(function(e) {
                e.stopPropagation();
                if (window.currentImageIndex > 0) {
                    window.currentImageIndex--;
                    let imgSrc = "/static/upload/activity/" + window.currentImages[window.currentImageIndex].fileName;
                    $("#fullscreen-image").attr("src", imgSrc);
                    updateNavigationButtons();
                }
            });
            // 下一张图片
            $("#next-image").click(function(e) {
                e.stopPropagation();
                if (window.currentImageIndex < window.currentImages.length - 1) {
                    window.currentImageIndex++;
                    let imgSrc = "/static/upload/activity/" + window.currentImages[window.currentImageIndex].fileName;
                    $("#fullscreen-image").attr("src", imgSrc);
                    updateNavigationButtons();
                }
            });
            // 鼠标悬停时显示箭头
            $("#fullscreen-view").hover(
                function() {
                    $("#prev-image, #next-image").css("opacity", "1");
                },
                function() {
                    $("#prev-image, #next-image").css("opacity", "0.5");
                }
            );

            // 点击全屏图片区域关闭
            $("#fullscreen-view").click(function() {
                $(this).fadeOut(200);
            });
            // 阻止图片点击事件冒泡
            $("#fullscreen-image").click(function(e) {
                e.stopPropagation();
            });
            let qrcode;
            let qrcodeName;
            //活动二维码
            $("#tbActivityList").on('click', '.qrcode', function () {
                if (qrcode != undefined) {
                    $("#qrcode").empty();
                }
                let data = oTable.row($(this).parents('tr')).data();
                qrcodeName = data.activityName;
                let activityId = data.id;
                qrcode = new QRCode(document.getElementById("qrcode"), {
                    text: window.location.protocol + "//" + window.location.host + "/app/activity/detail?activityId="+activityId,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                });
                $("#qrcode-modal").modal();
            });
            $("#download").click(function () {
                let img = $("#qrcode img")[0]; // 获取要下载的图片
                let url = img.src;                            // 获取图片地址
                let a = document.createElement('a');          // 创建一个a节点插入的document
                let event = new MouseEvent('click');          // 模拟鼠标click点击事件
                a.download = qrcodeName;                // 设置a节点的download属性值
                a.href = url;                                 // 将图片的src赋值给a节点的href
                a.dispatchEvent(event)
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/activityroom/activity/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //实现全选
            $("#chkall").change(function () {
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });

        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        /*活动列表 start*/
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "activityName", "bSortable": false },
            { "data": "activityType", "bSortable": false,"render": function (data, type, row, meta) {
                    let labels = "";
                    switch (row.activityType) {
                        case 1:
                            labels = '驻场咨询（轻咨询）';
                            break;
                        case 2:
                            labels = '驻场咨询（50分钟以上）';
                            break;
                        case 3:
                            labels = '团体辅导';
                            break;
                        case 4:
                            labels = '心理关爱活动';
                            break;
                        default:
                            labels = '';
                    }
                    return labels;
                }},
            { "data": "startTime",  "bSortable": false },
            { "data": "endTime","bSortable": false},
            { "data": "counselorName","bSortable": false},
            { "data": "clockingInNum","bSortable": false},
            { "data": "surveyName","bSortable": false}
        ];
        let columnDefs =
            [
                {
                    targets: 8,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        let startDate = row.startTime;
                        let endDate = row.endTime;
                        if (startDate > getDateNowFormat()) {
                            labels = '<span class="badge badge-light">未开始</span>';
                        }
                        if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                            labels = '<span class="badge badge-success">进行中</span>';
                        }
                        if (getDateNowFormat() >= endDate) {
                            labels = '<span class="badge badge-danger">已结束</span>';
                        }
                        return labels;
                    }
                },
                {
                    targets: 9,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        if ('[[${canUpdate}]]' === 'true') {
                            labels += '<button class="btn btn-outline-warning btn-sm edit mr-1" title="修改"><i class="fa fa-edit"></i></button>';
                        }
                        if('[[${canAdd}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm user mr-1" title="人员清单"><i class="fa fa-users"></i></button>';
                        }
                        if('[[${canViewPic}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm gallery mr-1" title="活动图库"><i class="fa fa-picture-o"></i></button>';
                        }
                        if('[[${canDownloadQRCode}]]' === 'true'){
                            labels += '<button class="btn btn-outline-success btn-sm qrcode mr-1" title="活动二维码"><i class="fa fa-qrcode"></i></button>';
                        }
                        if('[[${canViewSurveyRecord}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm survey mr-1" title="问卷记录">问卷记录</button>';
                        }
                        if('[[${canViewReport}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm report mr-1" title="活动报告">活动报告</button>';
                        }
                        return labels;
                    }
                }
            ];
        let getQueryCondition = function (data) {
            let param = {};
            param.activityName = $.trim($("#sr-activityName").val());
            param.activityType = $("#sr-activityType").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        /*活动列表 end*/
        let columns_s = [
            { "data": "surveyName" },
            { "data": "loginName" },
            { "data": "realName" },
            {"data": "recordDate", "bSortable": false, "render":
                    function(data,type,full,meta){
                        return moment(data).format("YYYY-MM-DD HH:mm:ss");
                    }
            },
            {
                "data": "isDone", "bSortable": false,
                render: function (data, type, row, meta) {
                    if (data === 1) {
                        return '<span class="badge badge-success badge-pill">已完成</span>';
                    }
                    if (data === 0) {
                        return '<span class="badge badge-light badge-pill">未完成</span>';
                    }
                }
            }
        ];
        let getQueryCondition_s = function (data){
            let param = {};
            param.activityId = $("#hidActivityId").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        }
        let oTableSurvey = null;
        let initSurveyRecord = function () {
            if (oTableSurvey != null) {
                oTableSurvey.destroy();
            }
            oTableSurvey = $("#tbSurveyRecord").DataTable({
                "ordering": false,
                "processing": true,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "bServerSide":true,
                "paging": true,
                "sAjaxSource": "/survey/surveyrecord/get_activity_surveyrecordlist",
                "fnServerData": function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_s(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                },
                //配置列要显示的数据
                "columns":columns_s,
                "columnDefs": [],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "sProcessing": "加载中...",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>