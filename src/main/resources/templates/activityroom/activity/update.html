<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <style type="text/css">
        .form-group .error {
            margin-top: 5px;
            display: block;
        }
        .input-group + .error {
            margin-top: 5px;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">修改活动</a></li>
                    </ol>
                </div>
                <h4 class="page-title">修改活动</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <form id="frmActivity" action="#" class="col-lg-8">
                        <div class="card-body">
                            <div class="form-group">
                                <label for="activityName">活动主题</label>
                                <input id="activityName" name="activityName" class="form-control" type="text">
                            </div>
                            <div class="form-group">
                                <label for="activityType">活动类型</label>
                                <select id="activityType" name="activityType" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="1">驻场咨询（轻咨询）</option>
                                    <option value="2">驻场咨询（50分钟以上）</option>
                                    <option value="3">团体辅导</option>
                                    <option value="4">心理关爱活动</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="activityDate">选择活动时间</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                    </div>
                                    <input type="text" class="form-control" value="" id="activityDate" name="activityDate">
                                </div>
                                <input type="hidden" id="hidStartTime" value="" />
                                <input type="hidden" id="hidEndTime" value="" />
                            </div>
                            <div class="form-group">
                                <label for="counselor">选择驻场咨询师</label>
                                <select id="counselor" name="counselor" class="form-control"></select>
                            </div>
                            <div class="form-group">
                                <label for="surveyId">选择调查问卷</label>
                                <select id="surveyId" name="surveyId" class="form-control">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="editor_content">活动介绍</label>
                                <textarea id="editor_content"></textarea>
                            </div>
                            <div class="form-group cover-thumbnail hide">
                                <img id="thumbnail_cover" th:src="@{/static/images/nopic.png}" class="img-responsive" style="width:200px;" />
                            </div>
                            <div class="form-group">
                                <label>活动封面图<i class="fa fa-info-circle ml-1" data-toggle="tooltip" data-placement="right" title="图片建议像素：380*330px"></i></label>
                                <input type="file" name="file" id="txt_file" class="file-loading" />
                                <input id="hidCover" type="hidden" value="" />
                            </div>
                        </div>
                        <div class="card-footer">
                            <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                            <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        let activityId = getUrlParam("id");
        let editor_content;
        $(function () {
            //初始化页面
            initPage();
            $("#frmActivity").validate({
                rules: {
                    activityName: { required: true },
                    activityType: { required: true },
                    activityDate: { required: true },
                    counselor: { required: true },
                    surveyId: { required: true }
                },
                messages: {
                    activityName: { required: "请填写活动主题" },
                    activityType: { required: "请选择活动类型" },
                    activityDate: { required: "请选择活动时间" },
                    counselor: { required: "请选择驻场咨询师" },
                    surveyId: { required: "请选择调查问卷" }
                },
                errorPlacement: function(error, element) {
                    if (element.attr("name") === "activityDate") {
                        error.insertAfter(element.closest(".input-group"));
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": activityId,
                        "activityName": $.trim($("#activityName").val()),
                        "activityType": $("#activityType").val(),
                        "startTime": $("#hidStartTime").val(),
                        "endTime": $("#hidEndTime").val(),
                        "counselor": $("#counselor").val(),
                        "activityIntro": editor_content.getData(),
                        "activityCover": $("#hidCover").val(),
                        "surveyId": $("#surveyId").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/activityroom/activity/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    location.href = "/activityroom/activity/list";
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
        let initPage = function () {
            ClassicEditor
                .create(document.querySelector('#editor_content'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_content = editor;
                });
            let oFileInput = new FileInput();
            oFileInput.Init("txt_file");
            $('#activityDate').val("");
            initSelect('#surveyId', '/survey/survey/get_for_select',{});
            initForm();
        };
        let initForm = function () {
            $.getJSON("/activityroom/activity/get", { id: activityId }, function (res) {
                $("#activityName").val(res.activityName);
                $("#activityType").val(res.activityType);
                $("#hidStartTime").val(res.startTime);
                $("#hidEndTime").val(res.endTime);
                $("#activityDate").val(res.startTime + " - " + res.endTime);
                initSelect('#counselor', '/anteroom/user/getCounselorList_for_select',{},res.counselorName);
                $("#surveyId").val(res.surveyId);
                editor_content.setData(res.activityIntro);
                $("#hidCover").val(res.activityCover);
                if (res.activityCover !== "") {
                    $("#thumbnail_cover").attr("src", "/static/upload/activity_cover/" + res.activityCover);
                    $(".cover-thumbnail").removeClass('hide').addClass('show');
                }
                layer.closeAll();
           });
        }
        let FileInput = function () {
            let oFile = {};
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=activity_cover',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-secondary", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidCover").val(res.resultMsg);
                        $("#thumbnail_cover").attr("src", res.resultMsg === "" ? "/static/images/nopic.png" : "/static/upload/activity_cover/" + res.resultMsg);
                        $(".cover-thumbnail").removeClass('hide').addClass('show');
                        layer.msg("上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
        let locale = {
            "format": 'YYYY-MM-DD HH:mm:ss',
            "separator": " - ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        $('#activityDate').daterangepicker({
            "locale": locale,
            "showDropdowns": true,
            "linkedCalendars": false,
            "timePicker": true,
            "timePickerIncrement": 1,
            "timePicker24Hour": true,
            "minDate": moment().subtract(1, "days"),
            "drops": "down"
        }, function (start, end, label) {
            let startTime, endTime;
            if ((start - end) === 0) {
                let _end = new Date(end);
                let year = _end.getFullYear();
                let month = _end.getMonth();
                let day = _end.getDate();
                let hour = _end.getHours();
                let min = _end.getMinutes();
                let s = _end.getSeconds();
                end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-dd hh:mm:ss');
            }
            else {
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-DD HH:mm:ss');
            }
            $('#activityDate').val(startTime + ' - ' + endTime);
            $("#hidStartTime").val(startTime);
            $("#hidEndTime").val(endTime);
        });
    </script>
</th:block>
</body>
</html>