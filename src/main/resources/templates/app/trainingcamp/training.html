<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style type="text/css">
        .appBottomMenu2{
            z-index: 9999;
            bottom: 0;
            left: 0;
            right: 0;
            background: #FFF;
            border-top: 1px solid #E5E9F2;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${trainingCamp.trainingCamp.campName}">

        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <div id="appCapsule">
        <div class="appContent pb-5">
            <div class="title font16 font-weight-bold mt-2">
                训练营周期<span class="font-weight-normal ml-2" th:utext="|${#dates.format(trainingCamp.trainingCamp.startDate,'yyyy-MM-dd')} 至 ${#dates.format(trainingCamp.trainingCamp.endDate,'yyyy-MM-dd')}|"></span>
            </div>
            <div class="card border-0">
                <div class="title text-muted pt-1">
                    <div class="pull-left">任务进度</div>
                    <div class="time pull-right mb-1" th:text="${trainingCamp.campLeftTime}"></div>
                    <div class="progress progress-bar-default col-12 p-0">
                        <div role="progressbar" class="progress-bar" th:attr="style=@{'width: ' + ${trainingCamp.checkInRate} + '' }">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mt-3 mb-3 border-0">
                <div class="card-body p-0">
                    <div class="accordion pl-1 pr-1" id="taskAccordion">
                        <th:block th:if="${(not #lists.isEmpty(trainingCamp.trainingCamp.tasks) ) and trainingCamp.trainingCamp.tasks.size() gt 0}" th:each="campTask:${trainingCamp.trainingCamp.tasks}">
                            <div class="card border-0">
                                <div class="card-header card-header m-1 border bg-light" th:with="prefix='heading_'"  th:attr="id=${prefix}+${campTask.id}">
                                    <th:block th:if="${campTask.isLocked eq 1}">
                                        <h2 class="mb-0" style="background-color: #f8f9fa;">
                                            <button class="btn btn-link text-dark" type="button">
                                                <th:block th:text="${campTask.taskTitle}"></th:block>
                                                <i class="fa fa-lock mr-1 pull-right"></i>
                                                <i class="arrow fa fa-angle-down"></i>
                                            </button>
                                        </h2>
                                    </th:block>
                                    <th:block th:if="${campTask.isLocked eq 0}">
                                        <th:block th:if="${campTask.isAllCourseCheckIn eq 0}">
                                            <h2 class="mb-0" style="background-color: #f8f9fa;">
                                                <button class="btn btn-link text-dark" type="button" data-toggle="collapse" aria-expanded="true"
                                                        th:with="prefix1='#collapse_',prefix2='collapse_'" th:attr="data-target=${prefix1}+${campTask.id},aria-controls =${prefix2}+${campTask.id}">
                                                    <th:block th:text="${campTask.taskTitle}"></th:block>
                                                    <i class="arrow fa fa-angle-down"></i>
                                                </button>
                                            </h2>
                                        </th:block>
                                        <th:block th:if="${campTask.isAllCourseCheckIn eq 1}">
                                            <h2 class="mb-0" style="background-color: #f5e8dd;">
                                                <button class="btn btn-link text-dark" type="button" data-toggle="collapse" aria-expanded="true"
                                                        th:with="prefix1='#collapse_',prefix2='collapse_'" th:attr="data-target=${prefix1}+${campTask.id},aria-controls =${prefix2}+${campTask.id}">
                                                    <th:block th:text="${campTask.taskTitle}"></th:block>
                                                    <i class="arrow fa fa-angle-down"></i>
                                                </button>
                                            </h2>
                                        </th:block>
                                    </th:block>
                                </div>
                                <div class="collapse" th:with="prefix1='heading_',prefix2='collapse_'"  th:attr="id=${prefix2}+${campTask.id},aria-labelledby=${prefix1}+${campTask.id}"
                                     data-parent="#taskAccordion">
                                    <div class="card-body">
                                        <th:block th:if="${not #lists.isEmpty(campTask.courses) and campTask.courses.size() gt 0}">
                                            <th:block th:each="course:${campTask.courses}">
                                                <div class="contentList">
                                                    <div class="iconBox">
                                                        <th:block th:if="${course.isCheckIn eq 0}">
                                                            <span class="badge badge-outline-danger mr-3">未打卡</span>
                                                            <a href="#" th:onclick="checkIn([[${course}]],[[${campTask.id}]],[[${course.isCheckIn}]])" class="text-dark"><th:block th:text="${course.courseName}" /></a>
                                                        </th:block>
                                                        <th:block th:if="${course.isCheckIn eq 1}">
                                                            <span class="badge badge-outline-success mr-3">已打卡</span>
                                                            <a href="#" th:onclick="checkIn([[${course}]],[[${campTask.id}]],[[${course.isCheckIn}]])" class="text-dark"><th:block th:text="${course.courseName}" /></a>
                                                        </th:block>
                                                    </div>
                                                    <div class="pull-right">
                                                        <th:block th:if="${course.attachmentType eq 1}">
                                                            <span class="badge badge-outline-primary" th:text="视频"></span>
                                                        </th:block>
                                                        <th:block th:if="${course.attachmentType eq 2}">
                                                            <span class="badge badge-outline-primary" th:text="音频"></span>
                                                        </th:block>
                                                        <th:block th:if="${course.attachmentType eq 3}">
                                                            <span class="badge badge-outline-primary" th:text="文章"></span>
                                                        </th:block>
                                                    </div>
                                                </div>
                                            </th:block>
                                        </th:block>
                                    </div>
                                </div>
                            </div>
                        </th:block>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal. start -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index:99999;">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered ">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title font16 text-light course-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true" class="text-light">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="play-body"></p>
                    <div class="divider mt-1 mb-1"></div>
                    <div class="comments" style="max-height: 200px; overflow:auto;">

                    </div>
                    <!-- * comments -->
                    <div class="appBottomMenu2 mt-1 pt-1">
                        <div class="small text-danger mt-1 hide mb-1">温馨提示：本评论区请勿发表与政治有关的言论，让我们共同营造和谐、积极的交流氛围。</div>
                        <form id="frmComment" action="#" method="post">
                            <div class="form-input mb-1">
                                <textarea id="content" name="content" class="form-control" rows="4" placeholder="写下您的评论，限制100字..." maxlength="100"></textarea>
                            </div>
                            <input id="btnSave" type="submit" class="btn btn-primary btn-large btn-block" value="发表" />
                        </form>
                    </div>
                </div>
                <input type="hidden" id="hidTaskId" value="0" />
                <input type="hidden" id="hidCourseId" value="0" />
                <input type="hidden" id="hidCourseType" value="0" />
                <input type="hidden" id="hidCheckIn" value="0" />
            </div>
        </div>
    </div>
    <!-- Modal. end -->
    <th:block th:insert="~{layouts/footCamp}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let articleContent;
        if (document.referrer) {
            const savedId = localStorage.getItem('expandedId');
            if (savedId) {
                // 如果有保存的 id，尝试展开对应的元素
                $("#"+savedId).addClass('show');
            }
        }
        else{
            localStorage.removeItem('expandedId');
        }
        window.addEventListener('beforeunload', function () {
            const elementWithShow = $('.collapse.show');
            if (elementWithShow.length > 0) {
                const idToSave = elementWithShow.attr('id');
                localStorage.setItem('expandedId', idToSave);
            } else {
                // 如果没有展开的元素，清除本地存储
                localStorage.removeItem('expandedId');
            }
        });
        window.addEventListener('load', function() {
            setTimeout(function() { // 延迟设置滚动位置
                let scrollPositionX = parseInt(localStorage.getItem('scrollPositionX'), 10);
                let scrollPositionY = parseInt(localStorage.getItem('scrollPositionY'), 10);
                if (!isNaN(scrollPositionX) && !isNaN(scrollPositionY)) {
                    window.scrollTo(scrollPositionX, scrollPositionY);
                }
            }, 500); // 可根据实际情况调整延
        });
        $(function(){
            let startTime = null;
            let checkInterval = null;
            let stayTIme = 0;
            // 模态框显示时开始计时
            $('#myModal').on('shown.bs.modal', function () {
                if($("#hidCheckIn").val() === '1') return;
                if($("#hidCourseType").val() === '1' || $("#hidCourseType").val() === '2'){
                    stayTIme = 180;
                    let duration = $(".course_player")[0].duration;
                    if(duration < stayTIme){
                        stayTIme = duration;
                    }
                }

                startTime = Date.now();
                checkInterval = setInterval(function () {
                    let currentTime = Date.now();
                    let timeElapsed = (currentTime - startTime) / 1000; // 转换为秒

                    if (timeElapsed >= stayTIme) { // 用户停留满时间打卡
                        clearInterval(checkInterval); // 停止计时器
                        let jsonObj = {};
                        jsonObj.campId = getUrlParam('campId');
                        jsonObj.taskId = $("#hidTaskId").val();
                        jsonObj.courseId= $("#hidCourseId").val();
                        $.ajax({
                            url: '/trainingcamp/checkin/add',
                            type: 'POST',
                            data: JSON.stringify(jsonObj),
                            contentType:'application/json',
                            dataType: "json",
                            success: function (res) {
                                if (res.resultCode === 200) {
                                    layer.open({
                                        content: res.resultMsg
                                        ,skin: 'msg'
                                        ,time: 1 //2秒后自动关闭
                                    });
                                }
                                else {
                                    layer.open({
                                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                        , style: 'background-color:#ffffff; border:none;'
                                        , time: 2
                                    });
                                }
                            }
                        });
                    }
                }, 1000); // 每秒检查一次
            });

            // 模态框隐藏时清除计时器
            $('#myModal').on('hidden.bs.modal', function () {
                clearInterval(checkInterval);
                location.reload();
            });
            $("#frmComment").validate({
                rules: {
                    content: { required: true, maxlength: 100 }
                },
                messages: {
                    content: { required: "请填写评论内容", maxlength: "超过了评论字数限制" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.function_type = 2;
                    jsonObj.product_id = $("#hidCourseId").val();
                    jsonObj.commentContent = $("#content").val();
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/comment/comment/add',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            $("#content").val("");
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1"/>发表成功！'
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });

        let checkIn =function(course,taskId,isCheckIn){
            $(".play-body").html("");
            $(".course-title").html(course.courseName);
            if(course.attachmentType === 1){
                $(".play-body").html('<video class="course_player" src="/static/upload/course/' + course.attachmentFileName + '" controls="controls" controlsList="nodownload" preload="auto" autoplay="autoplay" webkit-playsinline="true" playsinline="true" x5-video-player-type="h5">对不起，您的浏览器不支持该播放器！</video >');
                $(".course_player").bind("contextmenu", function () {//取消右键事件
                    return false;
                });
            }
            if (course.attachmentType === 2) {
                $(".play-body").html('<audio class="course_player" src="/static/upload/course/' +  course.attachmentFileName + '" controls="controls" controlsList="nodownload" oncontextmenu="return false" autoplay="autoplay"></audio >');
            }
            if (course.attachmentType === 3) {
                $(".play-body").html(course.articleContent);
                articleContent = course.articleContent.length;
            }
            //评论
            let commentWrapper = $(".comments");
            commentWrapper.html('');
            let commentHtml = '';
            if(course.comments === null || course.comments.length === 0){
                commentHtml += '<div class="splashBlock">';
                commentHtml += '<div class="mb-3 mt-3"><img src="/static/images/nocomment.png" alt="draw" class="imageBlock xlarge"></div>';
                commentHtml += '<div class="sectionTitle text-center"><div class="lead"><i class="fa fa-info-circle mr-1"></i>暂无评论</div></div>';
                commentHtml += '</div>';
            }
            else{
                for(let i =0; i < course.comments.length; i++){
                    let comment = course.comments[i];
                    commentHtml+= '<div class="item">';
                    let headPic = comment.headImg === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + comment.headImg;
                    commentHtml+= '<div class="image"><img src="'+headPic+'" class="avatar" />';
                    commentHtml+= '</div>';
                    commentHtml+= '<div class="content"><strong class="font14">'+comment.realName.substring(0,1)+'**</strong>';
                    commentHtml+= '<div class="text font13">'+comment.commentContent+'</div><footer>'+moment(comment.commentDate).format("YYYY-MM-DD HH:mm:ss")+'</footer></div>';
                    commentHtml+= '</div>';
                }
            }
            commentWrapper.append(commentHtml);

            $("#hidCourseId").val(course.id);
            $("#hidTaskId").val(taskId);
            $("#hidCourseType").val(course.courseType);
            $("#hidCheckIn").val(course.isCheckIn);

            // 保存当前滚动位置到localStorage
            let scrollPosition = getScrollPosition();
            localStorage.setItem('scrollPositionX', scrollPosition.x);
            localStorage.setItem('scrollPositionY', scrollPosition.y);
            $("#myModal").modal();
        }

        let getScrollPosition =function() {
            return {
                x: (window.pageXOffset !== undefined) ? window.pageXOffset : (document.documentElement || document.body.parentNode || document.body).scrollLeft,
                y: (window.pageYOffset !== undefined) ? window.pageYOffset : (document.documentElement || document.body.parentNode || document.body).scrollTop
            };
        }
    </script>
</th:block>
</body>
</html>