<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .rightBox{
            display: flex;
        }
        .rightBoxText{
            margin-top: 10px;
            font-weight: bold;
            font-size: 14px;
            margin-right: 20px;
            color: #333;
        }
        .imgBox{
            width: 60vw;
            /* height: 48vw; */
            position: relative;
            margin:0 auto;
        }
        .imgBox img{
            width: 60vw;
        }
        .infoBox{
            position: absolute;
            top: 25vw;
            left: 0;
            padding: 0 10vw;
            box-sizing: border-box;
            width: 100%;
        }
        .infoTitle{
            text-align: center;
            /* background: repeating-linear-gradient(315deg,#804F21,#E2BB8E 7%,#804F21 10%);  */
            background-color: #333;
            -webkit-background-clip: text; /* 使用文本作为背景剪辑 */
            background-clip: text; /* 使用文本作为背景剪辑 */
            color: transparent; /* 将文本颜色设置为透明 */
            font-weight: bold; /* 加粗字体 */
            font-size: 4vw;
        }
        .infoName{
            display: flex;
            font-size: 3vw;
            font-family: Source Han Serif CN-Bold, Source Han Serif CN;
            font-weight: bold;
            color: #000000;
            line-height: 1.354vw;
            margin-top: 5vw;
            text-indent: 0.2em;
            font-weight: normal;
        }
        .infoName div{
            padding-bottom: 0.12vw;
            width: 3vw;
            height: 1.563vw;
            font-weight: normal;
        }
        .infoTime{
            padding-top: 15px;
        }
        .infoContent,.infoBottom{
            text-indent: 2em;text-align:justify;
            font-size: 3vw;
            font-family: Source Han Serif CN-SemiBold, Source Han Serif CN;
            font-weight: 600;
            color: #000000;
            line-height: 7vw;
            margin-top: 1.5vw;
            font-weight: normal;
            white-space: break-spaces;
        }
        .infoBottom{
            margin-top:10vw;
        }
        .infoLast{
            width: max-content;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-weight: normal;
            position: absolute;
            right: 10vw;
            bottom: -12vw;

        }
        .infoLast div{
            display: flex;
            justify-content: flex-end;
            font-size: 3vw;
            font-family: Source Han Serif CN-SemiBold, Source Han Serif CN;
            font-weight: 600;
            color: #000000;
            line-height: 0.99vw;
            font-weight: normal;
        }
        .infoLast div:last-child{
            margin-top: 0.2vw;
        }
        .appBottomMenu2{
            position: fixed;
            z-index: 9999;
            bottom: 0;
            left: 0;
            right: 0;
            background: #FFF;
            border-top: 1px solid #E5E9F2;
            align-items: center;
            justify-content: center;
            padding-left: 10px;
            padding-right: 10px;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="#" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${trainingCamp.trainingCamp.campName}"></div>
        <div class="right mr-1">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent p-0">
            <div class="card border-0">
                <div class="card-body text-dark p-0">
                    <div class="p-0">
                        <img th:if="${trainingCamp.trainingCamp.thumbnail eq null or trainingCamp.trainingCamp.thumbnail eq ''}" th:src="@{/static/images/nopic.png}" class="imageBlock img-fluid mb-2" />
                        <img th:unless="${trainingCamp.trainingCamp.thumbnail eq null or trainingCamp.trainingCamp.thumbnail eq ''}" th:src="|@{/static/upload/camp_cover/thumbnail/}${trainingCamp.trainingCamp.thumbnail}|" class="imageBlock img-fluid mb-2" />
                    </div>
                </div>
            </div>
            <div class="ml-1 mr-1">
                <ul class="nav nav-tabs nav-bordered nav-justified" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab">训练营信息</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab">评论</a>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="myTabContent">
                    <div class="tab-pane fade show active text-left" id="home" role="tabpanel">
                        <div class="contentBox long mb-2">
                            <h5 class="title font14">介绍</h5>
                            <div class="contentBox-body font14 ml-1 mr-1 pl-0 pr-0">
                                <th:block th:if="${not #strings.isEmpty(trainingCamp.trainingCamp.campIntro) }" th:utext="${trainingCamp.trainingCamp.campIntro}" />
                                <th:block th:unless="${not #strings.isEmpty(trainingCamp.trainingCamp.campIntro) }" th:utext="暂无介绍" />
                            </div>
                        </div>
                        <div class="contentBox long mb-2">
                            <h5 class="title font14">训练营周期</h5>
                            <div class="contentBox-body font14 ml-1 mr-1 pl-0 pr-0" th:utext="|${#dates.format(trainingCamp.trainingCamp.startDate,'yyyy-MM-dd')} 至 ${#dates.format(trainingCamp.trainingCamp.endDate,'yyyy-MM-dd')}|">
                            </div>
                        </div>
                        <div class="text-center">
                            <th:block th:if="${trainingCamp.trainingCamp.campStateForUser eq '参与报名'}">
                                <input type="button" class="btn btn-primary btn-block rounded" id="btnReg" value="参与报名" />
                            </th:block>
                            <th:block th:if="${trainingCamp.trainingCamp.campStateForUser eq '已满员' or trainingCamp.trainingCamp.campStateForUser eq '报名已截止' or trainingCamp.trainingCamp.campStateForUser eq '已结束'  or trainingCamp.trainingCamp.campStateForUser eq '训练营已结束'}">
                                <input type="button" class="btn btn-secondary btn-block rounded" th:value="${trainingCamp.trainingCamp.campStateForUser}" />
                            </th:block>
                            <th:block th:if="${trainingCamp.trainingCamp.campStateForUser eq '已报名，等待开营中'}">
                                <input type="button" class="btn btn-warning btn-block rounded" value="已报名，等待开营中" />
                            </th:block>
                            <th:block th:if="${trainingCamp.trainingCamp.campStateForUser eq '前往打卡'}">
                                <a th:href="@{/app/trainingcamp/training(campId=${trainingCamp.trainingCamp.id})}" class="btn btn-primary btn-block rounded">前往打卡</a>
                            </th:block>
                            <th:block th:if="${trainingCamp.isSurveyIssued eq true and trainingCamp.isCertShown eq true}">
                                <footer>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <a href="javascript:void(0)" onclick="showSurvey(); return false;" class="btn btn-outline-primary rounded btn-block">问卷调查</a>
                                        </div>
                                        <div class="col-6">
                                            <a href="javascript:void(0)" onclick="showCert(); return false;" class="btn btn-outline-primary rounded btn-block">结营证书</a>
                                        </div>
                                    </div>
                                </footer>
                            </th:block>
                            <th:block th:if="${trainingCamp.isSurveyIssued eq true and trainingCamp.isCertShown eq false}">
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <a href="#" onclick="showSurvey()" class="btn btn-outline-primary rounded btn-block">问卷调查</a>
                                    </div>
                                </div>
                            </th:block>
                            <th:block th:if="${trainingCamp.isSurveyIssued eq false and trainingCamp.isCertShown eq true}">
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <a href="javascript:void(0)" onclick="showCert(); return false;" class="btn btn-outline-primary rounded btn-block">结营证书</a>
                                    </div>
                                </div>
                            </th:block>
                        </div>
                    </div>
                    <div class="tab-pane fade text-left" id="profile" role="tabpanel">
                        <div class="comments" style="max-height: 200px; overflow:auto;">
                            <div th:if="${(not #lists.isEmpty(trainingCamp.trainingCamp.comments)) and trainingCamp.trainingCamp.comments.size() gt 0 }">
                                <th:block th:each="comment:${trainingCamp.trainingCamp.comments}">
                                    <div class="item">
                                        <div class="image">
                                            <img th:if="${comment.headImg  eq null or comment.headImg  eq ''}" th:src="@{/static/images/user.png}" class="avatar" />
                                            <img th:unless="${comment.headImg  eq null or comment.headImg  eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${comment.headImg }|" class="avatar" />
                                        </div>
                                        <div class="content">
                                            <strong class="font14" th:text="|${#strings.substring(comment.realName,0,1)}**|"></strong>
                                            <div class="text font13" th:utext="${comment.commentContent}">
                                            </div>
                                            <footer th:text="${#dates.format(comment.commentDate,'yyyy-MM-dd HH:mm:ss')}"></footer>
                                        </div>
                                    </div>
                                </th:block>
                            </div>
                            <div th:unless="${(not #lists.isEmpty(trainingCamp.trainingCamp.comments)) and trainingCamp.trainingCamp.comments.size() gt 0 }">
                                <div class="splashBlock">
                                    <div class="mb-3 mt-3">
                                        <img th:src="@{/static/images/nocomment.png}" alt="draw" class="imageBlock xlarge">
                                    </div>
                                    <div class="sectionTitle text-center">
                                        <div class="lead">
                                            <i class="fa fa-info-circle mr-1"></i>暂无评论
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- * comments -->
                        <div class="appBottomMenu2">
                            <div class="small text-danger mt-1 mb-1">温馨提示：本评论区请勿发表与政治有关的言论，让我们共同营造和谐、积极的交流氛围。</div>
                            <div class="divider mt-1 mb-1"></div>
                            <form id="frmComment" action="#" method="post">
                                <div class="form-input mb-3">
                                    <textarea id="content" name="content" class="form-control" rows="4" placeholder="写下您的评论，限制100字..." maxlength="100"></textarea>
                                </div>
                                <input id="btnSave" type="submit" class="btn btn-primary btn-large btn-block" value="发表" />
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal. 调查问卷 -->
    <div id="surveyModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 99999;">
        <div class="modal-dialog text-dark">
            <div class="modal-content">
                <th:block th:if="${trainingCamp.survey  != null}">
                    <div class="modal-header modal-colored-header bg-primary">
                        <div class="modal-title font16 text-light" th:text="${trainingCamp.survey.surveyName}"></div>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true" class="text-light">&times;</span>
                        </button>
                    </div>
                    <form id="frmSurvey" class="font14">
                        <div class="modal-body p-3">
                            <th:block th:each="question:${trainingCamp.survey.listQuestions}">
                                <div class="font-weight-bold font16 mb-1">
                                    <th:block th:utext="${question.qNumber}"/>、<th:block th:utext="${#strings.replace(question.qContent,'<p>','')}"/>
                                </div>
                                <div class="form-group" th:if="${question.qType eq 1}">
                                    <th:block th:each="item:${question.listItems}">
                                        <div class="custom-control custom-radio mb-1">
                                            <input class="custom-control-input" type="radio"   th:with="prefix1='option_',prefix2='question_'"
                                                   th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}"  th:value="${item.itemContent}"
                                                   th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])"  />
                                            <label class="custom-control-label" th:with="prefix='option_'"  th:attr="for=${prefix}+${item.id}" th:utext="${item.itemContent}"></label>
                                        </div>
                                        <th:block th:if="${item.isOther eq 1}">
                                            <div class="form-group" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                                <input type="text" class="form-control" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}" placeholder="请输入具体内容" />
                                            </div>
                                        </th:block>
                                    </th:block>
                                </div>
                                <div class="form-group" th:if="${question.qType eq 2}">
                                    <th:block th:each="item:${question.listItems}">
                                        <div class="custom-control custom-checkbox mb-1">
                                            <input class="custom-control-input" type="checkbox"   th:with="prefix1='option_',prefix2='question_'"
                                                   th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}"  th:value="${item.itemContent}"
                                                   th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])" />
                                            <label class="custom-control-label" th:with="prefix='option_'"  th:attr="for=${prefix}+${item.id}" th:text="${item.itemContent}"></label>
                                        </div>
                                        <th:block th:if="${item.isOther eq 1}">
                                            <div class="form-group" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                                <input type="text" class="form-control" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}"  placeholder="请输入具体内容" />
                                            </div>
                                        </th:block>
                                    </th:block>
                                </div>
                                <div class="form-group" th:if="${question.qType eq 3}">
                                    <input type="text" class="form-control" th:with="prefix='question_'"  th:attr="name=${prefix}+${question.id}" placeholder="请输入内容" />
                                </div>
                                <div class="divider dotted large mt-2 mb-2"></div>
                            </th:block>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                            <input type="submit" class="btn btn-primary" id="btnSaveSurvey" value="保存" />
                        </div>
                    </form>
                </th:block>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- Modal. 结营证书 -->
    <div id="certModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 99999;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body p-3">
                    <div class="rightBox col-12 text-center">
                        <div class="imgBox">
                            <img src="/static/images/cert.png" alt="">
                            <div class="infoBox">
                                <div class="infoTitle"><th:block th:text="${trainingCamp.trainingCamp.cert.certName}"></th:block>结营证书</div>
                                <div class="infoName">学员您好！<div></div></div>
                                <div class="infoContent"><th:block th:text="${trainingCamp.trainingCamp.cert.certContent}"></th:block></div>
                                <div class="infoBottom"></div>
                                <div class="infoLast">
                                    <div class="infoDanwei"><th:block th:text="${trainingCamp.trainingCamp.cert.issuedDept}"></th:block></div>
                                    <div class="infoTime"><th:block th:text="${trainingCamp.trainingCamp.cert.issuedDate}"></th:block></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>&nbsp;&nbsp;
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script type="text/javascript">
        let campId = getUrlParam('campId');
        $(function () {
            $("#btnReg").click(function () {
                $(this).attr('disabled',true);
                let url = "/app/trainingcamp/reg";
                let jsonObj = { campId: campId };
                $.post(url, jsonObj, function (res) {
                    $("#btnReg").attr('disabled',false);
                    if (res.resultCode === 200) {
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                            btn: '确定',
                            shadeClose: false,
                            yes: function () {
                                location.reload();
                            }
                        });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            });
            // 动态生成验证规则
            let rules = {};
            let messages = {};
            // 获取所有的问题
            $('.form-group').each(function() {
                let questionId = $(this).find('input, text').first().attr('name').replace('question_', '');
                let questionType = $(this).find('input, text').first().attr('type');

                if (questionType === 'radio' || questionType === 'checkbox') {
                    rules['question_' + questionId] = "required";
                    messages['question_' + questionId] = "请选择一个选项";
                } else if (questionType === 'text') {
                    rules['question_' + questionId] = "required";
                    messages['question_' + questionId] = "请填写内容";
                }
            });
            $("#frmSurvey").validate({
                errorPlacement: function (error, element) {
                    if (element.attr("name").indexOf("_other_input") !== -1) {
                        // 如果是其他选项的填空题，将错误信息放置在其下方
                        error.insertAfter(element);
                    } else {
                        // 将错误信息放置在 form-group 的后面
                        error.insertAfter(element.closest('.form-group'));
                    }
                },
                rules: rules,
                messages: messages,
                submitHandler: function (form){
                    let answers = [];
                    // 处理单选按钮
                    $('.custom-control-input[type="radio"]').each(function() {
                        if (this.checked) {
                            let questionId = this.name.replace('question_', '');
                            answers.push({
                                qId: questionId,
                                itemId: this.value,
                                otherAnswer: ''
                            });
                        }
                    });
                    // 处理复选按钮
                    $('.custom-control-input[type="checkbox"]').each(function() {
                        if (this.checked && !this.name.endsWith('_other')) {
                            let questionId = this.name.replace('question_', '');
                            let optionId = this.id.replace('option_','');
                            let existingAnswer = answers.find(answer => answer.qId === questionId);
                            if (existingAnswer) {
                                if (!existingAnswer.itemId) {
                                    existingAnswer.itemId = '';
                                }
                                existingAnswer.itemId += (existingAnswer.itemId ? '|' : '') + this.value;
                            } else {
                                answers.push({
                                    qId: questionId,
                                    itemId: this.value,
                                    otherAnswer: ''
                                });
                            }
                        }
                    });

                    // 处理文本输入
                    $('.form-control[type="text"]').each(function() {
                        let questionId = this.name.replace('question_', '');
                        if (this.value.trim() !== '' ) {
                            if(questionId.indexOf('_other') !== -1){
                                let answer = answers.find(answer=> answer.qId === questionId.replace('_other',''));
                                if(answer){
                                    answer.itemId += '|其他：'+this.value.trim();
                                }
                            }
                            else{
                                let existingAnswer = answers.find(answer => answer.qId === questionId);
                                if (existingAnswer) {
                                    existingAnswer.itemId = this.value.trim();
                                } else {
                                    answers.push({
                                        qId: questionId,
                                        itemId: this.value.trim(),
                                        otherAnswer: ''
                                    });
                                }
                            }
                        }
                    });

                    // 转换为JSON字符串
                    let jsonObj = {};
                    jsonObj.surveyId = '[[${trainingCamp.trainingCamp.surveyId}]]';
                    jsonObj.campId = campId;
                    jsonObj.listAnswers = answers;
                    $("#btnSaveSurvey").attr('Disabled',true);
                    $("#btnSaveSurvey").val('请稍后……');
                    $.ajax({
                        type: 'POST',
                        url: '/survey/surveyrecord/add_for_camp',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveSurvey").val("保存");
                            $("#btnSaveSurvey").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />提交成功！' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function (index) {
                                        layer.close(index);
                                        $("#surveyModal").modal('hide');
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
            $("#frmComment").validate({
                rules: {
                    content: { required: true, maxlength: 100 }
                },
                messages: {
                    content: { required: "请填写评论内容", maxlength: "超过了评论字数限制" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.function_type = 1;
                    jsonObj.product_id = campId;
                    jsonObj.commentContent = $("#content").val();
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/comment/comment/add',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            $("#content").val("");
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1"/>发表成功！'
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
        let showSurvey = function(){
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            $.post("/trainingcamp/trainingcamp/is_trainingcamp_survey_done",{campId: campId},function(res){
                layer.closeAll();
                if(res.resultCode === 111){
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>'+res.resultMsg
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
                else{
                    $("#surveyModal").modal();
                }
            })
        }
        let showCert = function (){
            $("#certModal").modal();
        }
        let toggleOtherInput  = function(d,a,b,c) {
            if($(d).is('[type="radio"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
                else{
                    $("#otherInput_" + c).toggle(false);
                }
            }
            if($(d).is('[type="checkbox"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
            }
        }
    </script>
</th:block>
</body>
</html>