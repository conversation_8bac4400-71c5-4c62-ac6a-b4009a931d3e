<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .item {
            position: relative;
        }
        .item .camp-state{
            position: absolute;
            left: 12px;
            bottom: 12px;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的训练营
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <div id="appCapsule">
        <div class="appContent pb-5">
            <div class="mt-1 mb-3">
                <img th:src="@{/static/images/app_trainingcamp_banner.jpg}" alt="image" class="imageBlock img-fluid rounded" title="心理训练营" />
            </div>
            <th:block th:if="${(not #lists.isEmpty(myCamps)) and myCamps.size() gt 0 }">
                <div class="divider mt-1 mb-2 hide"></div>
                <!-- * Card Overlay Carousel -->
                <div class="sectionTitle mb-1">
                    <div class="title">
                        <h2 class="font18">我的训练营</h2>
                    </div>
                </div>
                <div class="itemList">
                    <th:block th:each="camp,itemStat:${myCamps}">
                        <div class="mb-1">
                            <a th:href="@{/app/trainingcamp/detail(campId=${camp.id})}">
                                <div class="item">
                                    <div class="image mr-2">
                                        <th:block th:if="${camp.thumbnail ne null and camp.thumbnail ne ''}">
                                            <img th:src="|@{/static/upload/camp_cover/thumbnail/}${camp.thumbnail}|" alt="image">
                                        </th:block>
                                        <th:block th:unless="${camp.thumbnail ne null and camp.thumbnail ne ''}">
                                            <img th:src="@{/static/images/nopic.png}" alt="image">
                                        </th:block>
                                        <th:block th:if="${camp.campStateText eq '未开始'}">
                                            <div class="badge badge-info camp-state"><th:block th:text="${camp.campStateText}"></th:block></div>
                                        </th:block>
                                        <th:block th:if="${camp.campStateText eq '进行中'}">
                                            <div class="badge badge-success camp-state"><th:block th:text="${camp.campStateText}"></th:block></div>
                                        </th:block>
                                        <th:block th:if="${camp.campStateText eq '已结束'}">
                                            <div class="badge badge-danger camp-state"><th:block th:text="${camp.campStateText}"></th:block></div>
                                        </th:block>
                                    </div>
                                    <div class="text" style="width:100%;">
                                        <div class="font16 font-weight-bold pt-0 pb-1 text-dark" th:text="${camp.campName}"></div>
                                        <div class="text-muted font12 mt-1">
                                            开营时间：<th:block th:text="${#dates.format(camp.startDate,'yyyy-MM-dd')}"></th:block>
                                            <span class="badge badge-outline-danger pull-right camp-detail">查看详细</span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <th:block th:unless="${itemStat.last}"><div class="divider dashed large mt-2 mb-2"></div></th:block>
                    </th:block>
                </div>
            </th:block>
            <th:block th:unless="${(not #lists.isEmpty(myCamps)) and myCamps.size() gt 0 }">
                <div class="lead mb-3 mt-3">
                    <img src="/static/images/warning.png" width="22" class="mr-1"><span>您还未参加过任何训练营，您可以选择下面适合您的训练营报名参加。</span>
                </div>
                <th:block th:if="${(not #lists.isEmpty(otherCamps)) and otherCamps.size() gt 0 }">
                    <div class="divider mb-2"></div>
                    <!-- * Card Overlay Carousel -->
                    <div class="sectionTitle mb-1">
                        <div class="title">
                            <h2 class="font18">推荐训练营</h2>
                            <a class="text-muted" th:href="@{/app/trainingcamp/other_camps}">查看所有 </a>
                        </div>
                    </div>
                    <div class="itemList">
                        <th:block th:each="camp,itemStat:${otherCamps}">
                            <div class="mb-1">
                                <a th:href="@{/app/trainingcamp/detail(campId=${camp.id})}">
                                    <div class="item">
                                        <div class="image mr-2">
                                            <th:block th:if="${camp.thumbnail ne null and camp.thumbnail ne ''}">
                                                <img th:src="|@{/static/upload/camp_cover/thumbnail/}${camp.thumbnail}|" alt="image">
                                            </th:block>
                                            <th:block th:unless="${camp.thumbnail ne null and camp.thumbnail ne ''}">
                                                <img th:src="@{/static/images/nopic.png}" alt="image">
                                            </th:block>
                                            <th:block th:if="${camp.campStateText eq '未开始'}">
                                                <div class="badge badge-info camp-state"><th:block th:text="${camp.campStateText}"></th:block></div>
                                            </th:block>
                                            <th:block th:if="${camp.campStateText eq '进行中'}">
                                                <div class="badge badge-success camp-state"><th:block th:text="${camp.campStateText}"></th:block></div>
                                            </th:block>
                                            <th:block th:if="${camp.campStateText eq '已结束'}">
                                                <div class="badge badge-danger camp-state"><th:block th:text="${camp.campStateText}"></th:block></div>
                                            </th:block>
                                        </div>
                                        <div class="text" style="width:100%;">
                                            <div class="font16 font-weight-bold pt-0 pb-1 text-dark" th:text="${camp.campName}"></div>
                                            <div class="text-muted font12 mt-1">
                                                开营时间：<th:block th:text="${#dates.format(camp.startDate,'yyyy-MM-dd')}"></th:block>
                                                <span class="badge badge-outline-danger pull-right camp-detail">查看详细</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <th:block th:unless="${itemStat.last}"><div class="divider dashed large mt-2 mb-2"></div></th:block>
                        </th:block>
                    </div>
                </th:block>
            </th:block>
        </div>
    </div>
    <th:block th:insert="~{layouts/footCamp}"/>
</th:block>
<th:block layout:fragment="common_js">

</th:block>
</body>
</html>