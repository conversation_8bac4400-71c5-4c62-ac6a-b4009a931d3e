<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* CSS变量定义 */
        :root {
            --primary-color: #6f42c1;
            --primary-gradient: linear-gradient(135deg, #6f42c1 0%, #8b5cf6 100%);
            --primary-hover: #5a2d91;
        }

        /* 通用隐藏类 */
        .hide {
            display: none !important;
        }

        body {
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            background-attachment: scroll;
            transition: background-image 0.5s ease-in-out;
        }

        /* 默认背景，当没有背景图片时使用 */
        body.no-background {
            background: linear-gradient(135deg, #f8f6fc 0%, #f0ebf8 100%);
        }

        /* 背景图片遮罩层 - 增强可读性 */
        body.has-background::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.15);
            z-index: -1;
            pointer-events: none;
            backdrop-filter: blur(1px);
        }

        /* 响应式背景图片适配 */
        @media (max-width: 768px) {
            body {
                background-attachment: scroll;
                background-size: cover;
                background-position: center top;
            }
        }

        @media (max-width: 480px) {
            body {
                background-size: cover;
                background-position: center center;
            }
        }

        @media (orientation: landscape) and (max-height: 600px) {
            body {
                background-size: cover;
                background-position: center center;
                background-attachment: scroll;
            }
        }

        /* 高分辨率屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            body {
                background-size: cover;
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }

        /* 超宽屏幕适配 */
        @media (min-width: 1200px) {
            body {
                background-size: cover;
                background-position: center center;
            }
        }

        /* 平板设备适配 */
        @media (min-width: 481px) and (max-width: 1024px) {
            body {
                background-size: cover;
                background-position: center center;
                background-attachment: scroll;
            }
        }

        /* iPhone X 及类似设备的刘海屏适配 */
        @media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
            body {
                background-size: cover;
                background-position: center center;
            }
        }

        /* iPad 适配 */
        @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
            body {
                background-size: cover;
                background-position: center center;
            }
        }

        /* 极小屏幕设备 */
        @media (max-width: 320px) {
            body {
                background-size: cover;
                background-position: center top;
            }
        }

        /* 背景图片加载状态 */
        body.background-loading {
            background: linear-gradient(135deg, #f8f6fc 0%, #f0ebf8 100%);
            position: relative;
        }

        body.background-loading::after {
            content: '';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 3px solid rgba(111, 66, 193, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: backgroundSpin 1s linear infinite;
            z-index: 1000;
            opacity: 0.7;
        }

        @keyframes backgroundSpin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        #appCapsule {
            max-width: 768px;
            margin: 0 auto;
            padding-bottom: 100px !important;
            padding-top: 20px;
            background: transparent;
        }

        #chatMessages {
            padding: 15px 0;
            background: transparent;
        }

        .chatItem {
            margin-bottom: 24px;
            display: flex;
            flex-direction: column;
            position: relative;
            max-width: 85%;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInFromBottom {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .chatItem.user {
            align-items: flex-end;
            align-self: flex-end;
            margin-left: auto;
        }

        .chatItem:not(.user) {
            align-items: flex-start;
            margin-right: auto;
        }

        .bubble {
            padding: 14px 18px;
            border-radius: 20px;
            word-break: break-word;
            line-height: 1.6;
            position: relative;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
            backdrop-filter: blur(10px);
        }

        .chatItem:not(.user) .bubble {
            background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,249,250,0.98) 100%);
            color: #2c3e50;
            border-top-left-radius: 6px;
            margin-left: 45px;
            border: 1px solid rgba(111, 66, 193, 0.15);
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .chatItem:not(.user) .bubble:before {
            content: "";
            position: absolute;
            left: -8px;
            top: 8px;
            border-top: 8px solid #ffffff;
            border-left: 8px solid transparent;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.05));
        }

        .chatItem.user .bubble {
            background: var(--primary-gradient);
            color: white;
            border-top-right-radius: 6px;
            box-shadow: 0 4px 16px rgba(111, 66, 193, 0.3);
        }

        .chatItem.user .bubble:before {
            content: "";
            position: absolute;
            right: -8px;
            top: 8px;
            border-top: 8px solid var(--primary-color);
            border-right: 8px solid transparent;
        }

        .chatItem footer {
            font-size: 11px;
            color: #999;
            margin-top: 6px;
            padding: 0 5px;
        }

        .suggested-list button {
            border-radius: 25px;
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,246,252,0.95) 100%);
            border: 2px solid rgba(111, 66, 193, 0.2);
            color: var(--primary-color);
            padding: 12px 18px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 3px 12px rgba(111, 66, 193, 0.15);
            backdrop-filter: blur(15px);
        }

        .suggested-list button:hover {
            background: var(--primary-gradient);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
        }

        /* 头像区域重新设计 */
        .sectionTitle {
            padding: 0;
            background: transparent;
            margin: 0;
            border-radius: 0;
            position: relative;
            overflow: visible;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* 头像主容器 */
        .avatar-main-container {
            position: relative;
            margin: 30px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* 头像容器 */
        .avatar-container {
            position: relative;
            display: inline-block;
            margin-bottom: 15px;
        }

        /* 头像样式 - 圆形显示与agentInfo页面一致 */
        .sectionTitle .imageBlock.rounded-circle {
            width: 200px !important;
            height: 200px !important;
            border: 3px solid white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            object-fit: cover;
            border-radius: 50% !important;
        }

        .sectionTitle .imageBlock.rounded-circle:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
        }

        /* 移除复杂的光晕效果 */
        .sectionTitle::before,
        .sectionTitle::after {
            display: none;
        }

        /* 顶部导航栏 */
        .header-controls {
            position: fixed;
            top: 10px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
        }

        .control-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover {
            background: white;
            color: var(--primary-color);
            transform: scale(1.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .control-btn:active {
            transform: scale(1.05);
        }

        /* 顶部标题 */
        .header-title {
            flex: 1;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            padding: 12px 20px;
            border-radius: 25px;
            margin: 0 15px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.6);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: all 0.3s ease;
            position: relative;
        }

        .header-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.05) 0%, rgba(248, 246, 252, 0.05) 100%);
            border-radius: 25px;
            z-index: -1;
        }

        /* 标题中的智能体名称高亮 */
        .header-title span {
            font-weight: 700;
        }

        /* 默认显示完整标题 */
        .header-title-full {
            display: block;
        }

        .header-title-short {
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .chatItem {
                max-width: 92%;
            }

            .bubble {
                padding: 12px 16px;
                font-size: 14px;
            }

            .sectionTitle {
                min-height: 160px;
                margin: 0;
            }

            .avatar-main-container {
                margin: 20px 0;
            }

            .sectionTitle .imageBlock.rounded-circle {
                width: 150px !important;
                height: 150px !important;
                border: 2px solid white;
                border-radius: 50% !important;
            }

            .header-controls {
                padding: 0 15px;
                top: 10px;
            }

            .control-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .header-title {
                font-size: 14px;
                padding: 10px 15px;
                margin: 0 10px;
            }







            .message-list {
                max-height: 300px;
                padding: 12px 5px;
                gap: 12px;
                /* 移动端触摸滚动增强 */
                overscroll-behavior: contain;
                touch-action: pan-y;
            }

            .message {
                max-width: 90%;
            }

            .user-message, .bot-message {
                padding: 12px 16px;
                font-size: 13px;
                border-radius: 20px;
                text-align: left;
                margin-left: 18px;
                margin-right: 18px;
            }

            .typing-indicator {
                padding: 12px 16px;
                font-size: 13px;
                text-align: left;
                margin-left: 18px;
                margin-right: 18px;
            }
        }

        /* 更小屏幕的适配 */
        @media (max-width: 360px) {
            .sectionTitle {
                min-height: 140px;
            }

            .avatar-main-container {
                margin: 15px 0;
            }

            .sectionTitle .imageBlock.rounded-circle {
                width: 160px !important;
                height: 160px !important;
                border: 2px solid white;
                border-radius: 50% !important;
            }

            .header-controls {
                padding: 0 10px;
                top: 10px;
            }

            .control-btn {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .header-title {
                font-size: 12px;
                padding: 8px 10px;
                margin: 0 6px;
                border-radius: 20px;
            }

            /* 极小屏幕下简化标题文字 */
            .header-title-full {
                display: none;
            }

            .header-title-short {
                display: block;
            }

            .agent-name {
                font-size: 16px;
                margin-top: 6px;
            }
        }

        /* 隐藏所有滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }

        /* 为支持的浏览器隐藏滚动条 */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 确保滚动功能仍然可用 */
        body, html {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }




        .audio-bars {
            display: flex;
            align-items: center;
            gap: 3px;
            height: 40px;
        }

        .audio-bar {
            width: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
            animation: audioBarAnimation 0.8s ease-in-out infinite;
            transform-origin: bottom;
        }

        .audio-bar:nth-child(1) { animation-delay: 0s; }
        .audio-bar:nth-child(2) { animation-delay: 0.1s; }
        .audio-bar:nth-child(3) { animation-delay: 0.2s; }
        .audio-bar:nth-child(4) { animation-delay: 0.3s; }
        .audio-bar:nth-child(5) { animation-delay: 0.4s; }
        .audio-bar:nth-child(6) { animation-delay: 0.5s; }
        .audio-bar:nth-child(7) { animation-delay: 0.6s; }
        .audio-bar:nth-child(8) { animation-delay: 0.7s; }
        .audio-bar:nth-child(9) { animation-delay: 0.8s; }
        .audio-bar:nth-child(10) { animation-delay: 0.9s; }

        @keyframes audioBarAnimation {
            0%, 100% {
                height: 8px;
                opacity: 0.6;
            }
            50% {
                height: 35px;
                opacity: 1;
            }
        }





        /* 横向线段脉冲特效 */
        .pulse-wave-container {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2000;
            width: 200px;
            height: 60px;
        }

        .pulse-wave {
            width: 4px;
            height: 20px;
            background: rgba(111, 66, 193, 0.8);
            border-radius: 2px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: horizontalPulse 1.2s ease-in-out infinite;
        }

        .pulse-wave:nth-child(1) { animation-delay: 0s; left: 40%; }
        .pulse-wave:nth-child(2) { animation-delay: 0.1s; left: 45%; }
        .pulse-wave:nth-child(3) { animation-delay: 0.2s; left: 50%; }
        .pulse-wave:nth-child(4) { animation-delay: 0.3s; left: 55%; }
        .pulse-wave:nth-child(5) { animation-delay: 0.4s; left: 60%; }

        @keyframes horizontalPulse {
            0%, 100% {
                transform: translate(-50%, -50%) scaleY(0.3);
                opacity: 0.4;
            }
            50% {
                transform: translate(-50%, -50%) scaleY(2);
                opacity: 1;
            }
        }

        /* 消息列表样式美化 - 简洁版 */
        .message-list {
            max-height: 400px;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 15px 5px;
            margin: 10px 0;
            display: flex;
            flex-direction: column;
            gap: 14px;
            position: relative;
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            /* 启用平滑滚动 */
            scroll-behavior: smooth;
            /* 移动端触摸滚动优化 */
            -webkit-overflow-scrolling: touch;
            padding-bottom: 100px; /* 新增，防止底部消息被按钮遮挡 */
        }

        /* 隐藏 Webkit 浏览器的滚动条 */
        .message-list::-webkit-scrollbar {
            display: none;
        }

        /* 滑动提示样式 */
        .scroll-hint {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 100;
        }

        .message-list.show-hint .scroll-hint {
            opacity: 1;
        }

        /* 渐变遮罩，提示可滚动内容 */
        .message-list::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(to bottom, rgba(255,255,255,0.8), transparent);
            pointer-events: none;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message-list.has-scroll::before {
            opacity: 1;
        }

        .message {
            animation: messageSlideIn 0.4s ease-out;
            position: relative;
            max-width: 85%;
            word-wrap: break-word;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 用户消息样式 - 简洁气泡 */
        .user-message {
            align-self: flex-start;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 14px 18px;
            border-radius: 25px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.8);
            font-size: 14px;
            line-height: 1.4;
            font-weight: 500;
            margin-left: 25px;
            margin-right: 25px;
            text-align: left;
        }

        /* AI消息样式 - 简洁气泡 */
        .bot-message {
            align-self: flex-start;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 14px 18px;
            border-radius: 25px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.8);
            font-size: 14px;
            line-height: 1.4;
            font-weight: 500;
            margin-left: 25px;
            margin-right: 25px;
            text-align: left;
        }

        /* 悬停效果 */
        .user-message:hover,
        .bot-message:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            transition: all 0.2s ease;
        }

        /* 打字动画效果 - 简洁版 */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 14px 18px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.8);
            max-width: 85%;
            align-self: flex-start;
            margin-left: 25px;
            margin-right: 25px;
            font-size: 14px;
            text-align: left;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
            animation: typingAnimation 1.4s ease-in-out infinite;
        }

        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingAnimation {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.3;
            }
            30% {
                transform: translateY(-8px);
                opacity: 1;
            }
        }

        /* 在现有CSS后追加 */
        .chatFooter .row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        .chatFooter .col-6 {
            flex: unset;
            width: auto;
            padding: 0;
        }

        /* 增强chatFooter按钮居中效果 */
        .chatFooter {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding-bottom: 20px;
            background: transparent;
        }
        .chatFooter .row {
            width: auto;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        .chatFooter .col-6 {
            flex: unset;
            width: auto;
            padding: 0;
            display: flex;
            justify-content: center;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <div id="appCapsule">
        <div class="appContent">
            <!-- 顶部导航栏 -->
            <div class="header-controls">
                <a href="#" class="control-btn back-btn goBack">
                    <i class="fa fa-angle-left"></i>
                </a>
                <div class="header-title">
                    <span class="header-title-full">
                        与 <span th:if="${agentInfo != null}" th:text="${agentInfo.name}">智能体</span> 对话中
                    </span>
                    <span class="header-title-short" th:if="${agentInfo != null}" th:text="${agentInfo.name}"></span>
                </div>
                <a href="#" onclick="showText()" class="control-btn call-btn">字幕</a>
            </div>
            <div class="section" style="padding-top:80px">
                <!-- 头像区域 - 移动到顶部 -->
                <div class="sectionTitle text-center">
                    <div class="avatar-main-container">
                        <!-- 头像容器 -->
                        <img th:src="${agentInfo.icon_url }" alt="profile" class="imageBlock rounded-circle xlarge" />
                    </div>
                </div>
                <div class="splashBlock">
                    <!-- 横向线段脉冲特效 -->
                    <div class="pulse-wave-container" id="pulseWaveContainer">
                        <div class="pulse-wave"></div>
                        <div class="pulse-wave"></div>
                        <div class="pulse-wave"></div>
                        <div class="pulse-wave"></div>
                        <div class="pulse-wave"></div>
                    </div>

                    <!-- 通话状态显示 -->
                    <div class="call-status" id="callStatus" style="display: none; text-align: center; padding: 20px;">
                        <div id="callStatusText">正在连接...</div>
                    </div>

                    <!-- 美化的消息列表 -->
                    <div class="message-list hide" id="messageList">
                        <div class="scroll-hint">👆 滑动查看历史消息</div>
                    </div>
                </div>
            </div>

        </div>
        <div class="container hide">
            <div class="card">
                <div id="status" class="status">状态：等待开始</div>
            </div>
        </div>
        <div class="chatFooter">
            <div class="row">
                <div class="col-6">
                    <button type="button" class="btn btn-primary btn-block rounded" id="startBtn">开始通话</button>
                </div>
                <div class="col-6">
                    <button type="button" class="btn btn-secondary btn-block rounded" id="stopBtn">结束通话</button>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script>
        // 配置信息
        const config = {
            pat: "[[${accessToken}]]",
            botId: getUrlParam('botId'),
            voiceId: "[[${agentInfo.voice_info_list[0].voice_id}]]",
            baseWsUrl: "wss://ws.coze.cn",
        };

        // 音频上下文
        let audioContext = null; // 优化：全局只创建一次
        let audioStream;
        let audioProcessor;
        let isRecording = false;
        let webSocket = null;
        let analyser;
        let audioLevelInterval;
        let pcmPlayer = null;
        let silenceTimer = null;
        let lastSpeechTime = 0;
        let silenceThreshold = 25; // 提高静音阈值，减少噪音触发
        let silenceTimeout = 1500; // 静音超过1.5秒自动发送完成信号
        let responseReceived = false;
        let sessionId = "session_" + Date.now(); // 全局session_id变量
        let lastTranscriptionText = ""; // 保存最新的识别文本
        let isGreetingMessage = false; // 标记是否是开场白消息
        let currentBotMessage = ""; // 当前机器人消息

        // 初始化
        $(document).ready(function () {
            console.log("页面加载完成，开始初始化");
            // 请求麦克风权限
            requestMicrophonePermission();

            // 按钮事件
            $("#startBtn").click(startConversation);
            $("#stopBtn").click(stopConversation);

            // 初始状态设置
            $("#stopBtn").prop("disabled", true);

            console.log("按钮事件绑定完成");
        });

        // 请求麦克风权限
        function requestMicrophonePermission() {
            console.log("请求麦克风权限");
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    audioStream = stream;
                    console.log("麦克风权限获取成功");
                    updateStatus("麦克风权限已获取，可以开始通话");
                })
                .catch(error => {
                    console.error("麦克风权限获取失败:", error);
                    updateStatus("获取麦克风权限失败: " + error.message);
                    $("#startBtn").prop("disabled", true);
                });
        }

        // 开始对话
        function startConversation() {
            console.log("开始对话");
            if (!audioStream) {
                console.error("麦克风未就绪，无法开始通话");
                updateStatus("麦克风未就绪，无法开始通话");
                return;
            }

            // 更新按钮状态和样式
            $("#startBtn").prop("disabled", true);
            $("#startBtn").removeClass("btn-primary").addClass("btn-secondary");
            $("#stopBtn").prop("disabled", false);
            $("#stopBtn").removeClass("btn-secondary").addClass("btn-danger");

            // 显示通话状态
            $("#callStatus").show();
            updateCallStatus("正在连接...");

            // 关键：所有音频初始化都放在resume回调里
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 24000 });
            }
            audioContext.resume().then(() => {
                console.log('AudioContext已解锁');
                initAudioContext();      // 先初始化音频处理
                connectWebSocket();      // 再连接WebSocket
                startAudioLevelMonitoring();
                console.log("开始监测音频电平");
            });
        }

        // 停止对话
        function stopConversation() {
            console.log("停止对话");

            // 清除静音计时器
            if (silenceTimer) {
                clearTimeout(silenceTimer);
                silenceTimer = null;
            }

            if (webSocket) {
                console.log("关闭语音识别WebSocket");
                webSocket.close();
                webSocket = null;
            }

            if (window.chatWs) {
                console.log("关闭聊天WebSocket");
                window.chatWs.close();
                window.chatWs = null;
            }

            if (audioProcessor) {
                console.log("断开音频处理器");
                audioProcessor.disconnect();
                audioProcessor = null;
            }

            if (analyser) {
                console.log("断开音频分析器");
                analyser.disconnect();
            }

            if (audioLevelInterval) {
                console.log("停止音频电平监测");
                clearInterval(audioLevelInterval);
                audioLevelInterval = null;
            }

            if (pcmPlayer) {
                console.log("销毁PCM播放器");
                pcmPlayer.destroy();
                pcmPlayer = null;
            }

            isRecording = false;
            updateStatus("通话已结束");

            // 恢复按钮状态和样式
            $("#startBtn").prop("disabled", false);
            $("#startBtn").removeClass("btn-secondary").addClass("btn-primary");
            $("#stopBtn").prop("disabled", true);
            $("#stopBtn").removeClass("btn-danger").addClass("btn-secondary");

            // 隐藏通话状态和脉冲波形
            $("#callStatus").hide();
            hidePulseWave();
            console.log("隐藏脉冲波形动画");

            console.log("重置音频电平");
        }

        // 打断对话
        function interruptConversation() {
            console.log("打断对话");

            if (!window.chatWs || window.chatWs.readyState !== WebSocket.OPEN) {
                console.error("聊天WebSocket未连接，无法打断对话");
                updateStatus("聊天WebSocket未连接，无法打断对话");
                return;
            }

            try {
                // 发送打断消息
                const interruptMessage = {
                    id: "interrupt_" + Date.now(),
                    event_type: "conversation.interrupt"
                };

                console.log("发送打断消息:", JSON.stringify(interruptMessage));
                window.chatWs.send(JSON.stringify(interruptMessage));
                console.log("打断消息已发送");

                // 更新状态
                updateStatus("检测到您想说话，已自动打断对话");

                // 如果有正在播放的音频，停止播放
                if (pcmPlayer && pcmPlayer.isPlaying) {
                    console.log("停止当前音频播放");
                    pcmPlayer.queue = []; // 清空播放队列
                    pcmPlayer.isPlaying = false;
                }

                // 如果静音计时器存在，清除它
                if (silenceTimer) {
                    clearTimeout(silenceTimer);
                    silenceTimer = null;
                    console.log("静音计时器已清除");
                }

            } catch (error) {
                console.error("打断对话失败:", error);
                updateStatus("打断对话失败: " + error.message);
            }
        }

        // 初始化音频上下文
        function initAudioContext() {
            console.log("初始化音频上下文");
            // audioContext已全局唯一创建，无需重复创建
            const source = audioContext.createMediaStreamSource(audioStream);

            // 创建分析器节点，用于显示音频电平
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;
            source.connect(analyser);

            // 创建处理节点，优先用1024，降级兼容
            let bufferSize = 1024;
            let processor = null;
            try {
                processor = audioContext.createScriptProcessor(bufferSize, 1, 1);
            } catch (e1) {
                try {
                    bufferSize = 2048;
                    processor = audioContext.createScriptProcessor(bufferSize, 1, 1);
                } catch (e2) {
                    bufferSize = 4096;
                    processor = audioContext.createScriptProcessor(bufferSize, 1, 1);
                }
            }
            audioProcessor = processor;

            // 处理音频数据
            audioProcessor.onaudioprocess = function (e) {
                if (!isRecording || !webSocket || webSocket.readyState !== WebSocket.OPEN) return;

                const inputData = e.inputBuffer.getChannelData(0);
                const pcmData = convertFloat32ToInt16(inputData);

                try {
                    // 发送音频数据
                    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
                        // 使用推荐的Base64编码方法
                        const raw = pcmData.buffer;
                        const base64String = btoa(
                            Array.from(new Uint8Array(raw))
                                .map(byte => String.fromCharCode(byte))
                                .join('')
                        );

                        // 发送Base64格式的数据
                        const message = {
                            id: "audio_" + Date.now(),
                            event_type: "input_audio_buffer.append",
                            data: {
                                delta: base64String
                            }
                        };

                        if (Math.random() < 0.01) { // 仅记录少量消息，避免日志过多
                            console.log(`发送音频数据: ${base64String.length} 字符, id: ${message.id}`);
                        }

                        webSocket.send(JSON.stringify(message));
                    }
                } catch (error) {
                    console.error("发送音频数据失败:", error);
                }
            };

            // 连接节点
            source.connect(audioProcessor);
            audioProcessor.connect(audioContext.destination);
            console.log("音频上下文初始化完成，bufferSize:", bufferSize);
        }

        // 监测音频电平
        function startAudioLevelMonitoring() {
            if (!analyser) {
                console.warn("音频分析器未初始化，无法监测音频电平");
                return;
            }

            console.log("开始音频电平监测");
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            lastSpeechTime = Date.now(); // 初始化最后说话时间
            let hasSpeechActivity = false; // 是否检测到语音活动
            let isBotSpeaking = false; // 机器人是否正在说话
            let shouldInterrupt = false; // 是否应该打断对话
            let speechStartTime = null; // 新增：语音活动起始时间

            audioLevelInterval = setInterval(() => {
                analyser.getByteFrequencyData(dataArray);

                // 计算平均音量
                let sum = 0;
                for (let i = 0; i < dataArray.length; i++) {
                    sum += dataArray[i];
                }
                const average = sum / dataArray.length; // 修复：补上average定义
                const level = Math.min(100, Math.max(0, average * 1.5));

                // 检测语音活动
                if (level > silenceThreshold) {
                    if (!speechStartTime) {
                        speechStartTime = Date.now();
                    }
                    // 只有持续超过300ms才算真正说话
                    if (Date.now() - speechStartTime > 300) {
                        lastSpeechTime = Date.now();
                        if (silenceTimer) {
                            clearTimeout(silenceTimer);
                            silenceTimer = null;
                        }
                        if (!hasSpeechActivity) {
                            hasSpeechActivity = true;
                            console.log("检测到语音活动开始");
                        }
                        // 本地不再自动打断AI，仅用于UI反馈
                        // if (pcmPlayer && pcmPlayer.isPlaying) {
                        //     isBotSpeaking = true;
                        //     if (!shouldInterrupt) {
                        //         shouldInterrupt = true;
                        //         setTimeout(() => {
                        //             if (hasSpeechActivity && shouldInterrupt) {
                        //                 interruptConversation();
                        //                 shouldInterrupt = false;
                        //             }
                        //         }, 200);
                        //     }
                        // }
                        enhancedVoiceActivityDetection(level);
                        if (level > 30) {
                            console.log("检测到较高音量:", Math.round(level) + "%");
                        }
                    }
                } else {
                    speechStartTime = null;
                    const silenceDuration = Date.now() - lastSpeechTime;
                    shouldInterrupt = false;
                    if (hasSpeechActivity && silenceDuration > 500 && !silenceTimer && isRecording) {
                        console.log("检测到语音活动结束，设置静音计时器");
                        silenceTimer = setTimeout(() => {
                            console.log("检测到持续静音，自动发送完成信号");
                            pauseRecordingAndSendComplete();
                            silenceTimer = null;
                            hasSpeechActivity = false; // 重置语音活动标记
                        }, silenceTimeout);
                    }
                }
                if (pcmPlayer) {
                    isBotSpeaking = pcmPlayer.isPlaying;
                }
            }, 100);
        }

        // 连接WebSocket
        function connectWebSocket() {
            const wsUrl = config.baseWsUrl + "/v1/audio/transcriptions?authorization=Bearer " + config.pat;
            console.log("连接语音识别WebSocket:", wsUrl);

            webSocket = new WebSocket(wsUrl);

            webSocket.onopen = function () {
                console.log("语音识别WebSocket已连接");
                updateStatus("WebSocket已连接，正在初始化会话...");

                // 发送初始化消息
                const initMessage = {
                    id: "init",
                    event_type: "transcriptions.update",
                    data: {
                        input_audio: {
                            format: "pcm",
                            codec: "pcm",
                            sample_rate: 24000,
                            channel: 1,
                            bit_depth: 16
                        }
                    }
                };

                console.log("发送语音识别初始化消息:", initMessage);
                webSocket.send(JSON.stringify(initMessage));
            };

            webSocket.onmessage = function (event) {
                const message = JSON.parse(event.data);
                console.log("接收语音识别消息:", message);

                // 添加调用堆栈追踪，帮助调试
                if (message.event_type === "transcriptions.message.completed" ||
                    message.event_type === "input_audio_buffer.completed") {
                    console.trace("WebSocket消息流程追踪");
                }

                if (message.event_type === "transcriptions.created") {
                    // 初始化完成，开始录音
                    isRecording = true;
                    updateStatus("录音已就绪，可以开始说话");
                    updateCallStatus("已连接，可以开始对话");
                    console.log("语音识别初始化完成，开始录音");

                    // 开始聊天会话
                    startChatSession();
                }
                else if (message.event_type === "transcriptions.message.update") {
                    // 语音识别结果
                    if (message.data && message.data.content) {
                        const transcriptionText = message.data.content;
                        console.log("语音识别结果:", transcriptionText);

                        // 更新lastTranscriptionText
                        lastTranscriptionText = transcriptionText;
                        console.log("更新最新识别文本为:", lastTranscriptionText);

                        // 显示临时识别结果
                        updateStatus("识别中: " + transcriptionText);
                    }
                }
                else if (message.event_type === "transcriptions.message.completed") {
                    // 语音识别完成
                    console.log("语音识别完成，完整数据:", message);

                    // 设置响应标志，表示已收到完成响应
                    responseReceived = true;
                    console.log("设置完成信号响应标志为true (transcriptions.message.completed)");

                    // 定义最终识别文本
                    let finalText = "";

                    try {
                        // 首先检查是否有lastTranscriptionText
                        if (lastTranscriptionText && lastTranscriptionText.trim()) {
                            finalText = lastTranscriptionText;
                            console.log("使用update事件中保存的最新识别文本:", finalText);
                        }
                        // 如果没有lastTranscriptionText，尝试从completed事件中获取
                        else if (message && message.data) {
                            if (typeof message.data.content === 'string') {
                                finalText = message.data.content;
                                console.log("从message.data.content获取结果:", finalText);
                            } else if (typeof message.data === 'string') {
                                finalText = message.data;
                                console.log("从message.data字符串获取结果:", finalText);
                            }
                        } else if (message && typeof message.content === 'string') {
                            finalText = message.content;
                            console.log("从message.content获取结果:", finalText);
                        }
                    } catch (error) {
                        console.error("提取识别结果时出错:", error);
                    }

                    console.log("最终识别结果:", finalText);
                    console.log("设置完成信号响应标志为true:", responseReceived);

                    // 保存最新的已完成消息，便于调试
                    window.lastCompletedMessage = message;

                    // 清空lastTranscriptionText，防止下次复用
                    const savedText = lastTranscriptionText;
                    lastTranscriptionText = "";
                    console.log("已清空最新识别文本");

                    // 显示用户消息并发送到聊天
                    if (finalText && finalText.trim()) {
                        console.log("添加用户消息到UI:", finalText);
                        // 使用美化的消息显示函数
                        const messageElement = $("<div>")
                            .addClass("message user-message")
                            .text(finalText)
                            .hide()
                            .fadeIn(400);
                        $("#messageList").append(messageElement);
                        scrollToBottom();

                        // 发送到聊天WebSocket
                        try {
                            console.log("发送识别结果到聊天:", finalText);
                            if (window.chatWs && window.chatWs.readyState === WebSocket.OPEN) {
                                sendUserMessage(finalText);
                            } else {
                                console.error("聊天WebSocket未连接，尝试重新连接并发送消息");
                                startChatSession(); // 重新建立连接

                                // 等待连接建立后再次尝试发送
                                setTimeout(() => {
                                    if (window.chatWs && window.chatWs.readyState === WebSocket.OPEN) {
                                        console.log("WebSocket已重连，尝试发送消息");
                                        sendUserMessage(finalText);
                                    } else {
                                        console.error("重连失败，无法发送识别结果");
                                    }
                                }, 1000);
                            }
                        } catch (error) {
                            console.error("发送识别结果到聊天失败:", error);
                        }

                        // 更新状态
                        updateStatus("录音已就绪，可以继续说话");
                    } else {
                        console.warn("未检测到有效语音内容");
                        updateStatus("未检测到有效语音，请重试");
                    }
                }
                else if (message.event_type === "input_audio_buffer.speech.started") {
                    // 检测到语音开始
                    console.log("检测到语音输入开始");
                    updateStatus("正在说话...");
                }
                else if (message.event_type === "input_audio_buffer.speech.stopped") {
                    // 检测到语音结束
                    console.log("检测到语音输入结束");
                    updateStatus("语音输入结束，正在识别...");

                    // 如果最后一次识别结果存在，保存它
                    if (lastTranscriptionText) {
                        console.log("语音结束时保存最后识别结果:", lastTranscriptionText);
                    }

                    // 自动发送完成信号，延迟500毫秒确保所有音频数据都已发送
                    setTimeout(() => {
                        pauseRecordingAndSendComplete();
                    }, 500);
                }
                else if (message.event_type === "input_audio_buffer.complete.received") {
                    // 完成信号已接收
                    console.log("服务器已接收完成信号");
                    responseReceived = true; // 设置响应标志
                    console.log("设置完成信号响应标志为true");
                }
                else if (message.event_type === "input_audio_buffer.completed") {
                    // 语音输入完成处理
                    console.log("收到语音识别消息:", message.event_type, "id:", message.id, "detail:", message.detail);
                    responseReceived = true;

                    // 记录最后收到的消息用于调试
                    window.lastReceivedMessage = message;
                }
                else if (message.event_type === "error") {
                    console.error("语音识别错误:", message.data);
                    const errorMsg = message.data && message.data.msg ? message.data.msg : "未知错误";
                    updateStatus("错误: " + errorMsg);

                    // 如果是格式错误，尝试自动恢复
                    if (errorMsg.includes("format is invalid")) {
                        console.log("音频格式错误，尝试重新初始化...");
                        // 3秒后尝试重新初始化
                        setTimeout(() => {
                            if (isRecording) {
                                console.log("重新初始化WebSocket连接...");
                                webSocket.close();
                                connectWebSocket();
                            }
                        }, 3000);
                    }
                }
                else if (message.event_type === "transcriptions.get" || message.event_type === "transcriptions.get.result") {
                    // 处理获取的识别结果
                    console.log("收到识别结果查询响应:", message);

                    let resultText = "";

                    try {
                        if (message.data && message.data.content) {
                            resultText = message.data.content;
                        } else if (message.content) {
                            resultText = message.content;
                        }

                        // 只有在有真实识别结果时才进行处理
                        if (resultText && resultText.trim()) {
                            console.log("查询获取的识别结果:", resultText);

                            // 更新到UI，发送到聊天
                            const messageElement = $("<div>")
                                .addClass("message user-message")
                                .text(resultText)
                                .hide()
                                .fadeIn(400);
                            $("#messageList").append(messageElement);
                            scrollToBottom();

                            // 发送到聊天
                            if (window.chatWs && window.chatWs.readyState === WebSocket.OPEN) {
                                sendUserMessage(resultText);
                            }

                            // 更新状态
                            updateStatus("录音已就绪，可以继续说话");
                        } else {
                            console.warn("获取到的识别结果为空");
                            updateStatus("未获取到识别结果，请重试");
                        }
                    } catch (error) {
                        console.error("处理识别结果响应出错:", error);
                    }
                }
                else {
                    console.log("未处理的消息类型:", message.event_type);
                }
            };

            webSocket.onerror = function (error) {
                console.error("语音识别WebSocket错误:", error);
                updateStatus("WebSocket错误: 连接异常，尝试重新连接...");

                // 尝试重新连接
                setTimeout(() => {
                    if (isRecording) {
                        console.log("尝试重新连接WebSocket...");
                        webSocket.close();
                        connectWebSocket();
                    }
                }, 2000);
            };

            webSocket.onclose = function (event) {
                console.log("语音识别WebSocket已关闭:", event.code, event.reason);
                updateStatus("WebSocket连接已关闭");
                isRecording = false;

                // 异常关闭时尝试重新连接
                if (event.code !== 1000 && event.code !== 1001) {
                    console.log("WebSocket异常关闭，尝试重新连接...");
                    setTimeout(() => {
                        if ($("#stopBtn").prop("disabled") === false) {
                            connectWebSocket();
                        }
                    }, 3000);
                }
            };
        }

        // 开始聊天会话
        function startChatSession() {
            // 构建聊天WebSocket URL
            let chatUrl = config.baseWsUrl + "/v1/chat?bot_id=" + config.botId + "&authorization=Bearer " + config.pat;

            console.log("连接聊天WebSocket:", chatUrl);
            const chatWs = new WebSocket(chatUrl);

            // 添加状态变化监听
            chatWs.addEventListener('open', function () {
                console.log("聊天WebSocket已连接 [状态]:", chatWs.readyState);
            });

            chatWs.addEventListener('close', function (event) {
                console.log("聊天WebSocket已关闭 [状态]:", chatWs.readyState, "代码:", event.code, "原因:", event.reason);
            });

            chatWs.addEventListener('error', function (event) {
                console.error("聊天WebSocket错误 [状态]:", chatWs.readyState, "错误:", event);
            });

            chatWs.onopen = function () {
                console.log("聊天WebSocket已连接，准备发送初始化消息");
                // 发送初始化消息
                const initMessage = {
                    id: "init",
                    event_type: "chat.update",
                    data: {
                        output_audio: {
                            voice_id: config.voiceId
                        },
                        input_audio: {
                            format: "pcm",
                            codec: "pcm",
                            sample_rate: 24000,
                            channel: 1,
                            bit_depth: 16
                        },
                        chat_config: {
                            auto_save_history: true,
                            timeout_seconds: 300, // 增加超时时间到5分钟
                            enable_audio: true
                        },
                        turn_detection: {
                            interrupt_config: {
                                mode: "keyword_contains",
                                keywords: ["你好"]
                            }
                        }
                    }
                };

                console.log("发送聊天初始化消息:", JSON.stringify(initMessage));
                try {
                    chatWs.send(JSON.stringify(initMessage));
                    console.log("聊天初始化消息发送成功");

                    // 发送一个连接测试消息
                    setTimeout(() => {
                        if (chatWs.readyState === WebSocket.OPEN) {
                            console.log("聊天WebSocket连接已建立，就绪状态");
                            // 不再发送测试消息，避免无效事件类型
                        }
                    }, 500);
                } catch (error) {
                    console.error("发送聊天初始化消息失败:", error);
                }
            };

            let currentBotMessage = "";

            chatWs.onmessage = function (event) {
                const message = JSON.parse(event.data);
                console.log("接收聊天消息:", message);

                if (message.event_type === "chat.created") {
                    console.log("聊天初始化完成，发送开场白");

                    // 标记下一条消息是开场白
                    isGreetingMessage = true;
                    console.log("标记开场白消息标志");

                    // 发送初始消息，触发智能体的开场白
                    const startMessage = {
                        id: "start",
                        event_type: "conversation.message.create",
                        data: {
                            role: "user",
                            content_type: "object_string",
                            content: '[{"type":"text","text":"你好"}]'
                        }
                    };

                    console.log("发送开场白消息:", JSON.stringify(startMessage));
                    try {
                        chatWs.send(JSON.stringify(startMessage));
                        console.log("开场白消息发送成功");
                    } catch (error) {
                        console.error("发送开场白消息失败:", error);
                    }
                }
                else if (message.event_type === "conversation.message.delta") {
                    // 增量更新消息
                    const content = message.data.content;
                    console.log("接收到增量文本:", content, "当前消息长度:", currentBotMessage.length);

                    if (currentBotMessage === "") {
                        // 开始新的消息
                        currentBotMessage = content;
                        console.log("开始新的AI消息，创建消息元素");
                        addBotMessage(currentBotMessage);

                        // 重置开场白标志
                        if (isGreetingMessage) {
                            console.log("重置开场白标志");
                            isGreetingMessage = false;
                        }
                    } else {
                        // 追加内容到现有消息
                        currentBotMessage += content;
                        console.log("更新现有消息，总长度:", currentBotMessage.length);
                        updateLastBotMessage(currentBotMessage);
                    }
                }
                else if (message.event_type === "conversation.message.completed") {
                    // 消息完成
                    console.log("文本消息完成");
                    currentBotMessage = "";

                    // 确保开场白标志被重置
                    if (isGreetingMessage) {
                        console.log("重置开场白标志");
                        isGreetingMessage = false;
                    }
                }
                else if (message.event_type === "conversation.audio.delta") {
                    // 接收音频数据
                    console.log("接收到音频数据片段");
                    try {
                        const audioData = base64ToArrayBuffer(message.data.content);
                        playAudio(audioData);
                    } catch (error) {
                        console.error("音频数据处理失败:", error);
                    }
                }
                else if (message.event_type === "conversation.interrupt.completed") {
                    // 打断完成
                    console.log("打断对话完成");
                    updateStatus("打断成功，可以继续对话");

                    // 开启麦克风
                    isRecording = true;

                    // 清空音频队列
                    if (pcmPlayer) {
                        pcmPlayer.queue = [];
                        pcmPlayer.isPlaying = false;
                    }

                    // 添加系统消息到会话
                    const messageElement = $("<div>")
                        .addClass("message bot-message")
                        .text("[系统] 对话已被打断")
                        .hide()
                        .fadeIn(400);
                    $("#messageList").append(messageElement);
                    scrollToBottom();
                }
                else if (message.event_type === "error") {
                    console.error("聊天错误:", message.data);
                    updateStatus("聊天错误: " + message.data.msg);
                }
            };

            // 保存聊天WebSocket引用，并设置全局变量便于调试
            window.chatWs = chatWs;
            console.log("聊天WebSocket已保存到window.chatWs");
        }

        // 向聊天服务器发送用户消息
        function sendUserMessage(text) {
            if (!window.chatWs || window.chatWs.readyState !== WebSocket.OPEN) {
                console.error("聊天WebSocket未连接，无法发送用户消息");
                return false;
            }

            try {
                // 转义特殊字符
                const escText = text.replace(/\\/g, '\\\\')
                    .replace(/"/g, '\\"')
                    .replace(/\n/g, '\\n');

                const msgObj = {
                    id: "msg_" + Date.now(),
                    event_type: "conversation.message.create",
                    data: {
                        role: "user",
                        content_type: "object_string",
                        content: `[{"type":"text","text":"${escText}"}]`
                    }
                };

                const msgStr = JSON.stringify(msgObj);
                console.log("发送用户消息到聊天:", msgStr);
                window.chatWs.send(msgStr);
                console.log("用户消息已发送到聊天服务器");

                return true;
            } catch (error) {
                console.error("发送用户消息失败:", error);
                return false;
            }
        }

        // 添加用户消息（增强版）
        function addUserMessage(text) {
            console.log("添加用户消息:", text);

            // 移除打字指示器
            removeTypingIndicator();

            const messageElement = $("<div>")
                .addClass("message user-message")
                .text(text)
                .hide();

            $("#messageList").append(messageElement);

            // 显示消息并滚动到底部
            messageElement.fadeIn(400, function() {
                // 确保消息显示完成后再滚动
                setTimeout(scrollToBottom, 100);
            });

            // 发送到聊天WebSocket
            sendUserMessage(text);
        }

        // 添加机器人消息（增强版）
        function addBotMessage(text) {
            console.log("添加机器人消息:", text);

            // 移除打字指示器
            removeTypingIndicator();

            // 创建消息元素
            const messageElement = $("<div>")
                .addClass("message bot-message")
                .hide();

            $("#messageList").append(messageElement);

            // 直接显示消息，不使用打字机效果（避免重复显示问题）
            messageElement.text(text).fadeIn(400, () => {
                setTimeout(scrollToBottom, 100);
            });
        }

        // 更新最后一条机器人消息（增强版）
        function updateLastBotMessage(text) {
            const lastMessage = $(".bot-message").last();
            if (lastMessage.length > 0) {
                // 直接更新文本内容，避免重复显示
                lastMessage.text(text);
                scrollToBottom();
            } else {
                console.warn("没有找到最后一条机器人消息，创建新消息");
                addBotMessage(text);
            }
        }

        // 打字机效果
        function typewriterEffect(element, text, callback) {
            element.show();
            let index = 0;
            const speed = 30; // 打字速度（毫秒）

            function typeChar() {
                if (index < text.length) {
                    element.text(text.substring(0, index + 1));
                    index++;

                    // 每打几个字符就滚动一次，保持最新内容可见
                    if (index % 5 === 0) {
                        scrollToBottom();
                    }

                    setTimeout(typeChar, speed);
                } else {
                    // 打字完成，最终滚动到底部
                    scrollToBottom();
                    if (callback) {
                        callback();
                    }
                }
            }

            typeChar();
        }

        // 显示打字指示器
        function showTypingIndicator() {
            if ($(".typing-indicator").length === 0) {
                const typingElement = $(`
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `);
                $("#messageList").append(typingElement);
                scrollToBottom();
            }
        }

        // 移除打字指示器
        function removeTypingIndicator() {
            $(".typing-indicator").fadeOut(200, function() {
                $(this).remove();
            });
        }

        // 滚动到底部（增强版）
        function scrollToBottom() {
            const messageList = document.getElementById("messageList");
            if (messageList && !messageList.classList.contains('hide')) {
                // 使用requestAnimationFrame确保DOM更新完成后再滚动
                requestAnimationFrame(() => {
                    // 确保滚动到最底部
                    const targetScrollTop = messageList.scrollHeight - messageList.clientHeight;

                    // 平滑滚动到底部
                    messageList.scrollTo({
                        top: Math.max(0, targetScrollTop),
                        behavior: 'smooth'
                    });
                });
            }
        }

        // 初始化触摸滚动增强
        function initTouchScrolling() {
            const messageList = document.getElementById("messageList");
            if (messageList) {
                // 检查是否有可滚动内容
                function checkScrollable() {
                    const hasScroll = messageList.scrollHeight > messageList.clientHeight;
                    if (hasScroll) {
                        messageList.classList.add('has-scroll');
                    } else {
                        messageList.classList.remove('has-scroll');
                    }
                }

                // 显示滑动提示
                function showScrollHint() {
                    // 计算实际消息数量（排除滚动提示元素）
                    const messageCount = Array.from(messageList.children).filter(child =>
                        !child.classList.contains('scroll-hint')
                    ).length;

                    if (messageCount > 3) { // 有多条消息时显示提示
                        messageList.classList.add('show-hint');
                        setTimeout(() => {
                            messageList.classList.remove('show-hint');
                        }, 3000); // 3秒后隐藏提示
                    }
                }

                // 防止滚动时的橡皮筋效果
                messageList.addEventListener('touchstart', function(e) {
                    // 记录初始触摸位置
                    this.startY = e.touches[0].pageY;
                    this.startScrollTop = this.scrollTop;
                }, { passive: true });

                messageList.addEventListener('touchmove', function(e) {
                    // 计算滚动边界，防止过度滚动
                    const currentY = e.touches[0].pageY;
                    const deltaY = this.startY - currentY;
                    const newScrollTop = this.startScrollTop + deltaY;

                    // 如果已经在顶部或底部，阻止默认行为
                    if ((newScrollTop <= 0 && deltaY < 0) ||
                        (newScrollTop >= this.scrollHeight - this.clientHeight && deltaY > 0)) {
                        e.preventDefault();
                    }
                }, { passive: false });

                // 监听内容变化
                const observer = new MutationObserver(() => {
                    checkScrollable();
                    showScrollHint();
                });

                observer.observe(messageList, {
                    childList: true,
                    subtree: true
                });

                // 初始检查
                checkScrollable();
            }
        }

        // 确保消息顺序正确的函数
        function ensureMessageOrder() {
            const messageList = $("#messageList");
            const messages = messageList.children().not('.scroll-hint');

            console.log("当前消息数量:", messages.length);
            messages.each(function(index) {
                console.log(`消息 ${index + 1}:`, $(this).text().substring(0, 50));
            });

            // 确保滚动到最新消息
            if (messages.length > 0) {
                scrollToBottom();
            }
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            initTouchScrolling();

            // 初始化消息顺序
            ensureMessageOrder();

            // 调试：添加测试消息功能
            window.testMessage = function() {
                console.log("测试消息功能");
                const messageList = $("#messageList");
                console.log("消息列表元素:", messageList.length);
                console.log("消息列表是否隐藏:", messageList.hasClass('hide'));

                // 确保字幕显示
                if (messageList.hasClass('hide')) {
                    showText();
                }

                // 添加多条测试消息来验证对齐效果
                for (let i = 1; i <= 3; i++) {
                    setTimeout(() => {
                        const userMsg = $("<div>")
                            .addClass("message user-message")
                            .text(`[用户] 测试消息 ${i}：这是一条用户发送的测试消息，用来验证左对齐效果。`)
                            .hide();
                        messageList.append(userMsg);
                        userMsg.fadeIn(400, () => {
                            scrollToBottom();
                        });

                        // 添加对应的AI回复
                        setTimeout(() => {
                            const botMsg = $("<div>")
                                .addClass("message bot-message")
                                .text(`[AI] 回复消息 ${i}：这是AI的回复消息，同样测试左对齐效果和统一的左右间距设置。`)
                                .hide();
                            messageList.append(botMsg);
                            botMsg.fadeIn(400, () => {
                                scrollToBottom();
                            });
                        }, 500);
                    }, i * 1500);
                }
            };

            // 清空消息列表的测试函数
            window.clearMessages = function() {
                const messageList = $("#messageList");
                messageList.children().not('.scroll-hint').remove();
                console.log("消息列表已清空");
            };

            // 测试开场白功能
            window.testGreeting = function() {
                console.log("测试开场白功能");
                const messageList = $("#messageList");

                // 确保字幕显示
                if (messageList.hasClass('hide')) {
                    showText();
                }

                // 模拟开场白
                setTimeout(() => {
                    addBotMessage("你好！我是您的AI助手，很高兴为您服务。请问有什么可以帮助您的吗？");
                }, 500);
            };

            // 测试增量消息更新
            window.testIncrementalMessage = function() {
                console.log("测试增量消息更新");
                const messageList = $("#messageList");

                // 确保字幕显示
                if (messageList.hasClass('hide')) {
                    showText();
                }

                // 模拟增量消息
                currentBotMessage = "";
                const fullMessage = "这是一条测试的增量消息，会分段显示。";
                const segments = ["这是一条", "测试的", "增量消息，", "会分段显示。"];

                segments.forEach((segment, index) => {
                    setTimeout(() => {
                        if (currentBotMessage === "") {
                            currentBotMessage = segment;
                            addBotMessage(currentBotMessage);
                        } else {
                            currentBotMessage += segment;
                            updateLastBotMessage(currentBotMessage);
                        }
                    }, index * 500);
                });

                // 重置消息
                setTimeout(() => {
                    currentBotMessage = "";
                }, segments.length * 500 + 1000);
            };
        });

        // 更新状态
        function updateStatus(text) {
            $("#status").text("状态：" + text);
        }

        // 更新通话状态文字
        function updateCallStatus(text) {
            $("#callStatusText").text(text);
        }

        // 显示脉冲波形动画
        function showPulseWave() {
            $("#pulseWaveContainer").show();
        }

        // 隐藏脉冲波形动画
        function hidePulseWave() {
            $("#pulseWaveContainer").hide();
        }





        // 语音活动检测增强
        function enhancedVoiceActivityDetection(level) {
            // 根据音量级别更新UI状态
            if (level > 30) {
                showPulseWave();
                updateCallStatus("检测到语音...");
            } else if (level > 10) {
                updateCallStatus("正在监听...");
            } else {
                hidePulseWave();
                updateCallStatus("等待语音输入...");
            }
        }

        // 转换Float32音频数据为Int16
        function convertFloat32ToInt16(buffer) {
            const l = buffer.length;
            const buf = new Int16Array(l);

            for (let i = 0; i < l; i++) {
                buf[i] = Math.min(1, Math.max(-1, buffer[i])) * 0x7FFF;
            }

            return buf;
        }

        // 播放音频
        function playAudio(audioData) {
            try {
                console.log("接收到音频数据，长度:", audioData.byteLength);

                // 如果PCM播放器不存在，则创建一个
                if (!pcmPlayer) {
                    console.log("创建PCM播放器");
                    pcmPlayer = new CozePCMPlayer({
                        encoding: '16bitInt',
                        channels: 1,
                        sampleRate: 24000,
                        flushingTime: 100
                    });
                }

                // 将音频数据喂给播放器
                pcmPlayer.feed(audioData);
            } catch (error) {
                console.error("播放音频失败:", error);
            }
        }

        // Coze PCM播放器
        class CozePCMPlayer {
            constructor(option) {
                this.init(option);
            }

            init(option) {
                const defaultOption = {
                    encoding: '16bitInt',
                    channels: 1,
                    sampleRate: 24000,
                    flushingTime: 40 // 降低flush间隔，减少播放延迟
                };

                this.option = Object.assign({}, defaultOption, option);
                this.samples = new Float32Array();
                this.interval = setInterval(this.flush.bind(this), this.option.flushingTime);
                this.maxValue = this.getMaxValue();
                this.typedArray = this.getTypedArray();
                this.createContext();
                this.isPlaying = false;
                this.queue = [];
            }

            getMaxValue() {
                const encodings = {
                    '8bitInt': 128,
                    '16bitInt': 32768,
                    '32bitInt': 2147483648,
                    '32bitFloat': 1
                };
                return encodings[this.option.encoding] ? encodings[this.option.encoding] : encodings['16bitInt'];
            }

            getTypedArray() {
                const typedArrays = {
                    '8bitInt': Int8Array,
                    '16bitInt': Int16Array,
                    '32bitInt': Int32Array,
                    '32bitFloat': Float32Array
                };
                return typedArrays[this.option.encoding] ? typedArrays[this.option.encoding] : typedArrays['16bitInt'];
            }

            createContext() {
                // 优化：全局audioContext已存在时复用
                if (audioContext) {
                    this.audioCtx = audioContext;
                } else {
                    this.audioCtx = new (window.AudioContext || window.webkitAudioContext)();
                }
                this.gainNode = this.audioCtx.createGain();
                this.gainNode.gain.value = 1;
                this.gainNode.connect(this.audioCtx.destination);
                this.startTime = this.audioCtx.currentTime;
            }

            feed(data) {
                const tmp = new this.typedArray(data);
                const len = tmp.length;
                const buffer = new Float32Array(len);

                for (let i = 0; i < len; i++) {
                    buffer[i] = tmp[i] / this.maxValue;
                }

                this.samples = this.appendBuffer(this.samples, buffer);
                console.log("音频数据已添加到缓冲区，当前样本数:", this.samples.length);
            }

            appendBuffer(buffer1, buffer2) {
                const tmp = new Float32Array(buffer1.length + buffer2.length);
                tmp.set(buffer1, 0);
                tmp.set(buffer2, buffer1.length);
                return tmp;
            }

            flush() {
                if (!this.samples.length) return;

                // 创建音频缓冲区
                const length = this.samples.length;
                const audioBuffer = this.audioCtx.createBuffer(this.option.channels, length, this.option.sampleRate);
                const audioData = audioBuffer.getChannelData(0);

                for (let i = 0; i < length; i++) {
                    audioData[i] = this.samples[i];
                }

                // 将音频片段添加到队列
                this.queue.push(audioBuffer);
                console.log("音频片段添加到队列，当前队列长度:", this.queue.length);

                // 清空样本缓冲区
                this.samples = new Float32Array();

                // 如果当前没有播放，则开始播放
                if (!this.isPlaying) {
                    this.playNext();
                }
            }

            playNext() {
                if (this.queue.length === 0) {
                    this.isPlaying = false;
                    return;
                }

                // 优化：播放前确保audioContext为running
                if (this.audioCtx.state !== 'running') {
                    this.audioCtx.resume().then(() => {
                        this._playBuffer();
                    });
                } else {
                    this._playBuffer();
                }
            }

            _playBuffer() {
                if (this.queue.length === 0) {
                    this.isPlaying = false;
                    return;
                }
                this.isPlaying = true;
                const audioBuffer = this.queue.shift();
                const bufferSource = this.audioCtx.createBufferSource();
                bufferSource.buffer = audioBuffer;
                bufferSource.connect(this.gainNode);
                bufferSource.onended = () => {
                    console.log("音频片段播放完成，继续播放下一个");
                    this.playNext();
                };
                bufferSource.start();
                console.log("开始播放音频片段，样本数:", audioBuffer.length);
            }

            destroy() {
                if (this.interval) {
                    clearInterval(this.interval);
                }
                this.samples = null;
                this.queue = [];
                this.isPlaying = false;
                // 优化：不再关闭全局audioContext，仅断开gainNode
                if (this.gainNode) {
                    this.gainNode.disconnect();
                }
                // this.audioCtx = null; // 不销毁全局audioContext
            }
        }

        // Base64转ArrayBuffer
        function base64ToArrayBuffer(base64) {
            try {
                const binaryString = window.atob(base64);
                const len = binaryString.length;
                const bytes = new Uint8Array(len);

                for (let i = 0; i < len; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                return bytes.buffer;
            } catch (error) {
                console.error("Base64转换失败:", error);
                return new ArrayBuffer(0);
            }
        }

        // 暂停录音并发送完成信号
        function pauseRecordingAndSendComplete() {
            if (!isRecording || !webSocket || webSocket.readyState !== WebSocket.OPEN) {
                console.warn("无法发送完成信号：当前未录音或WebSocket未连接");
                return;
            }

            console.log("暂停录音并发送完成信号");

            // 重置响应标志
            responseReceived = false;
            console.log("重置完成信号响应标志为false");

            // 清除静音计时器
            if (silenceTimer) {
                clearTimeout(silenceTimer);
                silenceTimer = null;
                console.log("静音计时器已清除");
            }

            // 暂时禁用暂停按钮，防止重复点击
            $("#stopBtn").prop("disabled", true);

            updateStatus("正在处理语音...");

            // 发送音频完成事件
            try {
                const completeMessage = {
                    id: "complete_" + Date.now(),
                    event_type: "input_audio_buffer.complete"
                };

                // 记录详细信息
                console.log(`发送音频完成消息:`, JSON.stringify(completeMessage));
                try {
                    webSocket.send(JSON.stringify(completeMessage));
                    console.log("音频完成消息已成功发送，等待识别结果...");
                } catch (sendError) {
                    console.error("发送完成消息失败:", sendError);
                }

                // 添加诊断日志，记录完成信号发送后的时间戳，用于分析服务器响应时间
                const completeSentTime = Date.now();
                console.log("完成信号发送时间戳:", completeSentTime);

                // 创建一个监听器，检测是否收到完成响应
                const checkResponseInterval = setInterval(() => {
                    const currentTime = Date.now();
                    const elapsedTime = currentTime - completeSentTime;

                    if (responseReceived) {
                        console.log("检测到responseReceived已设置为true，清除间隔检查");
                        clearInterval(checkResponseInterval);
                        return;
                    }

                    if (elapsedTime > 3000) { // 3秒超时
                        console.warn(`已等待${elapsedTime}ms，未收到语音识别完成响应`);
                        clearInterval(checkResponseInterval);
                    } else if (elapsedTime > 1000 && elapsedTime % 1000 < 100) {
                        // 每秒输出一次等待日志
                        console.log(`已等待${Math.floor(elapsedTime / 1000)}秒，等待语音识别完成响应...`);
                    }
                }, 100);

                // 替换原有的超时处理逻辑
                setTimeout(() => {
                    if ($("#status").text().includes("正在处理语音")) {
                        console.log("语音识别响应超时，尝试主动查询识别结果");

                        // 发送一个状态查询请求
                        try {
                            const queryMessage = {
                                id: "query_" + Date.now(),
                                event_type: "transcriptions.get",
                                data: {}
                            };
                            webSocket.send(JSON.stringify(queryMessage));
                            console.log("发送状态查询请求");
                        } catch (queryError) {
                            console.error("发送查询请求失败:", queryError);
                        }

                        // 2秒后如果仍未收到响应，则恢复状态
                        setTimeout(() => {
                            if ($("#status").text().includes("正在处理语音")) {
                                console.log("查询请求也超时，自动恢复状态");
                                updateStatus("录音已就绪，可以继续说话");
                                clearInterval(checkResponseInterval);
                            }
                        }, 2000);
                    }
                }, 5000);
            } catch (error) {
                console.error("发送音频完成消息失败:", error);
                updateStatus("发送完成信号失败: " + error.message);
            }

            // 3秒后重新启用暂停按钮
            setTimeout(() => {
                $("#stopBtn").prop("disabled", false);
                console.log("暂停按钮已重新启用");
            }, 3000);
        }
        function showText() {
            const messageList = $("#messageList");
            const button = $(".call-btn");

            if (messageList.hasClass("hide")) {
                // 显示字幕
                messageList.removeClass("hide").fadeIn(300, function() {
                    // 显示完成后滚动到底部并重新初始化滚动
                    setTimeout(() => {
                        scrollToBottom();
                        initTouchScrolling(); // 重新初始化触摸滚动
                    }, 100);
                });
            } else {
                // 隐藏字幕
                messageList.fadeOut(300, function() {
                    messageList.addClass("hide");
                    button.text("字幕");
                });
            }
        }

        // 页面卸载时销毁audioContext（可选）
        window.addEventListener('beforeunload', function() {
            if (audioContext && typeof audioContext.close === 'function') {
                audioContext.close();
                audioContext = null;
            }
        });
    </script>
</th:block>
</body>
</html>