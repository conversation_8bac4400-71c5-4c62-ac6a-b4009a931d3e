<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            问答详情
        </div>
        <div class="right">
            <a th:href="@{/app/counselingquestion/post}" title="提问">
                <i class="fa fa-edit"></i>
            </a>
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <!-- post -->
            <div class="photoPost mt-1">
                <header>
                    <div class="user">
                        <a href="#">
                            <th:block th:if="${question.isAnonymous eq 1}">
                                <img th:src="@{/static/images/user.png}" alt="avatar" class="avatar">
                                <strong>匿名</strong>
                            </th:block>
                            <th:block th:unless="${question.isAnonymous eq 1}">
                                <img th:if="${question.headPic eq null or question.headPic eq ''}" th:src="@{/static/images/user.png}" class="avatar" />
                                <img th:unless="${question.headPic eq null or question.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${question.headPic}|" class="avatar" />
                                <strong th:text="${question.questionerLoginName}"></strong>
                            </th:block>
                        </a>
                    </div>
                    <div class="right">
                        <div class="lead" th:text="${#dates.format(question.addDate,'MM-dd')}"></div>
                    </div>
                </header>
                <div class="content">
                    <strong th:text="${question.title}"></strong>
                    <div class="text mb-1">
                        <small th:utext="${question.content}"></small>
                    </div>
                    <th:block th:if="${question.img eq null or question.img eq ''}"></th:block>
                    <th:block th:unless="${question.img eq null or question.img eq ''}">
                        <img class="img-fluid" th:src="|@{/static/upload/counseling/thumbnail/small/}${question.img}|" />
                    </th:block>
                </div>
                <th:block th:if="${question.questioner eq user.userId}">
                    <div class="font13 text-right">
                        <a th:href="@{/app/counselingquestion/update(id=${question.id})}" class="text-primary mr-1">编辑</a>
                        <a href="javascript:" class="text-danger" th:onclick="|del(${question.id})|">关闭问题</a>
                    </div>
                </th:block>
            </div>
            <!-- post -->
            <div class="divider mt-1 mb-2"></div>
            <!-- comments -->
            <div class="sectionTitle mb-2">
                <div class="title">
                    <div class="font15 text-dark font-weight-bold"><i class="fa fa-comments-o mr-1"></i>回复 (<small th:text="${question.respondCount}"></small>)</div>
                    <div class="pull-right font-weight-bold"><a href="#respond">我要回答</a></div>
                </div>
            </div>
            <div class="comments">
                <th:block th:if="${not #lists.isEmpty(question.listResponds) and question.listResponds.size() gt 0}">
                    <th:block th:each="respond:${question.listResponds}">
                        <div class="item">
                            <div class="image">
                                <img class="avatar" th:if="${respond.headPic eq null or respond.headPic eq ''}" th:src="@{/static/images/user.png}" />
                                <img class="avatar" th:unless="${respond.headPic eq null or respond.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${respond.headPic}|" />
                            </div>
                            <div class="content">
                                <strong><th:block th:text="${respond.responderLoginName}"></th:block></strong>
                                <div class="text font14 text-justify">
                                    <th:block th:utext="${respond.content}"></th:block>
                                </div>
                                <footer><th:block th:text="${#dates.format(respond.addDate, 'yyyy-MM-dd HH:mm:ss')}"></th:block></footer>
                            </div>
                        </div>
                    </th:block>
                </th:block>
                <th:block th:unless="${not #lists.isEmpty(question.listResponds) and question.listResponds.size() gt 0}">
                    <div class="text-center">
                        <img th:src="@{/static/images/nocomment.png}" width="120" />
                        <h5 class="font-weight-normal font14">暂无回复！</h5>
                    </div>
                </th:block>
            </div>
            <!-- * comments -->
            <div class="divider mt-3 mb-3"></div>
            <!-- form -->
            <form class="mb-2" id="respond">
                <div class="form-group">
                    <textarea class="form-control" rows="4" placeholder="回复内容..." id="content"></textarea>
                </div>
                <button type="button" class="btn btn-primary btn-large btn-block" id="btnPost">
                    发送
                </button>
            </form>
            <!-- * form -->
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script type="text/javascript">
        let questionId = getUrlParam('id');
        $(function () {
            $("#btnPost").click(function () {
                let postContent = $("#content").val();
                if (postContent === "") {
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>说点什么吧！'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 1
                    });
                    return;
                }
                let jsonObj = { "content": postContent, "questionId": questionId };
                $.ajax({
                    type: 'POST',
                    url: '/counselingroom/counselingquestion/add_respond',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnPost").val("保存");
                        $("#btnPost").attr("Disabled", false);
                        if (res.resultCode === 200) {
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                                style: 'background-color:#ffffff; border:none;',
                                btn: '确定',
                                shadeClose: false,
                                yes: function () {
                                    location.reload();
                                }
                            });
                        }
                        else {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                        }
                    }
                });
            });
        });
        function del(id) {
            layer.open({
                content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>确定关闭问题吗？'
                , btn: ['确定', '取消']
                , shadeClose: false
                , yes: function (index) {
                    $.post("/counselingroom/counselingquestion/delete", 'id=' + questionId, function (res) {
                        if (res.resultCode === 200) {
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1"/>' + res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                            location.href = '/app/counselingquestion/index';
                        }
                        else {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                        }
                    }, 'json');
                }
            });
        };
    </script>
</th:block>
</body>
</html>