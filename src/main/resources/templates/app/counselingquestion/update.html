<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            编辑问题
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <form id="frmQuestion">
                <div class="form-group">
                    <label for="counselingType">您想要咨询的问题类型</label>
                    <select class="form-control" name="counselingType" id="counselingType">
                    </select>
                </div>
                <div class="form-group visitor">
                    <label for="title">简单描述您的问题</label>
                    <input id="title" name="title" class="form-control" type="text" placeholder="字数限制50字" maxlength="50" autocomplete="off">
                </div>
                <div class="form-group">
                    <label for="counselingContent">描述你的经历和感受</label>
                    <textarea class="form-control" rows="4" id="counselingContent" name="counselingContent"></textarea>
                </div>
                <div class="form-group form-inline">
                    <div class="mr-1">是否匿名</div>
                    <div>
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="isAnonymous" />
                            <label class="custom-control-label" for="isAnonymous"></label>
                        </div>
                    </div>
                </div>
                <div class="form-group form-inline hide" id="div-img">
                    <img src="/static/images/nopic.png" class="img-fluid" id="img" />
                </div>
                <div class="form-group form-inline">
                    <label>上传图片：</label>
                    <input type="file" name="file" id="txt_file" class="file-loading ml-1" />
                    <input id="hidImg" type="hidden" value="" />
                </div>
                <div>
                    <input type="submit" class="btn btn-primary btn-lg btn-block" value="提交" id="btnSave">
                </div>
            </form>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let questionId = getUrlParam('id');
        let init = function () {
            let oFileInput = new fileInput();
            oFileInput.Init("txt_file");
            $.get("/counselingroom/counselingquestion/get?questionId=" + questionId, "", function (res){
                $("#counselingType").val(res.counselingTypeId).trigger('change');
                $("#title").val(res.title);
                $("#counselingContent").val(res.content);
                if (res.isAnonymous === 1) {
                    $("#isAnonymous").attr("checked", true);
                }
                else {
                    $("#isAnonymous").attr("checked", false);
                }
                if(res.img != undefined && res.img != ''){
                    $("#hidImg").val(res.img);
                    $("#div-img").removeClass('hide').addClass('show');
                    $("#img").attr('src','/static/upload/counseling/thumbnail/small/'+res.img);
                }
            });
        };
        let fileInput = function () {
            let oFile = {};
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=counseling',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-primary", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidImg").val(res.resultMsg);
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1"/>上传成功'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                    else {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                });
            };
            return oFile;
        };
        $(function () {
            initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "","","选择问题类型");
            init();
            $("#frmQuestion").validate({
                rules: {
                    counselingType: { required: true },
                    title: { required: true },
                    counselingContent: { required: true }
                },
                messages: {
                    counselingType: { required: "请选择问题类型" },
                    title: { required: "请完整描述你的问题" },
                    counselingContent: { required: "请填写回复内容" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": questionId,
                        "counselingTypeId": $("#counselingType").val(),
                        "title": $.trim($("#title").val()),
                        "content": $("#counselingContent").val(),
                        "isAnonymous": $("#isAnonymous").prop("checked") ? 1 : 0,
                        "img":$("#hidImg").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counselingquestion/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        location.href = "/app/counselingquestion/detail?id=" + questionId;
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>