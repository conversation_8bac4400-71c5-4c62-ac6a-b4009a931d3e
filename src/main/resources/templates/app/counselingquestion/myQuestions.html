<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的提问
        </div>
        <div class="right">
            <label for="title" class="mb-0 toggleSearchbox">
                <i class="fa fa-search font14"></i>
            </label>
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <div class="searchBox">
        <form>
        <span class="inputIcon" id="btnQuery">
            <i class="fa fa-search font14"></i>
        </span>
            <input type="text" class="form-control" id="title">
            <a href="javascript:" class="toggleSearchbox closeButton">
                <i class="fa fa-times-circle font14"></i>
            </a>
        </form>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <div class="splashBlock hide">
                <div class="mb-3 mt-3">
                    <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="" >
                </div>
                <div class="sectionTitle text-center">
                    <div class="title">
                    </div>
                    <div class="lead">
                        暂无提问记录，现在去提问。<br><a class="btn btn-outline-primary btn-sm mt-1" th:href="@{/app/counselingquestion/post}">立即提问</a>
                    </div>
                </div>
            </div>
            <div class="list hide">
                <div class="mt-2 mb-3">
                    <a th:href="@{/app/counselingquestion/post}"><img th:src="@{/static/images/app_question_banner.png}" alt="image" class="imageBlock img-fluid rounded"></a>
                </div>
                <div class="detail font16">

                </div>
                <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-secondary btn-block" id="loadMore">查看更多</a></div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let currentPage = 0;
        let pageSize = 10;
        let getQueryCondition = function (currentPage) {
            let param = {};
            param.title = $("#title").val();
            param.questioner = '[[${user.userId}]]';
            param.pageSize = pageSize;
            param.pageIndex = currentPage * pageSize;
            return param;
        };
        $(function () {
            init(currentPage);
            $("#btnQuery").click(function () {
                $(".detail").empty();
                init(0);
            });
            $(".load-more").on('click', '#loadMore', function () {
                currentPage = currentPage + 1;
                init(currentPage);
            });
        });
        let init = function (currentPage) {
            let url = '/counselingroom/counselingquestion/list';
            layer.open({type: 2, content: '加载中'});
            $.ajax({
                type: "post",
                url: url,
                dataType: "json",
                data: JSON.stringify(getQueryCondition(currentPage)),
                contentType:'application/json',
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length === 0) {
                        if (currentPage === 0) {
                            $(".list").removeClass('show').addClass('hide');
                            $(".splashBlock").removeClass('hide').addClass('show');
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                    }
                    else {
                        let str = "";
                        for (let i = 0; i < res.data.length; i++) {
                            let isAnonymous = res.data[i].isAnonymous;
                            let headPic;
                            let questioner;
                            if (isAnonymous === 1) {
                                headPic = "/static/images/user.png";
                                questioner = "匿名";
                            }
                            else {
                                headPic = res.data[i].headPic === '' ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res.data[i].headPic;
                                questioner = res.data[i].questionerLoginName;
                            }
                            str += '<div class="photoPost">';
                            str += '<header>';
                            str += '<div class="user"><img src="' + headPic + '" alt="avatar" class="avatar">' + questioner + '</div>';
                            str += '<div class="right"><div class="lead">' + moment(res.data[i].addDate).format('MM-DD') +'</div></div></header>';
                            str += '<div class="content"><strong><a class="text-muted font-weight-bold" href="/app/counselingquestion/detail?id=' + res.data[i].id + '">' + res.data[i].title + '</a></strong>';
                            let content = res.data[i].content.length > 100 ? res.data[i].content.substring(0, 100) + '...</p>' : res.data[i].content;
                            str += '<div class="text"><small>' + content + '</small></div>';
                            str += '</div>';
                            str += '<footer class="row">';
                            str += '<div class="col-8"><button class="btn btn-sm"><i class="fa fa-comment text-primary"></i>' + res.data[i].respondCount+'</button></div>';
                            str += '<div class="col-4 text-right"><a href="/app/counselingquestion/detail?id=' + res.data[i].id + '#respond" class="btn btn-sm mr-2"><i class="fa fa-share text-secondary"></i></a></div>';
                            str += '</footer>';
                            str += '</div>';
                            str += '<div class="divider mt-2 mb-2"></div>';
                        }
                        $(".detail").append(str);
                        $(".list").removeClass('hide').addClass('show');
                        if (res.data.length < pageSize) {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('hide').addClass('show');
                        }
                        $(".splashBlock").removeClass('show').addClass('hide');
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>