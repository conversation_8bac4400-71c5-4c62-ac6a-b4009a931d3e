server:
  port: 8080
  servlet:
    session:
      tracking-modes: cookie
netty:
  port: 9999
  enabled: false
spring:
  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/static/, classpath:/templates/
  servlet:
    multipart:
      max-request-size: 50MB
      max-file-size: 50MB
  jackson:
    default-property-inclusion: NON_NULL
  # thymeleaf 配置
  thymeleaf:
    mode: HTML5                                    # 解析的模板类型
    encoding: UTF-8                              # 模板文件编码
    check-template: true                       # 检查模板是否存在
    enabled: true                                   # 是否启用thymeleaf作为视图解析
    enable-spring-el-compiler: true    # 是否spring el表达式
    cache: false                                      # 是否开启缓存，开发中设置成false，便于更改文件后自动刷新
    servlet:
      content-type: text/html                # 输出类型
    prefix: classpath:/templates/          # 模板文件路径前缀
    suffix: .html                                      # 文件后缀

  # 数据源配置
  datasource:
    name: druidDataSource
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://*************:3307/psycloud_ai?useUnicode=true&allowMultiQueries=true&characterEncoding=utf8&useSSL=false
    username: psycloud
    password: psycloud123123##

  # Redis配置
  redis:
    host: 127.0.0.1
    port: 6379
    password: hj666666
    database: 9
    jedis:
      pool:
        max-idle: 100
        max-active: 100
        min-idle: 8
    timeout: 4000
    isEnabled: true

# mybatis 配置
mybatis:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: cn.psycloud.psyplatform.entity;cn.psycloud.psyplatform.dto
  type-handlers-package: cn.psycloud.psyplatform.handlers
  configuration:
    map-underscore-to-camel-case: true # 驼峰命名规范
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #控制台输出

  logging:
    config: classpath:logback.xml
    level:
      cn.psycloud.psyplatform.dao: error

#上传
file:
  location: E:/环际教育/心理产品/心理软件/心理云服务平台/Java版本/upload/                #文件上传目录
  accessPath: /static/upload/** #对外访问路径

# mybatis 分页插件
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true

# 阿里云短信服务
sms:
  accessKeyId: LTAI4G2hW9aHHK6qwJsw3ijo
  accessKeySecret: ******************************
  product: Dysmsapi
  domain: dysmsapi.aliyuncs.com
  regionIdForPop: cn-hangzhou

# 平台配置
platform:
  scheduleconfig: false # 数据同步接口
  auditlog: false  # 审计日志
  password-forced-expire: # 定期强制修改密码
    enabled: false
    expire-time: 90  # 密码过期时间（单位：天）
  login-attempt: # 登录失败次数限制
    enabled: false
    max-failed-attempts: 5 # 最大登录失败次数
    lock-time-minutes: 1200 # 锁定账户时间（单位：秒）
  always-use-https: false #总是使用https

# 微信公众号授权登录配置
wechat:
  appId: wx5a15331454d3875f
  appSecret: 1c4375c669c1ed100958e0ec6eb3b68f
  redirectUri: https://ai.psy-cloud.cn/app/account/wechat_login

cozeAI:
  api-url: https://api.coze.cn # Coze API基础URL
  public-key: pzoASZ_xbiz8IrwCEBnj8gpOqTd0ej_ZBkH7GVFoJEQ # Coze API公钥
  private-pem-path: keys/coze_jwt_private_key.pem # Coze API私钥文件路径
  report-agent:
    botId: 7515427485924753420 #心理测评报告解读助手智能体
  htp-agent:
    botId: 7515805911257382923 #房树人绘画心理测试助手智能体
  counseling-agent:   #AI心理咨询的智能体所在的空间
    spaceId: 7516336911439577126
  analyze-agent:  # 对话内容分析的智能体
    botId: 7519812175473721398




