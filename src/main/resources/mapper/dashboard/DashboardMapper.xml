<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.dashboard.DashboardDao">
    <select id="get" resultType="cn.psycloud.psyplatform.dto.homePage.DashboardDto">
        select
            totalUserCount,
            counselorCount,
            visitorCount,
            0 as familyMemberCount,
            totalCount,
            unDoCount,
            doneCount,
            abnormalCount
        from (select count(*) as totalUserCount,1 as cum from psycloud_user_info pui where pui.is_valid = 1 and pui.is_checked = 1) t1
                 inner join (
            select count(*) as counselorCount,1 as cum
            from psycloud_user_info pui
                     inner join psycloud_user_role pur on pur.user_id = pui.user_id
                     inner join psycloud_role pr on pr.id = pur.role_id
            where pur.role_id not in (1,3,4) and pui.is_valid = 1 and pui.is_checked = 1 and pr.flag = 'j'
        ) t2 on t2.cum = t1.cum
                 inner join (
            select count(*) as visitorCount,1 as cum
            from psycloud_user_info pui
                     inner join psycloud_user_role pur on pur.user_id = pui.user_id
            where pur.role_id = 3 and pui.is_valid = 1 and pui.is_checked = 1
        ) t3 on t3.cum = t2.cum
                 inner join (
            select count(*) as totalCount,1 as cum from psycloud_test_record ptr where ptr.is_valid = 1
        ) t4 on t4.cum = t3.cum
                 inner join (
            select count(*) as unDoCount,1 as cum from psycloud_test_record ptr where ptr.is_valid = 1 and ptr.state =0
        ) t5 on t5.cum = t4.cum
                 inner join (
            select count(*) as doneCount,1 as cum from psycloud_test_record ptr where ptr.is_valid = 1 and ptr.state in (1,2)
        ) t6 on t6.cum = t5.cum
                 inner join (
            select count(*) as abnormalCount,1 as cum
            from psycloud_test_record ptr
            where ptr.is_valid = 1 and (select count(*) from psycloud_test_score pts where pts.record_id = ptr.id and pts.is_abnormal = 1) > 0
        ) t7 on t7.cum = t6.cum
    </select>
</mapper>