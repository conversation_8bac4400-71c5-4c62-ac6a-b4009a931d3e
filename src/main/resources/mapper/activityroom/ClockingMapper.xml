<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.psycloud.psyplatform.dao.activityroom.ClockingDao">
    <!-- 插入签到签退记录 -->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.activityroom.ClockingEntity">
        insert into psycloud_activity_clocking_record
            (user_id, activity_id, clocking_time, action_type)
        values
            (#{userId,jdbcType=INTEGER}, 
             #{activityId,jdbcType=INTEGER}, 
             #{clockingTime,jdbcType=TIMESTAMP},
             #{actionType,jdbcType=TINYINT})
    </insert>
    
    <!-- 查询签到签退记录是否存在 -->
    <select id="isExists" parameterType="map" resultType="java.lang.Integer">
        select 
            count(1)
        from psycloud_activity_clocking_record
            inner join psycloud_user on psycloud_user.user_id = psycloud_activity_clocking_record.user_id
        where
            psycloud_user.user_id = #{userId,jdbcType=INTEGER}
            and activity_id = #{activityId,jdbcType=INTEGER}
            and action_type = #{actionType,jdbcType=TINYINT}
    </select>

    <!-- 查询签到签退记录列表 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.activityroom.ClockingDto" resultType="cn.psycloud.psyplatform.dto.activityroom.ClockingDto">
        select
            psycloud_activity_clocking_record.id,
            psycloud_activity_clocking_record.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            f_GetStructFullName(psycloud_user_info.struct_id) as structFullName,
            psycloud_activity_clocking_record.activity_id,
            psycloud_activity.activity_name,
            psycloud_activity_clocking_record.clocking_time,
            psycloud_activity_clocking_record.action_type
        from psycloud_activity_clocking_record
            inner join psycloud_user on psycloud_user.user_id = psycloud_activity_clocking_record.user_id
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_activity_clocking_record.user_id
            inner join psycloud_activity on psycloud_activity.id = psycloud_activity_clocking_record.activity_id
        where
            psycloud_activity_clocking_record.activity_id = #{activityId,jdbcType=INTEGER}
            <if test="actionType != null">
                and psycloud_activity_clocking_record.action_type = #{actionType,jdbcType=TINYINT}
            </if>
    </select>

    <!-- 获取用户活动状态 -->
    <select id="getUserActivityStatus" parameterType="map" resultType="java.lang.Integer">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0  <!-- 未签到 -->
                WHEN SUM(CASE WHEN action_type = 2 THEN 1 ELSE 0 END) > 0 THEN 3  <!-- 已签退 -->
                WHEN SUM(CASE WHEN action_type = 1 THEN 1 ELSE 0 END) > 0 THEN 2  <!-- 已签到未签退 -->
                ELSE 1  <!-- 已签到 -->
            END as status
        FROM psycloud_activity_clocking_record
        WHERE user_id = #{userId}
        AND activity_id = #{activityId}
    </select>
</mapper>