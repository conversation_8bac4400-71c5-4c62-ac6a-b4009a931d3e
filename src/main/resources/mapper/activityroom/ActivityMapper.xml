<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.activityroom.ActivityDao">
    <!-- 添加心理活动 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_activity (
            activity_name, activity_type, activity_intro, activity_cover, start_time, end_time, counselor, add_date, is_valid, survey_id, operator
        ) values (
            #{activityName,jdbcType=VARCHAR}, 
            #{activityType,jdbcType=INTEGER}, 
            #{activityIntro,jdbcType=LONGVARCHAR},
            #{activityCover,jdbcType=VARCHAR}, 
            #{startTime,jdbcType=TIMESTAMP}, 
            #{endTime,jdbcType=TIMESTAMP}, 
            #{counselor,jdbcType=INTEGER}, 
            #{addDate,jdbcType=TIMESTAMP}, 
            1,
            #{surveyId,jdbcType=INTEGER},
            #{operator,jdbcType=INTEGER}
        )
    </insert>
    
    <!-- 修改心理活动 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivityEntity">
        update psycloud_activity
        set
            activity_name = #{activityName,jdbcType=VARCHAR},
            activity_type = #{activityType,jdbcType=INTEGER},
            activity_intro = #{activityIntro,jdbcType=VARCHAR},
            activity_cover = #{activityCover,jdbcType=VARCHAR},
            start_time = #{startTime,jdbcType=TIMESTAMP},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            counselor = #{counselor,jdbcType=INTEGER},
            survey_id = #{surveyId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <!-- 删除心理活动 -->
    <update id="delete" parameterType="java.lang.Integer">
        update psycloud_activity
        set is_valid = 0
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <!-- 根据条件查询心理活动列表 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto">
        select 
            a.id,
            a.activity_name,
            a.activity_type,
            a.activity_intro,
            a.activity_cover,
            a.start_time,
            a.end_time,
            u.real_name as counselorName,
            a.operator,
            u2.login_name as operatorName,
            a.add_date,
            a.is_valid,
            s.id as surveyId,
            s.survey_name,
            (select count(*) from psycloud_activity_clocking_record pacr where pacr.activity_id = a.id and pacr.action_type = 1) as clockingInNum
        from psycloud_activity a
            left join psycloud_user_info u on a.counselor = u.user_id
            left join psycloud_survey s on a.survey_id = s.id
            left join psycloud_user u2 on a.operator = u2.user_id
        where a.is_valid = 1
         and u.is_valid = 1
            <if test="activityName != null and activityName != ''">
                and a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="activityType != null and activityType != 0">
                and a.activity_type = #{activityType}
            </if>
        order by a.start_time desc
    </select>
    
    <!-- 根据id查询心理活动 -->
    <select id="getById" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto">
        select 
            a.id,
            a.activity_name,
            a.activity_type,
            a.activity_intro,
            a.activity_cover,
            a.start_time,
            a.end_time,
            u.user_id as counselorId,
            u.real_name as counselorName,
            a.add_date,
            a.is_valid,
            s.id as survey_id,
            s.survey_name
        from psycloud_activity a
            left join psycloud_user_info u on a.counselor = u.user_id
            left join psycloud_survey s on a.survey_id = s.id
        where a.is_valid = 1
          and u.is_valid = 1
          and a.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 查询用户调查问卷是否已做 -->
    <select id="isSurveyDone" parameterType="map" resultType="Integer">
        select count(*)
        from psycloud_survey_record psr
            inner join psycloud_activity_survey_record pasr on psr.id = pasr.survey_record_id
        where psr.is_done = 1
          and psr.survey_id = #{surveyId,jdbcType=INTEGER}
          and psr.user_id = #{userId,jdbcType=INTEGER}
          and pasr.activity_id = #{activityId,jdbcType=INTEGER}
    </select>

    <!-- 导出调查问卷结果-->
    <select id="getExportSurveyTestResult" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.activityroom.ExportActivitySurveyRecordDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            pui.user_id as 用户id,
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            ${sql}
        from psycloud_survey_question psq
            left join psycloud_survey_result psr on psr.q_id = psq.id
            inner join psycloud_survey_record psr2 on psr2.id = psr.record_id and psq.survey_id = psr2.survey_id
            inner join psycloud_user_info pui on pui.user_id = psr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr2.is_valid = 1
            and psr2.is_done = 1
            and pui.is_valid = 1
            and psr2.survey_id = #{surveyId}
            <if test="activityId !=null and activityId !=0">
                and psr2.id in (
                select ptsr3.survey_record_id
                from psycloud_activity_survey_record ptsr3
                    inner join psycloud_activity pt on pt.id = ptsr3.activity_id
                where  pt.id = #{activityId}
                )
            </if>
        group by pui.user_id,pu.login_name,pui.real_name
        order by pui.user_id,psq.q_number
    </select>

    <!-- 获取我的活动 -->
    <select id="getMyActivities" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto">
        (select
            pa.id,
            pa.activity_name,
            pa.activity_type,
            pa.start_time,
            pa.end_time,
            pa.activity_cover
        from psycloud_activity pa
            inner join psycloud_activity_clocking_record pacr on pacr.activity_id = pa.id
        where pacr.action_type=1 and pacr.user_id = #{userId})
        union
        (select
            pa.id,
            pa.activity_name,
            pa.activity_type,
            pa.start_time,
            pa.end_time,
            pa.activity_cover
        from psycloud_activity pa
        where pa.counselor = #{userId})
        order by start_time desc
    </select>

    <!-- 获取我的活动集合：select -->
    <select id="getCounselorActivitiesForSelect" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityForSelectDto">
        select
            pa.id,
            pa.activity_name
        from psycloud_activity pa
        where pa.is_valid = 1 and pa.counselor = #{userId}
        order by start_time desc
    </select>

    <!-- 更新活动总评分 -->
    <update id="updateOverallEvaluation" parameterType="cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity">
            update psycloud_activity
            set overall_evaluation = #{overallEvaluation,jdbcType=VARCHAR}
            where id = #{activityId,jdbcType=INTEGER}
    </update>

    <!-- 获取活动总评分 -->
    <select id="getOverallEvaluation" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.OverallEvaluationDto">
        select activity_name, overall_evaluation
        from psycloud_activity
        where id = #{activityId,jdbcType=INTEGER}
    </select>

    <!-- 获取活动报告 -->
    <select id="getActivityReport" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityReportDto">
        select
            a.id as activityId,
            a.activity_name,
            a.start_time,
            a.end_time,
            u.real_name as counselor,
            case a.activity_type
                when 1 then '驻场咨询（轻咨询）'
                when 2 then '驻场咨询（50分钟以上）'
                when 3 then '团体辅导'
                when 4 then '心理关爱活动'
                else ''
                end as activityType,
            (select COUNT(*) from psycloud_activity_clocking_record pacr
             where pacr.activity_id = a.id and pacr.action_type = 1) as clockingInCount,
            (select COUNT(*) from psycloud_activity_clocking_record pacr
             where pacr.activity_id = a.id and pacr.action_type = 2) as clockingOutCount,
            a.overall_evaluation,
            s.survey_name,
            (select COUNT(*)
             from psycloud_survey_record psr
                inner join psycloud_activity_survey_record pasr on psr.id = pasr.survey_record_id
             where pasr.activity_id = a.id and psr.is_done = 1 and psr.is_valid = 1) as surveySubmitCount
        from
            psycloud_activity a
                left join psycloud_user_info u on a.counselor = u.user_id
                left join psycloud_survey s on a.survey_id = s.id
        WHERE
            a.is_valid = 1
          and u.is_valid = 1
          and a.id = #{activityId}
    </select>
</mapper>