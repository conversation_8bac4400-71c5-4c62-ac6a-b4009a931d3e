<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.activityroom.SelfEvaluationDao">
    <!-- 插入个人点评内容 -->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.activityroom.SelfEvaluationEntity">
        insert into psycloud_activity_evaluation (
            activity_id,
            operator,
            user_id,
            evaluation_content,
            tags,
            operate_date,
            is_valid
        )
        VALUES (
            #{activityId, jdbcType=INTEGER},
            #{operator, jdbcType=INTEGER},
            #{userId, jdbcType=INTEGER},
            #{evaluationContent, jdbcType=VARCHAR},
            #{tags, jdbcType=VARCHAR},
            #{operateDate, jdbcType=TIMESTAMP},
            1
        )
    </insert>

    <!-- 删除个人点评内容 -->
    <delete id="delete" parameterType="map">
        delete from psycloud_activity_evaluation
        where activity_id = #{activityId}
        and user_id = #{userId}
    </delete>

    <!-- 根据活动id获取参与者的点评集合 -->
    <select id="getSelfEvaluationList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.SelfEvaluationDto">
        select
            pa.activity_name,
            pacr.user_id,
            pui.real_name,
            pae.evaluation_content,
            pae.tags
        from psycloud_activity_clocking_record pacr
            inner join psycloud_activity pa on pa.id = pacr.activity_id
            inner join psycloud_user_info pui on pui.user_id = pacr.user_id
            left join psycloud_activity_evaluation pae on pae.user_id = pacr.user_id
        where pacr.action_type = 1
        and pa.is_valid = 1
        and pa.id = #{activityId}
    </select>

    <!-- 根据活动id和用户id获取自己的个人评价 -->
    <select id="getMySelfEvaluationList" parameterType="map" resultType="String">
        select
            pae.tags
        from psycloud_activity_evaluation pae
        where pae.user_id = #{userId}
            and pae.activity_id = #{activityId}
    </select>

    <!-- 根据用户id获取来访者的个人活动评价集合 -->
    <select id="getMySelfEvaluationListByUerId" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.SelfEvaluationDto">
        select
            pa.activity_name,
            pa.start_time,
            pa.end_time,
            pui2.real_name as counselorName,
            pui.real_name,
            pae.tags,
            pae.evaluation_content
        from psycloud_activity_evaluation pae
            inner join psycloud_activity pa on pa.id = pae.activity_id
            inner join psycloud_user_info pui on pui.user_id = pae.user_id
            left join psycloud_user_info pui2 on pui2.user_id = pa.counselor
        where pae.user_id = #{userId}
    </select>
</mapper>