<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.homePage.StatOfHomePageDao">
    <select id="getVisitorCount" resultType="Integer">
        select COUNT(*)
        from psycloud_user_info pui
                 inner join psycloud_user_role pur on pur.user_id = pui.user_id
        where pur.role_id = 3
          and pui.is_valid = 1
          and pui.is_checked = 1
    </select>

    <select id="getCounselorCount" resultType="Integer">
        select COUNT(*)
        from psycloud_user_info pui
                 inner join psycloud_user_role pur on pur.user_id = pui.user_id
                 inner join psycloud_role pr on pr.id = pur.role_id
        where pur.role_id not in (1,3,4)
          and pui.is_valid = 1
          and pui.is_checked = 1
          and pr.flag='j'
    </select>

    <select id="getMeasuringRecordCount" resultType="Integer">
        select COUNT(*)
        from psycloud_test_record ptr
        where ptr.state = 1
            and ptr.is_valid = 1
    </select>

    <select id="getCounselingCount" resultType="Integer">
        select COUNT(*)
        from psycloud_counseling_order pco
        where pco.state in (1,3)
          and pco.is_valid = 1
    </select>

    <!--查询最近七天的测评量-->
    <select id="getTestRecordCapacitys" resultType="cn.psycloud.psyplatform.dto.measuringroom.TestRecordCapacityDto" databaseId="mysql">
        select a.dt,SUM(IFNULL(b.capacity,0)) as capacity
        from(
                select date_sub(curdate(),interval 0 day) as dt
                union
                select date_sub(curdate(),interval 1 day)
                union
                select date_sub(curdate(),interval 2 day)
                union
                select date_sub(curdate(),interval 3 day)
                union
                select date_sub(curdate(),interval 4 day)
                union
                select date_sub(curdate(),interval 5 day)
                union
                select date_sub(curdate(),interval 6 day)
            ) a
                left join (
                    select
                        CONVERT(ptr.start_time, date) as datetemp,
                        COUNT(ptr.id) as capacity
                    from psycloud_test_record ptr
                    where ptr.is_valid = 1
                      and ptr.state not in (0,2)
                      and DATEDIFF(NOW(), ptr.start_time) &gt;= 0
                      and DATEDIFF(NOW(), ptr.start_time) &lt; 7
                    group by ptr.start_time
        ) b on a.dt = b.datetemp
        group by a.dt
    </select>
    <!--查询最近七天的咨询量-->
    <select id="getCounselingCapacity" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingCapacityDto" databaseId="mysql">
        select
            a.dt,
            ifnull(b.count,0) as capacity
        from(
                select date_sub(curdate(),interval 0 day) as dt
                union
                select date_sub(curdate(),interval 1 day)
                union
                select date_sub(curdate(),interval 2 day)
                union
                select date_sub(curdate(),interval 3 day)
                union
                select date_sub(curdate(),interval 4 day)
                union
                select date_sub(curdate(),interval 5 day)
                union
                select date_sub(curdate(),interval 6 day)
            ) as a
                left join (
            select convert(pco.start_time,date) as datetemp,count(pco.id) as count
            from psycloud_counseling_order pco
            where pco.state in (1,3)
              and datediff(pco.start_time, now()) &gt;= 0
              and datediff(pco.start_time, now()) &lt; 7
            group by pco.start_time
        ) as b on a.dt = b.datetemp
        group by a.dt
    </select>

</mapper>