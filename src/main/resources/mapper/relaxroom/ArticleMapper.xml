<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.relaxroom.ArticleDao">
    <!--根据条件查询文章列表-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.relaxroom.ArticleDto" resultType="cn.psycloud.psyplatform.dto.relaxroom.ArticleDto">
        select
            pa.id,
            pa.title,
            pa.content,
            pa.cover_img,
            pa.add_date,
            pa.is_valid,
            pac.category_name,
            pui.real_name as operator_name,
            pa.category_id,
            pui.head_img,
            pa.hit_count,
            pa.is_banner,
            pa.banner
        from psycloud_article pa
            inner join psycloud_article_category pac on pac.id = pa.category_id
            inner join psycloud_user_info pui on pui.user_id = pa.operator
        where pa.is_valid=1
            and pac.is_valid = 1
        <if test="categoryId != null and categoryId!=0">and pa.category_id = #{categoryId}</if>
        <if test="id!=null and id!=0">and pa.id = #{id}</if>
        <if test="title != null and title != ''">and pa.title like CONCAT('%',#{title},'%')</if>
        <if test="isBanner != null">and pa.is_banner = #{isBanner}</if>
        order by pa.add_date desc
    </select>

    <!--根据栏目id查询文章集合-->
    <select id="getListByCategoryId" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.relaxroom.ArticleDto">
        select
            pa.id,
            pa.title,
            pa.cover_img,
            pa.add_date,
            pac.category_name,
            pa.category_id,
            pa.hit_count
        from psycloud_article pa
            inner join psycloud_article_category pac on pac.id = pa.category_id
            inner join psycloud_user_info pui on pui.user_id = pa.operator
        where pa.is_valid=1
            and pac.is_valid = 1
            and pa.category_id = #{categoryId}
        order by pa.add_date desc
    </select>

    <!--根据id查询文章信息-->
    <select id="getById" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.relaxroom.ArticleDto">
        select
            pa.id,
            pa.title,
            pa.content,
            pa.cover_img,
            pa.add_date,
            pa.is_valid,
            pac.category_name,
            pui.real_name as operatorName,
            pa.category_id,
            pui.head_img as headPic,
            pa.hit_count,
            pa.is_banner,
            pa.banner
        from psycloud_article pa
            inner join psycloud_article_category pac on pac.id = pa.category_id
            inner join psycloud_user_info pui on pui.user_id = pa.operator
        where pa.id = #{id}
    </select>
    
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_article set is_valid = 0 where id = #{id}
    </update>

    <!--添加-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.relaxroom.ArticleEntity">
        insert into psycloud_article(
            title,
            content,
            category_id,
            cover_img,
            hit_count,
            is_banner,
            banner,
            add_date,
            operator,
            is_valid
        )
        values(
            #{title,jdbcType=VARCHAR},
            #{content,jdbcType=LONGVARCHAR},
            #{categoryId,jdbcType=INTEGER},
            #{coverImg,jdbcType=VARCHAR},
             0,
            #{isBanner,jdbcType=TINYINT},
            #{banner,jdbcType=VARCHAR},
            #{addDate,jdbcType=TIMESTAMP},
            #{operator,jdbcType=INTEGER},
            1
        )
    </insert>

    <!--修改-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.relaxroom.ArticleEntity">
        update psycloud_article
        set
            title = #{title,jdbcType=VARCHAR},
            content = #{content,jdbcType=LONGVARCHAR},
            category_id = #{categoryId,jdbcType=INTEGER},
            cover_img = #{coverImg,jdbcType=VARCHAR},
            is_banner = #{isBanner,jdbcType=TINYINT},
            banner =#{banner,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--更新点击量-->
    <update id="addHitCount" parameterType="Integer">
        update psycloud_article set hit_count = hit_count + 1 where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>