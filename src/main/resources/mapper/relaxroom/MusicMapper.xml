<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.relaxroom.MusicDao">
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_album_music set is_valid = 0 where id = #{id,jdbcType=INTEGER}
    </update>
    <!--添加-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.relaxroom.MusicEntity">
        insert into psycloud_album_music(
            album_id,
            music_name,
            file_name,
            upload_date,
            operator,
            is_valid
        )
        values(
            #{albumId,jdbcType=INTEGER},
            #{musicName,jdbcType=VARCHAR},
            #{fileName,jdbcType=VARCHAR},
            #{uploadDate,jdbcType=VARCHAR},
            #{operator,jdbcType=INTEGER},
            1
        )
    </insert>

    <!--根据专辑id获取音乐集合-->
    <select id="getListByAlbumId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.relaxroom.MusicEntity">
        select
            id,
            album_id,
            music_name,
            file_name,
            upload_date,
            operator,
            is_valid
        from psycloud_album_music
        where is_valid = 1 and album_id = #{albumId}
    </select>
</mapper>