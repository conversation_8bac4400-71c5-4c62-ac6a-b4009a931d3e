<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.relaxroom.AlbumDao">
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_album set is_valid = 0 where id = #{id}
    </update>

    <!--添加-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.relaxroom.AlbumEntity">
        insert into psycloud_album(
            album_name,
            cover_img,
            album_type,
            is_valid
        )
        values(
            #{albumName,jdbcType=VARCHAR},
            #{coverImg,jdbcType=VARCHAR},
            #{albumType,jdbcType=INTEGER},
            1
        )
    </insert>

    <!--修改-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.relaxroom.AlbumEntity">
        update
            psycloud_album
        set
            album_name = #{albumName,jdbcType=VARCHAR},
            album_type= #{albumType,jdbcType=INTEGER},
            cover_img = #{coverImg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--根据id查询专辑信息-->
    <resultMap id="albumResultMap" type="cn.psycloud.psyplatform.dto.relaxroom.AlbumDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="album_name" property="albumName" jdbcType="VARCHAR" />
        <result column="cover_img" property="coverImg" jdbcType="VARCHAR" />
        <result column="album_type" property="albumType" jdbcType="INTEGER"/>
        <result column="album_type_name" property="albumTypeName" jdbcType="VARCHAR"/>
        <collection property="listMusic" column="id" select="cn.psycloud.psyplatform.dao.relaxroom.MusicDao.getListByAlbumId" />
    </resultMap>
    <select id="getById" parameterType="Integer" resultMap="albumResultMap">
        select
            pa.id,
            pa.album_name,
            pa.album_type,
            pat.album_type_name,
            pa.cover_img
        from psycloud_album pa
            inner join psycloud_album_type pat on pat.id = pa.album_type
        where pa.is_valid=1 and pa.id=#{id}
    </select>

    <!--查询专辑集合-->
    <select id="getList" resultType="cn.psycloud.psyplatform.dto.relaxroom.AlbumDto" parameterType="cn.psycloud.psyplatform.dto.relaxroom.AlbumDto">
        select
            pa.id,
            pa.album_name,
            pa.cover_img,
            pat.album_type_name
        from psycloud_album pa
            left join psycloud_album_type pat on pat.id = pa.album_type
        where pa.is_valid=1
        <if test="albumType != null and albumType!=0">and pat.id = #{albumType}</if>
        order by pa.id desc
    </select>
</mapper>