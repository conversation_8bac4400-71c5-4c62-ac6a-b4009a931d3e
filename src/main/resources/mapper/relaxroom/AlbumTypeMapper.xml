<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.relaxroom.AlbumTypeDao">
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.relaxroom.AlbumTypeEntity">
        insert into psycloud_album_type(
            album_type_name,
            sort,
            is_valid
        )
        values (
            #{albumTypeName,jdbcType=VARCHAR},
            0,
            1
        )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.relaxroom.AlbumTypeEntity">
        update psycloud_album_type set album_type_name = #{albumTypeName,jdbcType=VARCHAR} where id=#{id,jdbcType=INTEGER}
    </update>

    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_album_type set is_valid = 0 where id = #{id}
    </update>

    <resultMap id="albumTypeResultMap" type="cn.psycloud.psyplatform.dto.relaxroom.AlbumTypeDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="album_type_name" property="albumTypeName" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT"/>
    </resultMap>

    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.relaxroom.AlbumTypeDto" resultMap="albumTypeResultMap">
        select
            id,
            album_type_name,
            sort,
            is_valid
        from psycloud_album_type
        where is_valid=1
        order by id desc
    </select>
</mapper>