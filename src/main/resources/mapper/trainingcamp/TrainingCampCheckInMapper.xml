<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampCheckInDao">
    <!-- 添加训练营打卡记录-->
    <insert id="addCheckInRecord" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampCheckInEntity">
        insert into psycloud_trainingcamp_checkin(
            camp_id,
            task_id,
            course_id,
            user_id,
            checkin_date
        )
        values (
            #{campId,jdbcType=INTEGER},
            #{taskId,jdbcType=INTEGER},
            #{courseId,jdbcType=INTEGER},
            #{userId,jdbcType=INTEGER},
            #{checkInDate,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 验证用户是否打卡课程 -->
    <select id="isCheckIn" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampCheckInEntity">
        select count(*)
        from psycloud_trainingcamp_checkin
        where camp_id = #{campId}
            and task_id = #{taskId}
            and course_id = #{courseId}
            and user_id = #{userId}
    </select>

    <!-- 获取任务下所有打卡记录 -->
    <select id="getAllCheckInCourses" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampCheckInEntity">
        select
            id,
            course_id
        from psycloud_trainingcamp_checkin
        where camp_id = #{campId}
          and task_id = #{taskId}
          and user_id = #{userId}
    </select>
</mapper>