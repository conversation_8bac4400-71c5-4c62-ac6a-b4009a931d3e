<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampUserDao">
    <!-- 保存训练营学员 -->
    <insert id="addCampUser" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampUserEntity">
        insert into psycloud_trainingcamp_user(
            camp_id,
            user_id
        )
        values (
            #{campId,jdbcType=INTEGER},
            #{userId,jdbcType=INTEGER}
        )
    </insert>

    <!-- 训练营学员集合 -->
    <select id="getCampUserList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.trainingcamp.TrainingCampUserDto">
        select
            ptu.camp_id,
            pu.login_name,
            pui.real_name,
            pui.mobile,
            f_getstructfullname(pui.struct_id) as structFullName
        from psycloud_trainingcamp_user ptu
            inner join psycloud_user pu on pu.user_id = ptu.user_id
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where pui.is_valid=1 and ptu.camp_id = #{campId}
    </select>

    <!-- 验证用户是否报名训练营 -->
    <select id="isUserExists" parameterType="map">
        select count(*) from psycloud_trainingcamp_user where camp_id = #{campId} and user_id = #{userId}
    </select>

    <!-- 查询已报名人数 -->
    <select id="getRegNum" parameterType="Integer">
        select count(*) from psycloud_trainingcamp_user where camp_id = #{campId}
    </select>
</mapper>