<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.trainingcamp.TrainingCampTaskDao">
    <!-- 添加训练营任务 -->
    <insert id="addCampTask" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampTaskEntity">
        insert into psycloud_trainingcamp_task(
            camp_id,
            task_date,
            task_title,
            clocking_deadline,
            course_ids,
            is_valid
        )
        values (
            #{campId,jdbcType=INTEGER},
            #{taskDate,jdbcType=TIMESTAMP},
            #{taskTitle,jdbcType=VARCHAR},
            #{clockingDeadline,jdbcType=INTEGER},
            #{courseIds,jdbcType=VARCHAR},
            1
        )
    </insert>
    <!-- 修改训练营任务 -->
    <update id="updateCampTask" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampTaskEntity">
        update
            psycloud_trainingcamp_task
        set
            task_date = #{taskDate,jdbcType=TIMESTAMP},
            task_title = #{taskTitle,jdbcType=VARCHAR},
            clocking_deadline = #{clockingDeadline,jdbcType=INTEGER},
            course_ids = #{courseIds,jdbcType=VARCHAR}
        where id = #{id}
    </update>

    <!-- 删除训练营任务 -->
    <update id="delCampTask" parameterType="Integer">
        update psycloud_trainingcamp_task set is_valid=0 where id = #{id}
    </update>

    <resultMap id="campTaskResultMap" type="cn.psycloud.psyplatform.dto.trainingcamp.TrainingCampTaskDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="camp_id" property="campId" jdbcType="INTEGER" />
        <result column="task_date" property="taskDate" jdbcType="TIMESTAMP" />
        <result column="task_title" property="taskTitle" jdbcType="VARCHAR"/>
        <result column="clocking_deadline" property="clockingDeadline" jdbcType="TINYINT"/>
        <result column="course_ids" property="courseIds" jdbcType="VARCHAR" />
        <collection property="courses" select="getCourses" column="course_ids" />
    </resultMap>
    <!-- 查询训练营任务集合 -->
    <select id="getList" parameterType="Integer" resultMap="campTaskResultMap">
        select
            ptt.id,
            ptt.camp_id,
            ptt.task_date,
            ptt.task_title,
            ptt.clocking_deadline,
            ptt.course_ids,
            pt.start_date,
            pt.end_date
        from psycloud_trainingcamp_task ptt
            inner join psycloud_trainingcamp pt on pt.id = ptt.camp_id
        where ptt.is_valid = 1
        <if test="campId != null and campId != 0">and ptt.camp_id = #{campId}</if>
        order by ptt.task_date
    </select>

    <resultMap id="courseResultMap" type="cn.psycloud.psyplatform.dto.trainingcamp.TrainingCourseDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="product_id" property="productId" jdbcType="INTEGER" />
        <result column="course_name" property="courseName" jdbcType="VARCHAR" />
        <result column="course_type" property="courseType" jdbcType="TINYINT" />
        <result column="delivery_platform" property="deliveryPlatform" jdbcType="TINYINT" />
        <result column="comment_enabled" property="commentEnabled" jdbcType="TINYINT" />
        <result column="attachment_type" property="attachmentType" jdbcType="TINYINT" />
        <result column="attachment_file_name" property="attachmentFileName" jdbcType="VARCHAR" />
        <result column="attachment_url" property="attachmentUrl" jdbcType="VARCHAR" />
        <result column="article_content" property="articleContent" jdbcType="LONGVARCHAR" />
        <result column="add_date" property="addDate" jdbcType="TIMESTAMP" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
        <collection property="comments" select="cn.psycloud.psyplatform.dao.comment.CommentDao.getCommentsForTrainingCampCourse" column="product_id"/>
    </resultMap>
    <select id="getCourses" resultMap="courseResultMap">
        select
            ptc.id,
            ptc.id as product_id,
            ptc.course_name,
            ptc.course_type,
            ptc.delivery_platform,
            ptc.comment_enabled,
            ptc.attachment_type,
            ptc.attachment_file_name,
            ptc.attachment_url,
            ptc.article_content,
            ptc.add_date,
            ptc.is_valid
        from psycloud_trainingcamp_course ptc
        where ptc.is_valid=1
        <if test="courseIds!=null and courseIds != ''">
            and ptc.id in
            <foreach collection="courseIds.split(',')" index="index" item="courseId" open="(" separator="," close=")">
                #{courseId}
            </foreach>
        </if>
    </select>

    <!-- 验证当天是否存在任务 -->
    <select id="isTaskDateExists" parameterType="map">
        select count(*) from psycloud_trainingcamp_task where task_date = #{taskDate} and is_valid =1 and camp_id = #{campId}
    </select>

</mapper>