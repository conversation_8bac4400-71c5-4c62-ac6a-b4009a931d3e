<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.PointsConfigDao">
    <resultMap id="pointsConfigResultMap" type="cn.psycloud.psyplatform.entity.platform.PointsConfigEntity">
        <result column="is_counseling_enabled" property="isCounselingEnabled" jdbcType="TINYINT" />
        <result column="is_measuring_enabled" property="isMeasuringEnabled" jdbcType="TINYINT" />
    </resultMap>
    <sql id="tb">psycloud_points_config</sql>
    <!--查询积分配置-->
    <select id="get" resultMap="pointsConfigResultMap">
        select
            is_counseling_enabled,
            is_measuring_enabled
        from
        <include refid="tb" />
    </select>

    <!--保存积分配置-->
    <update id="save" parameterType="cn.psycloud.psyplatform.entity.platform.PointsConfigEntity">
        update <include refid="tb" />
        set
            is_counseling_enabled = #{isCounselingEnabled,jdbcType=TINYINT},
            is_measuring_enabled = #{isMeasuringEnabled,jdbcType=TINYINT}
    </update>
</mapper>