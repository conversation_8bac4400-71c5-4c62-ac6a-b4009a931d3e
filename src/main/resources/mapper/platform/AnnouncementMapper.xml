<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.AnnouncementDao">
    <resultMap id="announcementResultMap" type="cn.psycloud.psyplatform.dto.platform.AnnouncementDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="add_date" property="addDate" jdbcType="TIMESTAMP"/>
        <result column="operator" property="operator" jdbcType="INTEGER"/>
        <result column="real_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="is_valid" property="isValid" jdbcType="TINYINT"/>
        <result column="head_img" property="headPic" jdbcType="VARCHAR"/>
        <result column="is_banner" property="isBanner" jdbcType="TINYINT"/>
        <result column="banner" property="banner" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="announcement_field_list">
        pa.id,
        pa.title,
        pa.content,
        pa.add_date,
        pa.operator,
        pui.real_name,
        pa.is_valid,
        pui.head_img,
        pa.is_banner,
        pa.banner
    </sql>
    <select id="getList" resultMap="announcementResultMap" parameterType="cn.psycloud.psyplatform.dto.platform.AnnouncementDto">
        select
        <include refid="announcement_field_list"/>
        from psycloud_announcement pa
        inner join psycloud_user_info pui on pui.user_id = pa.operator
        where pa.is_valid = 1
        <if test="id != null and id != 0">and pa.id = #{id}</if>
        <if test="isBanner != null and isBanner !=''">and pa.is_banner = #{isBanner}</if>
        order by pa.add_date desc
    </select>

    <select id="get" resultMap="announcementResultMap" parameterType="cn.psycloud.psyplatform.dto.platform.AnnouncementDto">
        select
        <include refid="announcement_field_list"/>
        from psycloud_announcement pa
        inner join psycloud_user_info pui on pui.user_id = pa.operator
        where pa.is_valid = 1
        <if test="id != null and id != 0">and pa.id = #{id}</if>
        <if test="isBanner != null and isBanner !=''">and pa.is_banner = #{isBanner}</if>
    </select>

    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.platform.AnnouncementEntity">
        insert into psycloud_announcement(
            title,
            content,
            is_banner,
            banner,
            add_date,
            operator,
            is_valid
        )
        values (
           #{title,jdbcType=VARCHAR},
           #{content,jdbcType=LONGVARCHAR},
           #{isBanner,jdbcType=TINYINT},
           #{banner,jdbcType=VARCHAR},
           #{addDate,jdbcType=TIMESTAMP},
           #{operator,jdbcType=INTEGER},
           1
        )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.platform.AnnouncementEntity">
        update
            psycloud_announcement
        set
            title = #{title,jdbcType=VARCHAR},
            content = #{content,jdbcType=LONGVARCHAR},
            is_banner = #{isBanner,jdbcType=TINYINT},
            banner= #{banner,jdbcType=VARCHAR}
        where id = #{id}
    </update>

    <update id="delete" parameterType="Integer">
        update
            psycloud_announcement
        set
            is_valid = 0
        where id=#{id,jdbcType=INTEGER}
    </update>
</mapper>