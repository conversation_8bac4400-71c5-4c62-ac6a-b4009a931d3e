<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.course.CourseCategoryDao">
    <resultMap id="courseCategoryResultMap" type="cn.psycloud.psyplatform.entity.course.CourseCategoryEntity">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="course_id" property="courseId" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="attachment" property="attachment" jdbcType="VARCHAR"/>
        <result column="attachment_file_type" property="attachmentFileType" jdbcType="VARCHAR"/>
        <result column="article_content" property="articleContent" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
    </resultMap>

    <update id="delete" parameterType="Integer">
        update psycloud_course_category set is_valid = 0 where id = #{id}
    </update>

    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.course.CourseCategoryEntity">
        insert into psycloud_course_category(
            course_id,
            title,
            attachment,
            attachment_file_type,
            article_content,
            operator,
            add_date,
            sort,
            is_valid
        )
        values (
            #{courseId,jdbcType=INTEGER},
            #{title,jdbcType=VARCHAR},
            #{attachment,jdbcType=VARCHAR},
            #{attachmentFileType,jdbcType=VARCHAR},
            #{articleContent,jdbcType=VARCHAR},
            #{operator,jdbcType=INTEGER},
            #{addDate,jdbcType=TIMESTAMP},
            #{sort,jdbcType=INTEGER},
            1
        )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.course.CourseCategoryEntity">
        update
            psycloud_course_category
        set
            course_id = #{courseId,jdbcType=INTEGER},
            title = #{title,jdbcType=VARCHAR},
            attachment = #{attachment,jdbcType=VARCHAR},
            attachment_file_type = #{attachmentFileType,jdbcType=VARCHAR},
            article_content = #{articleContent,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.course.CourseCategoryDto" resultMap="courseCategoryResultMap">
        select
            id,
            course_id,
            title,
            attachment,
            attachment_file_type,
            article_content,
            sort
        from psycloud_course_category
        where is_valid=1
            <if test="categoryId!=null and categoryId!=0">and id=#{categoryId}</if>
            <if test="courseId!=null and courseId!=0">and course_id=#{courseId}</if>
        order by sort desc
    </select>

    <select id="get" parameterType="cn.psycloud.psyplatform.dto.course.CourseCategoryDto" resultMap="courseCategoryResultMap">
        select
            id,
            course_id,
            title,
            attachment,
            attachment_file_type,
            article_content,
            sort
        from psycloud_course_category
        where is_valid=1
            <if test="categoryId!=null and categoryId!=0">and id=#{categoryId}</if>
            <if test="courseId!=null and courseId!=0">and course_id=#{courseId}</if>
    </select>

    <select id="getCategoryCount" parameterType="INTEGER" resultType="INTEGER">
        select COUNT(*)
        from psycloud_course_category
        where course_id =#{courseId}
    </select>

    <!-- 上移 Start  -->
    <!-- 上移其它   -->
    <update id="moveUpOther" parameterType="map">
        update psycloud_course_category set sort =#{sort,jdbcType=INTEGER} where sort = #{sort,jdbcType=INTEGER}+1 and course_id =#{courseId,jdbcType=INTEGER}
    </update>
    <!-- 上移自己   -->
    <update id="moveUpSelf" parameterType="map">
        update psycloud_course_category set sort =#{sort,jdbcType=INTEGER}+1 where id = #{categoryId,jdbcType=INTEGER}
    </update>
    <!-- 上移 End   -->

    <!--  下移 Start  -->
    <!-- 下移其它    -->
    <update id="moveDownOther" parameterType="map">
        update psycloud_course_category set sort =#{sort,jdbcType=INTEGER} where sort=  #{sort,jdbcType=INTEGER}- 1 and course_id = #{courseId,jdbcType=INTEGER}
    </update>
    <!-- 下移自己   -->
    <update id="moveDownSelf" parameterType="map">
        update psycloud_course_category set sort= #{sort,jdbcType=INTEGER} - 1 WHERE id = #{categoryId,jdbcType=INTEGER}
    </update>
    <!--  下移 End  -->
</mapper>