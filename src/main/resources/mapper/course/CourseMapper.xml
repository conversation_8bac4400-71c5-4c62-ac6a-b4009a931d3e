<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.course.CourseDao">
    <insert id="addCourse" parameterType="cn.psycloud.psyplatform.entity.course.CourseEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_course(
            course_type,
            course_name,
            operator,
            add_date,
            intro,
            trainer,
            trainer_info,
            hit_count,
            thumbnail,
            is_recommend,
            is_publish,
            is_valid
        )
        values (
            #{courseType,jdbcType=INTEGER},
            #{courseName,jdbcType=VARCHAR},
            #{operator,jdbcType=INTEGER},
            #{addDate,jdbcType=TIMESTAMP},
            #{intro,jdbcType=VARCHAR},
            #{trainer,jdbcType=VARCHAR},
            #{trainerInfo,jdbcType=VARCHAR},
            0,
            #{thumbnail,jdbcType=VARCHAR},
            #{isRecommend,jdbcType=INTEGER},
            0,
            1)
    </insert>
    <update id="updateCourse" parameterType="cn.psycloud.psyplatform.entity.course.CourseEntity">
        update
            psycloud_course
        set
            course_type =  #{courseType,jdbcType=INTEGER},
            course_name = #{courseName,jdbcType=VARCHAR},
            intro = #{intro,jdbcType=VARCHAR},
            trainer = #{trainer,jdbcType=VARCHAR},
            trainer_info = #{trainerInfo,jdbcType=VARCHAR},
            thumbnail = #{thumbnail,jdbcType=VARCHAR},
            is_recommend = #{isRecommend,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="deleteCourse" parameterType="Integer">
        update psycloud_course set is_valid = 0 where id = #{courseId}
    </update>

    <update id="updatePublishState" parameterType="map">
        update psycloud_course set is_publish = #{state,jdbcType=INTEGER} where id = #{courseId,jdbcType=INTEGER}
    </update>
    
    <update id="updateHitCount" parameterType="Integer">
        update psycloud_course set hit_count = hit_count + 1 where id = #{courseId}
    </update>
    
    <select id="isExists" parameterType="String">
        select COUNT(*) from psycloud_course where course_name = #{courseName}
    </select>

    <resultMap id="courseMap" type="cn.psycloud.psyplatform.dto.course.CourseDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="product_id" property="productId" jdbcType="INTEGER" />
        <result column="function_type" property="functionType" jdbcType="INTEGER" />
        <result column="course_name" property="courseName" jdbcType="VARCHAR"/>
        <result column="thumbnail" property="thumbnail" jdbcType="VARCHAR"/>
        <result column="trainer" property="trainer" jdbcType="VARCHAR"/>
        <result column="intro" property="intro" jdbcType="VARCHAR"/>
        <result column="trainer_info" property="trainerInfo" jdbcType="VARCHAR"/>
        <result column="hit_count" property="hitCount" jdbcType="INTEGER"/>
        <collection property="comments" select="cn.psycloud.psyplatform.dao.comment.CommentDao.getComments" column="{productId=product_id, functionType=function_type}"  />
    </resultMap>
    <select id="get" parameterType="cn.psycloud.psyplatform.dto.course.CourseDto"  resultMap="courseMap">
        select
            id,
            id as product_id,
            3 as function_type,
            course_name,
            thumbnail,
            trainer,
            intro,
            trainer_info,
            hit_count
        from
            psycloud_course
        where is_valid=1
            <if test="id != null and id !=0">and id=#{id}</if>
            <if test="isPublish !=null">and is_publish=#{isPublish}</if>
            <if test="courseTypeId!=null and courseTypeId!=0">and course_type=#{courseTypeId}</if>
            <if test="courseName!=null and courseName!=''">and course_name like CONCAT('%',#{courseName},'%')</if>
            <if test="isRecommend!=null">and is_recommend=#{isRecommend}</if>
    </select>

    <resultMap id="courseResultMap" type="cn.psycloud.psyplatform.dto.course.CourseDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="course_name" property="courseName" jdbcType="VARCHAR"/>
        <result column="course_type" property="courseTypeId" jdbcType="INTEGER"/>
        <result column="add_date" property="addDate" jdbcType="TIMESTAMP"/>
        <result column="trainer" property="trainer" jdbcType="VARCHAR"/>
        <result column="intro" property="intro" jdbcType="VARCHAR"/>
        <result column="trainer_info" property="trainerInfo" jdbcType="VARCHAR"/>
        <result column="thumbnail" property="thumbnail" jdbcType="VARCHAR"/>
        <result column="is_recommend" property="isRecommend" jdbcType="TINYINT"/>
        <result column="is_publish" property="isPublish" jdbcType="TINYINT"/>
        <result column="type_name" property="courseTypeName" jdbcType="VARCHAR"/>
        <result column="real_name" property="operatorRealName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getListByPaged" parameterType="cn.psycloud.psyplatform.dto.course.CourseDto" resultMap="courseResultMap">
        select
            pc.id,
            pc.course_name,
            pc.course_type,
            pc.add_date,
            pc.trainer,
            pc.intro,
            pc.trainer_info,
            pc.thumbnail,
            pc.is_recommend,
            pc.is_publish,
            pct.type_name,
            pui.real_name
        from psycloud_course pc
            inner join psycloud_user_info pui on pui.user_id = pc.operator
            left join psycloud_course_type pct on pct.id = pc.course_type
        where pc.is_valid=1
            <if test="isPublish !=null">and pc.is_publish=#{isPublish}</if>
            <if test="courseTypeId!=null and courseTypeId!=0">and pc.course_type=#{courseTypeId}</if>
            <if test="courseName!=null and courseName!=''">and pc.course_name like CONCAT('%',#{courseName},'%')</if>
            <if test="isRecommend!=null">and pc.is_recommend=#{isRecommend}</if>
        order by pc.add_date desc
    </select>
    
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.course.CourseDto" resultType="cn.psycloud.psyplatform.dto.course.CourseDto">
        select
            id,
            course_type,
            intro,
            course_name,
            thumbnail,
            hit_count
        from
            psycloud_course
        where is_valid=1
            <if test="isPublish !=null">and is_publish=#{isPublish}</if>
            <if test="courseTypeId!=null and courseTypeId!=0">and course_type=#{courseTypeId}</if>
            <if test="courseName!=null and courseName!=''">and course_name like CONCAT('%',#{courseName},'%')</if>
            <if test="isRecommend!=null">and is_recommend=#{isRecommend}</if>
        order by add_date desc
    </select>
    
    <select id="getListForIndex" resultType="cn.psycloud.psyplatform.entity.course.CourseEntity">
        select
            id,
            course_name,
            thumbnail
        from psycloud_course
        where is_valid = 1
          and is_publish = 1
          and is_recommend = 1
        order by add_date desc limit 4
    </select>
</mapper>