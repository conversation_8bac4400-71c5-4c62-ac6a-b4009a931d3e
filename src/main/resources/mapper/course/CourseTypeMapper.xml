<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.course.CourseTypeDao">
    <resultMap id="courseTypeResultMap" type="cn.psycloud.psyplatform.dto.course.CourseTypeDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="type_name" property="courseTypeName" jdbcType="VARCHAR"/>
        <result column="is_valid" property="isValid" jdbcType="TINYINT"/>
    </resultMap>

    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.course.CourseTypeDto" resultMap="courseTypeResultMap">
        select
            id,
            type_name,
            is_valid
        from psycloud_course_type
        where is_valid=1
            <if test="courseTypeName!=null and courseTypeName!=''">and type_name like CONCAT('%',#{courseTypeName},'%')</if>
    </select>

    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.course.CourseTypeEntity">
        insert into
            psycloud_course_type(type_name,is_valid)
        values(#{courseTypeName,jdbcType=VARCHAR},1)
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.course.CourseTypeEntity">
        update
            psycloud_course_type
        set type_name=#{courseTypeName,jdbcType=VARCHAR}
        where id = #{id}
    </update>
    <update id="delete" parameterType="Integer">
        update
            psycloud_course_type
        set
            is_valid = 0
        where id = #{id}
    </update>
</mapper>

