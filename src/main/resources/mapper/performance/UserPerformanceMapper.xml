<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.performance.UserPerformanceDao">
    <!-- 新增用户绩效信息 -->
    <insert id="insertUserPerformance" parameterType="cn.psycloud.psyplatform.entity.performance.UserPerformanceEntity">
        insert into psycloud_performance (
            user_id, yearly, month_01, month_02, month_03, month_04, month_05, month_06,
            month_07, month_08, month_09, month_10, month_11, month_12,is_valid
        ) values (
            #{userId,jdbcType=INTEGER}, #{yearly,jdbcType=VARCHAR}, #{month01,jdbcType=VARCHAR}, #{month02,jdbcType=VARCHAR}, 
            #{month03,jdbcType=VARCHAR}, #{month04,jdbcType=VARCHAR}, #{month05,jdbcType=VARCHAR}, #{month06,jdbcType=VARCHAR},
            #{month07,jdbcType=VARCHAR}, #{month08,jdbcType=VARCHAR}, #{month09,jdbcType=VARCHAR}, #{month10,jdbcType=VARCHAR}, 
            #{month11,jdbcType=VARCHAR}, #{month12,jdbcType=VARCHAR},1
        )
    </insert>

    <!-- 根据主键ID逻辑删除用户绩效信息 -->
    <update id="deleteById">
        update psycloud_performance set is_valid = 0 where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据用户ID和年份删除用户绩效信息 -->
    <delete id="deleteByUserIdAndYear">
        delete from psycloud_performance where user_id = #{userId,jdbcType=INTEGER} and yearly = #{yearly,jdbcType=VARCHAR}
    </delete>

    <!-- 查询用户绩效信息列表 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.performance.UserPerformanceDto" 
            resultType="cn.psycloud.psyplatform.dto.performance.UserPerformanceDto">
        select
            up.id,
            u.login_name,
            pui.real_name,
            f_GetStructFullName(pui.struct_id) as structFullName,
            up.yearly,
            up.month_01 as month01,
            up.month_02 as month02,
            up.month_03 as month03,
            up.month_04 as month04,
            up.month_05 as month05,
            up.month_06 as month06,
            up.month_07 as month07,
            up.month_08 as month08,
            up.month_09 as month09,
            up.month_10 as month10,
            up.month_11 as month11,
            up.month_12 as month12
        from psycloud_performance up
            left join psycloud_user u on up.user_id = u.user_id
            left join psycloud_user_info pui on pui.user_id = u.user_id
        where
            up.is_valid = 1
            and pui.is_valid = 1
            <if test="loginName != null and loginName != ''">
                and u.login_name like concat('%', #{loginName}, '%')
            </if>
            <if test="realName != null and realName != ''">
                and u.real_name like concat('%', #{realName}, '%')
            </if>
            <if test="yearly != null and yearly != ''">
                and up.yearly = #{yearly}
            </if>
    </select>

    <!-- 判断用户的绩效数据是否存在 -->
    <select id="isExist" parameterType="map" resultType="java.lang.Integer">
        select count(1) from psycloud_performance where user_id = #{userId,jdbcType=INTEGER} and yearly = #{yearly,jdbcType=VARCHAR}
    </select>

    <!-- 根据用户id查询用户绩效信息 -->
    <select id="getByUserId" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.performance.UserPerformanceDto">
        select
            up.yearly,
            up.month_01 as month01,
            up.month_02 as month02,
            up.month_03 as month03,
            up.month_04 as month04,
            up.month_05 as month05,
            up.month_06 as month06,
            up.month_07 as month07,
            up.month_08 as month08,
            up.month_09 as month09,
            up.month_10 as month10,
            up.month_11 as month11,
            up.month_12 as month12
        from psycloud_performance up
            left join psycloud_user u on up.user_id = u.user_id
        where
            up.is_valid = 1 and u.user_id = #{userId}
    </select>
</mapper>