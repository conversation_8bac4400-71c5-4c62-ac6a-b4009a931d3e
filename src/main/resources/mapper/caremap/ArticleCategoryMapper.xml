<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.caremap.CaremapCategoryDao">
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_caremap_category set is_valid = 0 where id = #{id}
    </update>

    <!--添加栏目-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.relaxroom.ArticleCategoryEntity">
        insert into psycloud_caremap_category(
            category_name,
            is_valid
        )
        values(
            #{categoryName,jdbcType=VARCHAR},
            1
        )
    </insert>

    <!--修改栏目-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.relaxroom.ArticleCategoryEntity">
        update psycloud_caremap_category set category_name = #{categoryName,jdbcType=VARCHAR} where id = #{id,jdbcType=INTEGER}
    </update>

    <!--根据条件查询栏目集合-->
    <resultMap id="categoryResultMap" type="cn.psycloud.psyplatform.dto.relaxroom.ArticleCategoryDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <collection property="articles" column="id" select="cn.psycloud.psyplatform.dao.caremap.CaremapArticleDao.getListByCategoryId" />
    </resultMap>
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.relaxroom.ArticleCategoryDto" resultMap="categoryResultMap">
        select
            id,
            category_name
        from psycloud_caremap_category
        where is_valid = 1
            <if test="categoryName != null and categoryName!= ''">and category_name like CONCAT('%',#{categoryName},'%')</if>
    </select>

    <!--根据条件查询栏目集合：分页-->
    <select id="getListByPaged" parameterType="cn.psycloud.psyplatform.dto.relaxroom.ArticleCategoryDto" resultType="cn.psycloud.psyplatform.dto.relaxroom.ArticleCategoryDto">
        select
            id,
            category_name
        from psycloud_caremap_category
        where is_valid = 1
        <if test="categoryName != null and categoryName!= ''">and category_name like CONCAT('%',#{categoryName},'%')</if>
    </select>

    <!--根据id查询栏目-->
    <select id="getById" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.relaxroom.ArticleCategoryDto">
        select id,category_name
        from psycloud_caremap_category
        where is_valid = 1 and id = #{id}
    </select>

    <!--文章统计-->
    <select id="getArticleCapacityList" resultType="cn.psycloud.psyplatform.dto.relaxroom.ArticleCapacityDto">
        select
            pac.category_name as categoryName,
            ifnull(a.count,0) as capacity
        from psycloud_caremap_category pac
            left join (
                select pac2.id, count(*) as count
                from psycloud_caremap_category pac2
                left join psycloud_article pa on pa.category_id = pac2.id
                where pac2.is_valid = 1
                group by pac2.id
            ) as a on pac.id = a.id
        where pac.is_valid = 1
        order by a.id
    </select>
</mapper>