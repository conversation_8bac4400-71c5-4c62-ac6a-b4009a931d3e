<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.survey.SurveyItemDao">
    <!-- 根据选项id删除选项 -->
    <delete id="deleteById" parameterType="Integer">
        delete from psycloud_survey_item where id = #{id}
    </delete>

    <!-- 添加选项 -->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.survey.SurveyItemEntity">
        insert into psycloud_survey_item(
            item_content,
            item_no,
            q_id,
            is_other
        )
        values (
            #{itemContent,jdbcType=VARCHAR},
            #{itemNo,jdbcType=INTEGER},
            #{qId,jdbcType=INTEGER},
            #{isOther,jdbcType=TINYINT}
        )
    </insert>

    <!-- 根据题目id删除选项 -->
    <delete id="deleteByQid" parameterType="Integer">
        delete from psycloud_survey_item where q_id = #{qId}
    </delete>

    <!--根据题目查询选项集合-->
    <select id="getListByQId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.survey.SurveyItemEntity">
        select id,item_content,item_no,q_id,is_other from psycloud_survey_item where q_id = #{qId}
    </select>

    <!--获取选项总数-->
    <select id="getItemCount" parameterType="Integer">
        select count(id) from psycloud_survey_item where q_id=#{qId}
    </select>
</mapper>