<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao">
    <resultMap id="questionMap" type="cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto">
        <result column="q_content" property="qContent" jdbcType="VARCHAR"/>
        <result column="q_number" property="qNumber" jdbcType="INTEGER" />
        <result column="q_type" property="qType" jdbcType="TINYINT" />
        <result column="id" property="id" jdbcType="INTEGER"/>
        <collection property="listItems" select="cn.psycloud.psyplatform.dao.survey.SurveyItemDao.getListByQId" column="id" />
    </resultMap>
    <!-- 根据问卷获取题目集合 -->
    <select id="getListBySurveyId" parameterType="Integer" resultMap="questionMap">
        select id,q_content,q_number,q_type from psycloud_survey_question where is_valid=1 and survey_id = #{surveyId}
    </select>

    <!--获取题目数 -->
    <select id="getQuestionCount" parameterType="Integer" resultType="Integer">
        select count(id) from psycloud_survey_question where is_valid=1 and survey_id=#{surveyId}
    </select>

    <!--根据题目id查询题目所属量表id和题目序号-->
    <resultMap id="getByIdResultMap" type="java.util.HashMap">
        <result column="survey_id" property="survey_id" jdbcType="INTEGER" />
        <result column="q_number" property="q_number" jdbcType="INTEGER" />
    </resultMap>
    <select id="getById" parameterType="Integer" resultMap="getByIdResultMap">
        select survey_id,q_number from psycloud_survey_question where is_valid =1 and id = #{qId}
    </select>

    <!-- 删除题目 -->
    <update id="delete" parameterType="Integer">
        update psycloud_survey_question set is_valid=0 where id=#{qId}
    </update>

    <!-- 添加题目 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.survey.SurveyQuestionEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_survey_question(
            q_content,
            q_number,
            q_type,
            survey_id,
            is_valid
        )
        values (
            #{qContent,jdbcType=VARCHAR},
            #{qNumber,jdbcType=INTEGER},
            #{qType,jdbcType=TINYINT},
            #{surveyId,jdbcType=INTEGER},
            1
        )
    </insert>

    <!-- 修改题目 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.survey.SurveyQuestionEntity">
        update psycloud_survey_question
        set
            q_content = #{qContent,jdbcType=VARCHAR},
            q_type =  #{qType,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--删除题目后更新题目序号-->
    <update id="updateQno" parameterType="java.util.HashMap">
        update psycloud_survey_question set q_number = q_number -1 where is_valid = 1 and q_number &gt; #{qNumber,jdbcType=INTEGER} and survey_id =#{surveyId,jdbcType=INTEGER}
    </update>

    <!--导出问卷选项数据时查询选项题目-->
    <resultMap id="getListForExportMap" type="java.util.LinkedHashMap">
        <result column="q_number" property="q_number" jdbcType="INTEGER" />
        <result column="q_content" property="q_content" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getListForExport" resultMap="getListForExportMap" parameterType="Integer">
        select q_number,q_content from psycloud_survey_question where is_valid=1 and survey_id = #{surveyId} order by q_number
    </select>
</mapper>