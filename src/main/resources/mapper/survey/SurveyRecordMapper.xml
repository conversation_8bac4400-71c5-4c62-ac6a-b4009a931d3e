<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.survey.SurveyRecordDao">
    <!-- 根据记录id删除问卷作答记录  -->
    <update id="delRecord" parameterType="Integer">
        update psycloud_survey_record set is_valid = 0 where id = #{id}
    </update>

    <!-- 删除测评任务下某个用户的所有问卷作答记录 -->
    <update id="delRecordByTaskId" parameterType="map">
        update psycloud_survey_record psr
        inner join psycloud_task_survey_record ptsr on ptsr.survey_record_id = psr.id
        set psr.is_valid = 0
        where psr.is_done = 0
          and ptsr.task_id = #{taskId,jdbcType=INTEGER}
          and psr.user_id = #{userId,jdbcType=INTEGER}
    </update>

    <!-- 删除问卷调查任务下某个用户的所有问卷作答记录 -->
    <update id="delRecordOfTaskSurveyByTaskId" parameterType="map">
        update psycloud_survey_record psr
        inner join psycloud_task_record ptr on ptr.record_id = psr.id
        set psr.is_valid = 0
        where psr.is_done = 0
          and ptr.task_id = #{taskId,jdbcType=INTEGER}
          and psr.user_id = #{userId,jdbcType=INTEGER}
    </update>

    <!-- 删除训练营下某个用户的所有问卷作答记录 -->
    <update id="delRecordByCampId" parameterType="map">
        update psycloud_survey_record psr
        inner join psycloud_trainingcamp_survey_record ptsr on ptsr.survey_record_id = psr.id
        set psr.is_valid = 0
        where psr.is_done = 0
          and ptsr.camp_id = #{campId,jdbcType=INTEGER}
          and psr.user_id = #{userId,jdbcType=INTEGER}
    </update>

    <!-- 删除活动下某个用户的所有问卷作答记录 -->
    <update id="delRecordByActivityId" parameterType="map">
        update psycloud_survey_record psr
        inner join psycloud_activity_survey_record psar on psar.survey_record_id = psr.id
        set psr.is_valid = 0
        where psr.survey_id = #{surveyId,jdbcType=INTEGER}
          and psar.activity_id = #{activityId,jdbcType=INTEGER}
          and psr.user_id = #{userId,jdbcType=INTEGER}
    </update>

    <!-- 测评任务：添加问卷作答记录 -->
    <insert id="addRecord" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_survey_record(
            user_id,
            survey_id,
            record_date,
            is_valid,
            is_done
        )
        values(
            #{userId,jdbcType=INTEGER},
            #{surveyId,jdbcType=INTEGER},
            #{recordDate,jdbcType=TIMESTAMP},
            1,
            0
        )
    </insert>

    <!-- 训练营：添加问卷作答记录 -->
    <insert id="addRecordForCamp" parameterType="cn.psycloud.psyplatform.entity.trainingcamp.TrainingCampSurveyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_survey_record(
            user_id,
            survey_id,
            record_date,
            is_valid,
            is_done
        )
        values(
              #{userId,jdbcType=INTEGER},
              #{surveyId,jdbcType=INTEGER},
              #{recordDate,jdbcType=TIMESTAMP},
              1,
              0
        )
    </insert>

    <!-- 活动：添加问卷作答记录 -->
    <insert id="addRecordForActivity" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivitySurveyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_survey_record(
            user_id,
            survey_id,
            record_date,
            is_valid,
            is_done
        )
        values(
              #{userId,jdbcType=INTEGER},
              #{surveyId,jdbcType=INTEGER},
              #{recordDate,jdbcType=TIMESTAMP},
              1,
              0
        )
    </insert>

    <!-- 添加问卷作答选项结果 -->
    <insert id="addResult" parameterType="cn.psycloud.psyplatform.entity.survey.SurveyResultEntity">
        insert into psycloud_survey_result(
            record_id,
            q_id,
            item_id,
            other_answer
        )
        values (
            #{recordId,jdbcType=INTEGER},
            #{qId,jdbcType=INTEGER},
            #{itemId,jdbcType=VARCHAR},
            #{otherAnswer,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 删除作答记录下的所有选项结果 -->
    <delete id="deleteResultByRecordId" parameterType="Integer">
        delete from psycloud_survey_result where record_id = #{recordId}
    </delete>

    <!-- 更新作答记录状态-->
    <update id="updateSurveyRecordState" parameterType="Integer">
        update psycloud_survey_record set is_done = 1 where id = #{recordId}
    </update>

    <!-- 更新作答时间 -->
    <update id="updateSurveyRecordDate" parameterType="map">
        update psycloud_survey_record set record_date = #{recordDate,jdbcType=TIMESTAMP} where id = #{recordId}
    </update>

    <!-- 添加测评任务与调查问卷关联记录 -->
    <insert id="addTaskSurvey" parameterType="map">
        insert into psycloud_task_survey_record(
            task_id,
            survey_record_id
        )
        values (
            #{taskId,jdbcType=INTEGER},
            #{surveyRecordId,jdbcType=INTEGER}
    )
    </insert>

    <!-- 添加训练营与调查问卷关联记录 -->
    <insert id="addTrainingCampSurvey" parameterType="map">
        insert into psycloud_trainingcamp_survey_record(
            camp_id,
            survey_record_id
        )
        values (
               #{campId,jdbcType=INTEGER},
               #{surveyRecordId,jdbcType=INTEGER}
           )
    </insert>

    <!-- 添加活动与调查问卷关联记录 -->
    <insert id="addActivitySurvey" parameterType="map">
        insert into psycloud_activity_survey_record(
            activity_id,
            survey_record_id
        )
        values (
               #{activityId,jdbcType=INTEGER},
               #{surveyRecordId,jdbcType=INTEGER}
           )
    </insert>

    <!-- 根据条件查询问卷调查记录集合 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto" resultType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto">
        select
            psr.id,
            psr.user_id,
            psr.survey_id,
            ps.survey_name,
            psr.record_date,
            pui.real_name,
            pu.login_name,
            psr.is_done,
            pt.task_name,
            f_GetStructFullName(pui.struct_id) as structFullName
        from psycloud_survey_record psr
                 inner join psycloud_survey ps on ps.id = psr.survey_id
                 inner join psycloud_task_survey_record ptsr on ptsr.survey_record_id = psr.id
                 inner join psycloud_task pt on pt.id = ptsr.task_id
                 inner join psycloud_user_info pui on pui.user_id = psr.user_id
                 inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr.is_valid =1
          and ps.is_valid=1
          and pui.is_valid=1
          and pt.is_valid=1
        <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
        <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{login_name},'%')</if>
        <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{real_name},'%')</if>
        <if test="childStructs!=null and childStructs.size()>0">
            and pui.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="isDone !=null">and psr.is_done = #{isDone}</if>
        <if test="startTime !=null and endTime !=null">and psr.record_date &gt;= #{startTime} and psr.record_date &lt;= #{endTime}</if>
    </select>

    <!-- 根据条件查询问卷调查记录集合 -->
    <select id="getListForTaskSurvey" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto" resultType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto">
        select
            psr.id,
            psr.user_id,
            psr.survey_id,
            ps.survey_name,
            psr.record_date,
            pui.real_name,
            pu.login_name,
            psr.is_done,
            pt.task_name,
            f_GetStructFullName(pui.struct_id) as structFullName
        from psycloud_survey_record psr
        inner join psycloud_survey ps on ps.id = psr.survey_id
        inner join psycloud_task_record ptsr on ptsr.record_id = psr.id
        inner join psycloud_task pt on pt.id = ptsr.task_id
        inner join psycloud_user_info pui on pui.user_id = psr.user_id
        inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr.is_valid =1
        and ps.is_valid=1
        and pui.is_valid=1
        and pt.is_valid=1
        <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
        <if test="surveyId !=null and surveyId !=0">and ps.id = #{surveyId}</if>
        <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{login_name},'%')</if>
        <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{real_name},'%')</if>
        <if test="childStructs!=null and childStructs.size()>0">
            and pui.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="isDone !=null">and psr.is_done = #{isDone}</if>
        <if test="startTime !=null and endTime !=null">and psr.record_date &gt;= #{startTime} and psr.record_date &lt;= #{endTime}</if>
    </select>

    <!--导出作答记录：测评任务-->
    <select id="getExportRecordList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto" resultType="cn.psycloud.psyplatform.dto.survey.ExportSurveyRecordDto">
        select
            pu.login_name,
            pui.real_name,
            f_GetStructFullName(pui.struct_id) as structFullName,
            pt.task_name,
            ps.survey_name,
            psr.record_date,
            case psr.is_done when 0 then '未完成' when 1 then '已完成'  end as isDone
        from psycloud_survey_record psr
            inner join psycloud_survey ps on ps.id = psr.survey_id
            inner join psycloud_task_survey_record ptsr on ptsr.survey_record_id = psr.id
            inner join psycloud_task pt on pt.id = ptsr.task_id
            inner join psycloud_user_info pui on pui.user_id = psr.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr.is_valid =1
            and ps.is_valid=1
            and pui.is_valid=1
            and pt.is_valid=1
            <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
            <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{login_name},'%')</if>
            <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{real_name},'%')</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isDone !=null">and psr.is_done = #{isDone}</if>
            <if test="startTime !=null and endTime !=null">and psr.record_date &gt;= #{startTime} and psr.record_date &lt;= #{endTime}</if>
    </select>

    <!--导出作答记录：问卷调查任务-->
    <select id="getExportTaskSurveyRecord" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto" resultType="cn.psycloud.psyplatform.dto.survey.ExportSurveyRecordDto">
        select
            pu.login_name,
            pui.real_name,
            f_GetStructFullName(pui.struct_id) as structFullName,
            pt.task_name,
            ps.survey_name,
            psr.record_date,
            case psr.is_done when 0 then '未完成' when 1 then '已完成'  end as isDone
        from psycloud_survey_record psr
            inner join psycloud_survey ps on ps.id = psr.survey_id
            inner join psycloud_task_record ptsr on ptsr.record_id = psr.id
            inner join psycloud_task pt on pt.id = ptsr.task_id
            inner join psycloud_user_info pui on pui.user_id = psr.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr.is_valid =1
            and ps.is_valid=1
            and pui.is_valid=1
            and pt.is_valid=1
            <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
            <if test="surveyId !=null and surveyId !=0">and ps.id = #{surveyId}</if>
            <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{login_name},'%')</if>
            <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{real_name},'%')</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isDone !=null">and psr.is_done = #{isDone}</if>
            <if test="startTime !=null and endTime !=null">and psr.record_date &gt;= #{startTime} and psr.record_date &lt;= #{endTime}</if>
    </select>

    <!-- 导出调查问卷结果：问卷调查任务-->
    <select id="getExportTaskSurveyResult" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto">
        select
        /*+ GROUP_OPT_FLAG(1)*/
        pui.user_id as 用户id,
        pu.login_name as 用户名,
        pui.real_name as 姓名,
        pui.sex as 性别,
        TIMESTAMPDIFF(YEAR,pui.birth,CURDATE()) as 年龄,
        f_GetStructFullName(pui.struct_id) as 所属组织,
        ${sql}
        from psycloud_survey_question psq
            left join psycloud_survey_result psr on psr.q_id = psq.id
            inner join psycloud_survey_record psr2 on psr2.id = psr.record_id and psq.survey_id = psr2.survey_id
            inner join psycloud_user_info pui on pui.user_id = psr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr2.is_valid = 1
            and psr2.is_done = 1
            and pui.is_valid = 1
            and psr2.survey_id = #{surveyId}
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskId !=null and taskId !=0">
                and psr2.id in (
                    select ptsr3.record_id
                    from psycloud_task_record ptsr3
                        inner join psycloud_task pt on pt.id = ptsr3.task_id
                    where  pt.id = #{taskId}
                )
            </if>
        group by pui.user_id,pu.login_name,pui.real_name,pui.sex,pui.birth
        order by pui.user_id,psq.q_number
    </select>

    <!--获取训练营的问卷作答记录 -->
    <select id="getTrainingCampSurveyRecordList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.trainingcamp.TrainingCampSurveyRecordDto">
        select
            psr.id,
            pu.login_name,
            pui.real_name,
            ps.survey_name,
            psr.record_date,
            psr.is_done
        from psycloud_trainingcamp_survey_record ptsr
            inner join psycloud_survey_record psr on psr.id = ptsr.survey_record_id
            inner join psycloud_survey ps on ps.id = psr.survey_id
            inner join psycloud_user pu on pu.user_id = psr.user_id
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where camp_id=#{campId}
    </select>

    <!--获取活动的问卷作答记录 -->
    <select id="getActivitySurveyRecordList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivitySurveyRecordDto">
        select
            psr.id,
            pu.login_name,
            pui.real_name,
            ps.survey_name,
            psr.record_date,
            psr.is_done
        from psycloud_activity_survey_record psar
            inner join psycloud_survey_record psr on psr.id = psar.survey_record_id
            inner join psycloud_survey ps on ps.id = psr.survey_id
            inner join psycloud_user pu on pu.user_id = psr.user_id
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where activity_id=#{activityId}
    </select>

    <!-- 获取我的问卷记录 -->
    <select id="getMyRecords" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto" resultType="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto">
        select
            psr.id,
            ps.survey_name,
            psr.record_date,
            psr.is_done
        from psycloud_survey_record psr
            inner join psycloud_user_info pui on pui.user_id = psr.user_id
            inner join psycloud_survey ps on ps.id = psr.survey_id
        where psr.is_valid =1
            and pui.is_valid = 1
            and pui.user_id = #{userId}
            and psr.id not in (
                select ptr2.record_id
                from psycloud_task_record ptr2
                    inner join psycloud_task pt on pt.id = ptr2.task_id
                    inner join psycloud_survey_record ptr3 on ptr3.id = ptr2.record_id
                where pt.is_valid=0 and ptr3.user_id =#{userId}
        )
        <if test="surveyName !=null and surveyName != ''">and ps.survey_name =#{surveyName}</if>
        order by psr.id desc
    </select>

    <!-- 获取调查问卷记录id -->
    <select id="getSurveyRecordId" parameterType="map" resultType="java.lang.Integer">
        select psr.id
        from psycloud_survey_record psr
            inner join psycloud_survey ps on ps.id = psr.survey_id
            inner join psycloud_task_survey_record ptsr on ptsr.survey_record_id = psr.id
            inner join psycloud_task pt on pt.id = ptsr.task_id
            inner join psycloud_user_info pui on pui.user_id = psr.user_id
        where psr.is_valid =1
            and ps.is_valid=1
            and pui.is_valid=1
            and pt.is_valid=1
            and pt.id = #{taskId}
            and pui.user_id = #{userId}
            and ps.id = #{surveyId}
    </select>
</mapper>