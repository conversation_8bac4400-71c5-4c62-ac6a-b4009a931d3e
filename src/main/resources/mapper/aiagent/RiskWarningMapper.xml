<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.aiagent.RiskWarningDao">
    <!-- 添加风险预警 -->
    <insert id="addRiskWarning" parameterType="cn.psycloud.psyplatform.entity.aiagent.conversation.RiskWarningEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_ai_risk(
            conversation_id,
            user_id,
            risk_level,
            topic,
            risk_reason,
            risk_accord,
            risk_detail,
            risk_date,
            is_valid
        )
        values(
            #{conversationId,jdbcType=VARCHAR},
            #{userId,jdbcType=INTEGER},
            #{riskLevel,jdbcType=VARCHAR},
            #{topic,jdbcType=VARCHAR},
            #{riskReason,jdbcType=VARCHAR},
            #{riskAccord,jdbcType=VARCHAR},
            #{riskDetail,jdbcType=VARCHAR},
            #{riskDate,jdbcType=TIMESTAMP},
            1
        )
    </insert>

    <!-- 查询风险预警集合 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.aiagent.conversation.RiskWarningDto" resultType="cn.psycloud.psyplatform.dto.aiagent.conversation.RiskWarningDto">
        select
            parw.id,
            parw.conversation_id,
            parw.user_id,
            pu.login_name,
            parw.risk_level,
            parw.topic,
            parw.risk_reason,
            parw.risk_accord,
            parw.risk_detail,
            parw.risk_date,
            parw.is_valid
        from psycloud_ai_risk parw
            left join psycloud_user pu on pu.user_id = parw.user_id
        where parw.is_valid = 1
        <if test="userId != null and userId != ''">and parw.user_id = #{userId}</if>
        <if test="riskLevel != null and riskLevel != ''">and parw.risk_level = #{riskLevel}</if>
        <if test="topic != null and topic != ''">and parw.topic like CONCAT('%',#{topic},'%')</if>
        order by parw.risk_date desc
    </select>
</mapper>