<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.aiagent.AIAgentStatDao">

    <resultMap id="SubjectStatResultMap" type="cn.psycloud.psyplatform.dto.aiagent.stat.SubjectStatDto">
        <result property="topicName" column="topic"/>
        <result property="count" column="topic_count"/>
    </resultMap>

    <!-- 查询最新的预警信息 -->
    <select id="selectLatestWarnings" resultType="cn.psycloud.psyplatform.dto.aiagent.conversation.RiskWarningDto">
        select
            parw.id,
            parw.conversation_id,
            parw.user_id,
            pu.login_name,
            parw.risk_level,
            parw.topic,
            parw.risk_reason,
            parw.risk_accord,
            parw.risk_detail,
            parw.risk_date
        from psycloud_ai_risk parw
            left join psycloud_user pu on pu.user_id = parw.user_id
        where parw.is_valid = 1
        order by parw.risk_date desc
        limit #{limit}
    </select>

    <!-- 查询对话主题排行 -->
    <select id="selectSubjectRanking" resultMap="SubjectStatResultMap">
        select topic, count(id) as topic_count
        from psycloud_ai_risk
        where is_valid = 1 and topic is not null
        group by topic
        order by topic_count desc
    </select>

    <!-- 查询总对话轮数 -->
    <select id="selectTotalConversationRounds" resultType="java.lang.Long">
        select count(id) from psycloud_ai_conversation where is_valid = 1
    </select>

    <!-- 查询总对话天数 -->
    <select id="selectTotalConversationDays" resultType="java.lang.Long">
        select count(DISTINCT DATE(chat_date)) from psycloud_ai_conversation where is_valid = 1
    </select>

    <!-- 查询参与对话的总用户数 -->
    <select id="selectTotalUsersInConversation" resultType="java.lang.Long">
        select count(DISTINCT user_id) from psycloud_ai_conversation where is_valid = 1
    </select>

    <!-- 查询预警等级分布 -->
    <select id="selectWarningLevelDistribution" resultType="java.util.Map">
        select risk_level as name, count(id) as value
        from psycloud_ai_risk
        where is_valid = 1 and risk_level is not null
        group by risk_level
    </select>

    <!-- 查询预警主题（关键词）分布 -->
    <select id="selectRiskTopicDistribution" resultType="java.util.Map">
        select topic as name, count(id) as value
        from psycloud_ai_risk
        where is_valid = 1 and topic is not null
        group by topic
    </select>

    <!-- 查询最近一周AI对话次数统计 -->
    <select id="selectWeeklyConversationStats" resultType="cn.psycloud.psyplatform.dto.aiagent.stat.WeeklyConversationStatDto">
        select 
            DATE_FORMAT(chat_date, '%Y-%m-%d') as date,
            count(id) as count
        from psycloud_ai_conversation
        where is_valid = 1 
        and chat_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        group by DATE_FORMAT(chat_date, '%Y-%m-%d')
        order by date asc
    </select>

</mapper>