<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.aiagent.ConversationDao">
    <!-- 添加会话 -->
    <insert id="addConversation" parameterType="cn.psycloud.psyplatform.entity.aiagent.conversation.ConversationEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_ai_conversation(
            bot_id,
            bot_name,
            user_id,
            conversation_id,
            role,
            chat_content,
            chat_date,
            is_valid
        )
        values(
            #{botId,jdbcType=VARCHAR},
            #{botName,jdbcType=VARCHAR},
            #{userId,jdbcType=INTEGER},
            #{conversationId,jdbcType=VARCHAR},
            #{role,jdbcType=VARCHAR},
            #{chatContent,jdbcType=VARCHAR},
            #{chatDate,jdbcType=TIMESTAMP},
            1
        )
    </insert>

    <!-- 查询会话集合 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.aiagent.conversation.ConversationDto" resultType="cn.psycloud.psyplatform.dto.aiagent.conversation.ConversationDto">
        select
            pac.id,
            pac.bot_id,
            pac.bot_name,
            pac.user_id,
            pu.login_name,
            pac.conversation_id,
            pac.role,
            pac.chat_content,
            pac.chat_date,
            pac.is_valid
        from psycloud_ai_conversation pac
            left join psycloud_user pu on pu.user_id = pac.user_id
        where pac.is_valid = 1
        <if test="botId != null and botId != ''">and pac.bot_id = #{botId}</if>
        <if test="conversationId != null and conversationId != ''">and pac.conversation_id = #{conversationId}</if>
        order by pac.chat_date asc
    </select>
</mapper>