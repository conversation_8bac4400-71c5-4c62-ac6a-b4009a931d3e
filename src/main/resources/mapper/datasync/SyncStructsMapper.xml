<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.datasync.SyncStructsDao">
    <select id="getSchools" resultType="java.lang.String">
        select distinct school_name from sync_structs
    </select>

    <select id="getYearBySchool" parameterType="String" resultType="java.lang.String">
        select distinct join_year from sync_structs where school_name = #{schoolName} order by join_year
    </select>
    
    <select id="getOrgByYearAndSchool" parameterType="java.util.HashMap" resultType="java.util.Map">
        select org_name,org_no from sync_structs where join_year =  #{joinYear} and school_name = #{schoolName}
    </select>

</mapper>