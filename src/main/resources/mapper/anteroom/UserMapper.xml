<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.anteroom.UserDao">
    <resultMap id="userResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="password" property="pwd" jdbcType="VARCHAR" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="struct_id" property="structId" jdbcType="INTEGER" />
        <result column="sex" property="sex" jdbcType="CHAR" />
        <result column="birth" property="birth" jdbcType="VARCHAR" />
        <result column="idcard_no" property="iDCardNo" jdbcType="VARCHAR" />
        <result column="nation" property="nation" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="email" property="email" jdbcType="VARCHAR" />
        <result column="address_province" property="addressProvince" jdbcType="VARCHAR" />
        <result column="address_city" property="addressCity" jdbcType="VARCHAR" />
        <result column="address_dist" property="addressDist" jdbcType="VARCHAR" />
        <result column="address_detail" property="addressDetail" jdbcType="VARCHAR" />
        <result column="head_img" property="headPic" jdbcType="VARCHAR" />
        <result column="native_place" property="nativePlace" jdbcType="VARCHAR" />
        <result column="is_checked" property="isChecked" jdbcType="TINYINT" />
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR" />
        <result column="last_login_date" property="lastLoginDateTime" jdbcType="TIMESTAMP" />
        <result column="reg_date" property="regDate" jdbcType="TIMESTAMP" />
        <result column="operator" property="operator" jdbcType="INTEGER" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
        <result column="role_id" property="roleId" jdbcType="INTEGER" />
        <result column="role_name" property="role.roleName" jdbcType="VARCHAR" />
        <result column="structFullName" property="structFullName" jdbcType="VARCHAR" />
        <result column="struct_name" property="structName" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="is_mobile_bind" property="isMobileBind" jdbcType="TINYINT" />
        <result column="points" property="points" jdbcType="INTEGER" />
        <result column="emergency_contact_person" property="emergencyContactPerson" jdbcType="VARCHAR" />
        <result column="emergency_contact_mobile" property="emergencyContactMobile" jdbcType="VARCHAR" />
        <result column="education" property="education" jdbcType="VARCHAR"/>
        <result column="job" property="job" jdbcType="VARCHAR"/>
        <result column="marriage" property="marriage" jdbcType="VARCHAR"/>
        <result column="religion" property="religion" jdbcType="VARCHAR"/>
        <result column="flag" property="flag" jdbcType="CHAR"/>

        <collection property="counselorInfo" ofType="CounselorInfoEntity">
            <result column="user_id" property="userId" jdbcType="INTEGER"/>
            <result column="is_recommend" property="isRecommend" jdbcType="TINYINT"/>
            <result column="be_good_at" property="beGoodAt" jdbcType="VARCHAR"/>
            <result column="counselor_info" property="counselorIntro" jdbcType="LONGVARCHAR"/>
        </collection>
        <collection property="comments" select="cn.psycloud.psyplatform.dao.comment.CommentDao.getCommentsForCounselorInfo" column="product_id" />
    </resultMap>

    <sql id="user_field_list">
        psycloud_user.user_id,
        psycloud_user.user_id as product_id,
        4 as function_type,
        psycloud_user.login_name,
        psycloud_user.password,
        psycloud_user_info.real_name,
        psycloud_user_info.struct_id,
        psycloud_user_info.sex,
        psycloud_user_info.birth,
        psycloud_user_info.idcard_no,
        psycloud_user_info.nation,
        psycloud_user_info.mobile,
        psycloud_user_info.email,
        psycloud_user_info.address_province,
        psycloud_user_info.address_city,
        psycloud_user_info.address_dist,
        psycloud_user_info.address_detail,
        psycloud_user_info.head_img,
        psycloud_user_info.native_place,
        psycloud_user_info.is_checked,
        psycloud_user_info.last_login_ip,
        psycloud_user_info.last_login_date,
        psycloud_user_info.reg_date,
        psycloud_user_info.operator,
        psycloud_user_info.is_valid,
        psycloud_user_role.role_id,
        psycloud_role.role_name,
--         f_GetStructFullName(psycloud_user_info.struct_id) AS StructFullName,
        psycloud_structs.struct_name,
        psycloud_user_info.description,
        psycloud_user_info.is_mobile_bind,
        psycloud_user_info.points,
        psycloud_user_info.emergency_contact_person,
        psycloud_user_info.emergency_contact_mobile,
        psycloud_user_info.education,
        psycloud_user_info.job,
        psycloud_user_info.marriage,
        psycloud_user_info.religion,
        psycloud_counselor_info.is_recommend,
        psycloud_counselor_info.be_good_at,
        psycloud_counselor_info.counselor_info
    </sql>

    <resultMap id="signInResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="login_name" property="loginName" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="head_img" property="headPic" jdbcType="VARCHAR"/>
        <result column="role_id" property="role.roleId" jdbcType="INTEGER"/>
        <result column="struct_id" property="structId" jdbcType="INTEGER"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="is_mobile_bind" property="isMobileBind" jdbcType="TINYINT"/>
    </resultMap>

    <select id="signIn" resultMap="signInResultMap" parameterType="cn.psycloud.psyplatform.dto.anteroom.LoginDto">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_user_info.head_img,
            psycloud_user_info.struct_id,
            psycloud_user_info.mobile,
            psycloud_user_info.is_mobile_bind,
            psycloud_user_role.role_id
        from psycloud_user
                 inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
                 inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
        where psycloud_user_info.is_valid = 1
          and psycloud_user_info.is_checked = 1
          and psycloud_user.login_name = #{loginName}
          and psycloud_user.password = #{pwd}
    </select>

    <!--短信登录-->
    <select id="smsLogin" parameterType="String" resultMap="signInResultMap">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_user_info.head_img,
            psycloud_user_info.struct_id,
            psycloud_user_info.mobile,
            psycloud_user_info.is_mobile_bind,
            psycloud_user_role.role_id
        from psycloud_user
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
        where psycloud_user_info.is_valid = 1
            and psycloud_user_info.is_checked = 1
            and psycloud_user_info.is_mobile_bind = 1
            and psycloud_user_info.mobile = #{mobile}
    </select>

    <!--手机号码登录/注册-->
    <select id="mobileLogin" parameterType="String" resultMap="signInResultMap">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_user_info.head_img,
            psycloud_user_info.struct_id,
            psycloud_user_info.mobile,
            psycloud_user_info.is_mobile_bind,
            psycloud_user_role.role_id
        from psycloud_user
             inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
             inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
        where psycloud_user_info.is_valid = 1
            and psycloud_user_info.is_checked = 1
            and psycloud_user_info.mobile = #{mobile}
            and psycloud_user_info.is_mobile_bind = 1
    </select>

    <select id="getUserList" resultMap="userResultMap" parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        select <include refid="user_field_list" />
        from psycloud_user
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            left join psycloud_structs on psycloud_structs.id = psycloud_user_info.struct_id
            inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
            inner join psycloud_role on psycloud_role.id = psycloud_user_role.role_id
            left join psycloud_counselor_info on psycloud_counselor_info.user_id = psycloud_user_info.user_id
        where  psycloud_user_info.is_valid = 1
            <if test="userId != null and userId != 0">and psycloud_user.user_id  = #{userId}</if>
            <if test="loginName != null and loginName != ''">and psycloud_user.login_name =#{loginName}</if>
            <if test="realName != null and realName != ''">and psycloud_user_info.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="isChecked != null">and psycloud_user_info.is_checked = #{isChecked,jdbcType=TINYINT}</if>
            <if test="isMobileBind != null">and psycloud_user_info.is_mobile_bind = #{isMobileBind,jdbcType=TINYINT}</if>
            <if test="mobile != null and mobile != ''">and psycloud_user_info.mobile = #{mobile}</if>
            <if test="isCounselor == 1">and psycloud_role.id not in (1,3,4)</if>
            <if test="roleId != null and roleId != ''">and psycloud_role.id = #{roleId}</if>
            <if test="flag != null and flag != ''">and psycloud_role.flag = #{flag} and psycloud_role.id not in (2,3)</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and psycloud_user_info.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by psycloud_user.user_id desc
    </select>

    <resultMap id="visitorResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="sex" property="sex" jdbcType="CHAR"/>
        <result column="birth" property="birth" jdbcType="VARCHAR"/>
        <result column="structFullName" property="structFullName" jdbcType="VARCHAR" />
        <result column="points" property="points" jdbcType="INTEGER" />
    </resultMap>
    <select id="getVisitorList" resultMap="visitorResultMap">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            ifnull(psycloud_user_info.real_name,'') as real_name,
            ifnull(psycloud_user_info.sex,'') as sex,
            psycloud_user_info.birth,
            f_GetStructFullName(psycloud_user_info.struct_id) AS structFullName,
            psycloud_user_info.points
        from psycloud_user
                 inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
                 left join psycloud_structs on psycloud_structs.id = psycloud_user_info.struct_id
                 inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
        where  psycloud_user_info.is_valid = 1
            and psycloud_user_role.role_id= 3
            <if test="userId != null and userId != 0">and psycloud_user_info.user_id  = #{userId}</if>
            <if test="loginName != null and loginName != ''">and psycloud_user.login_name =#{loginName}</if>
            <if test="realName != null and realName != ''">and psycloud_user_info.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="isChecked != null">and psycloud_user_info.is_checked = #{isChecked,jdbcType=TINYINT}</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and psycloud_user_info.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by psycloud_user_info.user_id desc
    </select>

    <resultMap id="adminUserResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="head_img" property="headPic" jdbcType="VARCHAR"/>
        <result column="role_name" property="role.roleName" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getAdminUsers" resultMap="adminUserResultMap">
        select
            pu.user_id,
            pu.login_name,
            pui.real_name,
            pr.role_name,
            pui.head_img
        from psycloud_user pu
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
            inner join psycloud_user_role pur on pur.user_id = pui.user_id
            inner join psycloud_role pr on pr.id = pur.role_id
        where pui.is_valid =1
            and pr.flag = 'p'
            and pr.id not in (2,3,4)
            and pr.is_valid = 1
            <if test="loginName != null and loginName != ''">and pu.login_name =#{loginName}</if>
            <if test="realName != null and realName != ''">and pui.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="roleId != null and roleId != 0">and pr.id = #{roleId}</if>
        order by pu.user_id desc
    </select>

    <resultMap id="counselorResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="structFullName" property="structFullName" jdbcType="VARCHAR" />
        <result column="head_img" property="headPic" jdbcType="VARCHAR"/>
        <result column="role_name" property="role.roleName" jdbcType="VARCHAR"/>
        <result column="is_recommend" property="counselorInfo.isRecommend" jdbcType="TINYINT"/>
    </resultMap>
    <select id="getCounselorList" resultMap="counselorResultMap">
        select
            pu.user_id,
            pu.login_name,
            pui.real_name,
            f_GetStructFullName(pui.struct_id) as structFullName,
            pr.role_name,
            pci.is_recommend,
            pui.head_img
        from psycloud_user pu
             inner join psycloud_user_info pui on pui.user_id = pu.user_id
             inner join psycloud_structs ps on ps.id = pui.struct_id
             inner join psycloud_user_role pur on pur.user_id = pu.user_id
             inner join psycloud_role pr on pr.id = pur.role_id
             left join psycloud_counselor_info pci on pci.user_id = pu.user_id
        where pui.is_valid = 1
            and pr.flag='j'
            and pr.id not in(1,3,4)
            <if test="roleId != null and roleId != 0">and pr.id = #{roleId}</if>
            <if test="loginName != null and loginName != ''">and pu.login_name =#{loginName}</if>
            <if test="realName != null and realName != ''">and pui.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by pu.user_id desc
    </select>

    <!--获取推荐咨询师集合(下拉列表)：select-->
    <select id="getCounselorListForSelect" parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto" resultType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        select
            pui.user_id,
            pui.real_name
        from psycloud_user_info pui
            inner join psycloud_counselor_info pci on pci.user_id = pui.user_id
        where pui.is_valid =1 and pui.is_checked=1 and pci.is_recommend =1
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <resultMap id="recommendCounselorResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="head_img" property="headPic" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="is_recommend" property="counselorInfo.isRecommend" jdbcType="TINYINT"/>
        <result column="be_good_at" property="counselorInfo.beGoodAt" jdbcType="VARCHAR"/>
        <result column="counselor_info" property="counselorInfo.counselorIntro" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <select id="getRecommendCounselorList" resultMap="recommendCounselorResultMap">
        select
            pui.user_id,
            pui.real_name,
            pui.head_img,
            pui.description,
            pci.be_good_at,
            pci.counselor_info,
            pci.is_recommend
        from psycloud_counselor_info pci
            inner join psycloud_user_info pui on pui.user_id = pci.user_id
        where pui.is_valid = 1
            and pui.is_checked =1
            and pci.is_recommend = 1
        order by pui.reg_date desc
    </select>
    
    <select id="get" resultMap="userResultMap" parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        select <include refid="user_field_list" />
        from psycloud_user
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            left join psycloud_structs on psycloud_structs.id = psycloud_user_info.struct_id
            inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
            inner join psycloud_role ON psycloud_role.id = psycloud_user_role.role_id
            left join psycloud_counselor_info on psycloud_counselor_info.user_id = psycloud_user_info.user_id
        where  psycloud_user_info.is_valid = 1
            <if test="userId != null and userId != 0">and psycloud_user.user_id  = #{userId}</if>
            <if test="loginName != null and loginName != ''">and psycloud_user.login_name =#{loginName}</if>
            <if test="realName != null and realName != ''">and psycloud_user_info.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="isChecked != null">and psycloud_user_info.is_checked = #{isChecked}</if>
            <if test="isMobileBind != null">and psycloud_user_info.is_mobile_bind = #{isMobileBind}</if>
            <if test="mobile != null and mobile != ''">and psycloud_user_info.mobile = #{mobile}</if>
            <if test="isCounselor!=null and isCounselor == 1">and psycloud_role.id not in (1,3)</if>
            <if test="roleId != null and roleId != ''">and psycloud_role.id = #{roleId}</if>
            <if test="flag != null and flag != ''">and psycloud_role.flag = #{flag} and psycloud_role.id not in (2,3,4)</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and psycloud_user_info.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="getUserByName" parameterType="String" resultMap="userResultMap">
        select <include refid="user_field_list" />
        from psycloud_user
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            left join psycloud_structs on psycloud_structs.id = psycloud_user_info.struct_id
            inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
            inner join psycloud_role on psycloud_role.id = psycloud_user_role.role_id
            left join psycloud_counselor_info on psycloud_counselor_info.user_id = psycloud_user_info.user_id
        where  psycloud_user_info.is_valid = 1
        and psycloud_user.login_name = #{loginName}
    </select>

    <!--预约咨询验证来访者信息-->
    <select id="checkUserInfo" parameterType="String" resultType="String">
        select psycloud_user.user_id
        from psycloud_user
             inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
             inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
        where  psycloud_user_info.is_valid = 1
            and psycloud_user_role.role_id= 3
            and psycloud_user.login_name = #{loginName}
    </select>

    <update id="deleteById" parameterType="Integer">
        update psycloud_user_info set is_valid = 0 WHERE user_id = #{userId}
    </update>

    <insert id="addUser" parameterType="cn.psycloud.psyplatform.entity.anteroom.UserEntity" useGeneratedKeys="true" keyProperty="userId">
        insert into psycloud_user(
            login_name,
            password
        )
        values(
                  #{loginName,jdbcType=VARCHAR},
                  #{pwd,jdbcType=VARCHAR}
              )
    </insert>

    <insert id="addUserInfo" parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        insert into psycloud_user_info(
            user_id,
            struct_id,
            real_name,
            sex,
            birth,
            idcard_no,
            nation,
            mobile,
            is_mobile_bind,
            emergency_contact_person,
            emergency_contact_mobile,
            email,
            address_province,
            address_city,
            address_dist,
            address_detail,
            head_img,
            native_place,
            education,
            job,
            religion,
            marriage,
            description,
            is_checked,
            last_login_date,
            last_login_ip,
            reg_date,
            points,
            operator,
            is_valid,
            sync_user_id
        )
        VALUES(
          #{userId,jdbcType=INTEGER},
          #{structId,jdbcType=INTEGER},
          #{realName,jdbcType=VARCHAR},
          #{sex,jdbcType=CHAR},
          #{birth,jdbcType=VARCHAR},
          #{iDCardNo,jdbcType=VARCHAR},
          #{nation,jdbcType=VARCHAR},
          #{mobile,jdbcType=VARCHAR},
          #{isMobileBind,jdbcType=TINYINT},
          #{emergencyContactPerson,jdbcType=VARCHAR},
          #{emergencyContactMobile,jdbcType=VARCHAR},
          #{email,jdbcType=VARCHAR},
          #{addressProvince,jdbcType=VARCHAR},
          #{addressCity,jdbcType=VARCHAR},
          #{addressDist,jdbcType=VARCHAR},
          #{addressDetail,jdbcType=VARCHAR},
          #{headPic,jdbcType=VARCHAR},
          #{nativePlace,jdbcType=VARCHAR},
          #{education,jdbcType=VARCHAR},
          #{job,jdbcType=VARCHAR},
          #{religion,jdbcType=VARCHAR},
          #{marriage,jdbcType=VARCHAR},
          #{description,jdbcType=VARCHAR},
          #{isChecked,jdbcType=TINYINT},
          #{lastLoginDateTime,jdbcType=TIMESTAMP},
          #{lastLoginIp,jdbcType=VARCHAR},
          #{regDate,jdbcType=TIMESTAMP},
          0,
          #{operator,jdbcType=INTEGER},
          1,
          #{syncUserId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="addCounselorInfo" parameterType="cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_counselor_info(
            user_id,
            is_recommend,
            be_good_at,
            counselor_info
        )
        values (
                   #{userId,jdbcType=INTEGER},
                   #{isRecommend,jdbcType=TINYINT},
                   #{beGoodAt,jdbcType=VARCHAR},
                   #{counselorIntro,jdbcType=LONGVARCHAR}
               )
    </insert>

    <select id="getPwdById" parameterType="Integer" resultType="String">
        select
            password
        from psycloud_user
        where user_id = #{userId}
    </select>

    <update id="modifyPassword" parameterType="cn.psycloud.psyplatform.dto.anteroom.ModifyPwdDto">
        update
            psycloud_user
        set
            password = #{newPwd}
        where
            user_id = #{userId}
    </update>

    <!-- 修改密码，验证旧密码 -->
    <update id="modifyPasswordByOriginalPwd" parameterType="cn.psycloud.psyplatform.dto.anteroom.ModifyPwdDto">
        update
            psycloud_user
        set
            password = #{newPwd}
        where
            user_id = #{userId} and password=#{originalPwd}
    </update>

    <!--获取最后修改密码时间 -->
    <select id="getLastPasswordChangeDate" parameterType="Integer" resultType="Date">
        select  last_password_change_date from psycloud_user_info where user_id=#{userId}
    </select>

    <update id="updateLoginDateAndIp" parameterType="map">
        update psycloud_user_info
        set
            last_login_date = #{lastLoginDate,jdbcType=TIMESTAMP},
            last_login_ip = #{lastLoginIp}
        where user_id = #{userId}
    </update>

    <delete id="deleteUserRole" parameterType="Integer">
        delete from psycloud_user_role where user_id = #{userId}
    </delete>

    <insert id="addUserRole" parameterType="map">
        insert into psycloud_user_role(
            user_id,
            role_id
        )
        values (
                   #{userId,jdbcType=INTEGER},
                   #{roleId,jdbcType=INTEGER}
               )
    </insert>

    <update id="updateUserRole" parameterType="map">
        update psycloud_user_role set role_id=#{roleId,jdbcType=INTEGER} where user_id=#{userId,jdbcType=INTEGER}
    </update>

    <update id="updateUser" parameterType="map">
        update psycloud_user set login_name = #{loginName,jdbcType=VARCHAR} where user_id = #{userId,jdbcType=INTEGER}
    </update>

    <update id="updateUserInfo"  parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        update psycloud_user_info
        set
            struct_id = #{structId,jdbcType=INTEGER},
            real_name = #{realName,jdbcType=VARCHAR},
            sex = #{sex,jdbcType=CHAR},
            birth = #{birth,jdbcType=VARCHAR},
            idcard_no = #{iDCardNo,jdbcType=VARCHAR},
            nation = #{nation,jdbcType=VARCHAR},
            mobile = #{mobile,jdbcType=VARCHAR},
            emergency_contact_person =  #{emergencyContactPerson,jdbcType=VARCHAR},
            emergency_contact_mobile = #{emergencyContactMobile,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            address_province = #{addressProvince,jdbcType=VARCHAR},
            address_city = #{addressCity,jdbcType=VARCHAR},
            address_dist = #{addressDist,jdbcType=VARCHAR},
            address_detail = #{addressDetail,jdbcType=VARCHAR},
            native_place = #{nativePlace,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            education = #{education,jdbcType=VARCHAR},
            job = #{job,jdbcType=VARCHAR},
            marriage = #{marriage,jdbcType=VARCHAR},
            religion = #{religion,jdbcType=VARCHAR}
        where user_id = #{userId,jdbcType=INTEGER}
    </update>

    <update id="updateForScale" parameterType="map">
        update psycloud_user_info
        set real_name = #{realName},sex = #{sex},birth = #{birth} WHERE user_id= #{userId}
    </update>

    <select id="isLoginNameExist" parameterType="String" resultType="Integer">
        select COUNT(*)
        from psycloud_user pu
                 inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where pui.is_valid = 1 AND pu.login_name = #{loginName}
    </select>

    <update id="updateAvatar" parameterType="map">
        update psycloud_user_info
        set head_img = #{headPic}
        where user_id = #{userId}
    </update>

    <update id="checkUser" parameterType="Integer">
        update psycloud_user_info
        set is_checked = 1
        where user_id = #{userId}
    </update>

    <update id="updateCounselorInfo" parameterType="cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity">
        update
            psycloud_counselor_info
        set
            is_recommend = #{isRecommend},
            be_good_at =#{beGoodAt},
            counselor_info = #{counselorIntro}
        where
            user_id= #{userId}
    </update>

    <select id="getCounselorInfoByUserId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity">
        select
            user_id,
            is_recommend,
            be_good_at,
            counselor_info
        from
            psycloud_counselor_info
        where user_id = #{userId}
    </select>

    <update id="bindMobile" parameterType="map">
        update psycloud_user_info
        set
            mobile = #{mobile},
            is_mobile_bind = 1
        where user_id = #{userId}
    </update>

    <select id="checkMobile" parameterType="String" resultType="Integer">
        select
            COUNT(*)
        from psycloud_user_info pui
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where pui.mobile = #{mobile}
            and pui.is_mobile_bind = 1
            and pui.is_valid = 1
    </select>
    <!--生成时返回的用户集合-->
    <select id="getUserListForArchive" parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto" resultType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_user_info.sex,
            psycloud_user_info.birth,
            psycloud_user_info.nation,
            psycloud_user_info.native_place,
            psycloud_user_info.idcard_no,
            psycloud_user_info.address_province,
            psycloud_user_info.address_city,
            psycloud_user_info.address_dist,
            psycloud_user_info.address_detail,
            psycloud_user_info.mobile,
            f_GetStructFullName(psycloud_user_info.struct_id) as structFullName
        from psycloud_user
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            left join psycloud_structs on psycloud_structs.id = psycloud_user_info.struct_id
            inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
            inner join psycloud_role on psycloud_role.id = psycloud_user_role.role_id
        where  psycloud_user_info.is_valid = 1
            <if test="loginName != null and loginName != ''">and psycloud_user.login_name =#{loginName}</if>
            <if test="realName != null and realName != ''">and psycloud_user_info.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="roleId != null and roleId != ''">and psycloud_role.id = #{roleId}</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and psycloud_user_info.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <!--根据用户ID查询用户手机号码-->
    <select id="getMobileByUserId" parameterType="Integer" resultType="String">
        select mobile from psycloud_user_info where is_valid = 1 and is_checked = 1 and user_id = #{userId}
    </select>

    <select id="isSyncUserExists" parameterType="String" resultType="java.lang.Integer">
        select count(*)
        from psycloud_user_info pui
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where pu.login_name = #{mobile} and pui.is_valid = 1
    </select>

    <update id="updateUserInfoBySyncUserId" parameterType="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        update psycloud_user_info
        set
            struct_id = #{structId,jdbcType=INTEGER},
            real_name = #{realName,jdbcType=VARCHAR},
            sex = #{sex,jdbcType=CHAR},
            mobile = #{mobile,jdbcType=VARCHAR}
        where sync_user_id = #{syncUserId,jdbcType=INTEGER}
    </update>

    <update id="deleteBySyncUserId" parameterType="Integer">
        update psycloud_user_info set is_valid = 0 where sync_user_id = #{syncUserId,jdbcType=INTEGER}
    </update>

    <!-- 修改用户的最后修改密码时间 -->
    <update id="updateLastChangePwdDate" parameterType="Integer">
        update psycloud_user_info set last_password_change_date = now() where user_id = #{userId}
    </update>

    <!-- 根据用户名查询用户id -->
    <select id="getUserIdByLoginName" parameterType="String">
        select pu.user_id
        from psycloud_user pu
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where pui.is_valid=1 and pu.login_name = #{loginName}
    </select>

    <!-- 添加微信用户 -->
    <insert id="addWechatUser" parameterType="cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity">
        insert into psycloud_user_wechat(
            user_id,
            open_id,
            union_id,
            nick_name
        )
        values(
            #{userId,jdbcType=INTEGER},
            #{openid,jdbcType=VARCHAR},
            #{unionid,jdbcType=VARCHAR},
            #{nickname,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据用户id查询微信用户信息 -->
    <select id="getWechatUserInfo" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity">
        select
            user_id,
            open_id,
            union_id,
            nick_name
        from psycloud_user_wechat
        where user_id = #{userId}
    </select>

    <!-- 根据unionid查询微信用户信息 -->
    <select id="getUserInfoByWechatUnionId" parameterType="String" resultMap="signInResultMap">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_user_info.head_img,
            psycloud_user_info.struct_id,
            psycloud_user_info.mobile,
            psycloud_user_info.is_mobile_bind,
            psycloud_user_role.role_id
        from psycloud_user
                 inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
                 inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
                 inner join psycloud_user_wechat on psycloud_user_wechat.user_id = psycloud_user.user_id
        where psycloud_user_info.is_valid = 1
          and psycloud_user_info.is_checked = 1
          and psycloud_user_wechat.union_id = #{unionid}
    </select>

    <!-- 根据用户名和真实姓名查询用户id -->
    <select id="getUserInfoByLoginNameAndRealName" parameterType="map" resultMap="signInResultMap">
        select
            psycloud_user.user_id,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_user_info.head_img,
            psycloud_user_info.struct_id,
            psycloud_user_info.mobile,
            psycloud_user_info.is_mobile_bind,
            psycloud_user_role.role_id
        from psycloud_user
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            inner join psycloud_user_role on psycloud_user_role.user_id = psycloud_user_info.user_id
        where psycloud_user_info.is_valid=1
          and psycloud_user_info.is_checked =1
          and psycloud_user.login_name = #{loginName}
          and psycloud_user_info.real_name = #{realName}
    </select>
</mapper>