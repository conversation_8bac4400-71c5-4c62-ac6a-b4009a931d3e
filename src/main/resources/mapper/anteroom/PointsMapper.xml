<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.anteroom.PointsDao">
    <!--用户积分充值和消费-->
    <update id="updatePoints" parameterType="map">
        update psycloud_user_info set points = points + (#{point,jdbcType=INTEGER}) where user_id = #{userId,jdbcType=INTEGER}
    </update>

    <!--保存积分明细-->
    <insert id="insertPoints" parameterType="cn.psycloud.psyplatform.entity.anteroom.PointsDetailEntity">
        insert into psycloud_points_detail(
            user_id,
            point,
            charge_type,
            source_id,
            operator,
            operate_date
        )
        values(
            #{userId,jdbcType=INTEGER},
            #{point,jdbcType=INTEGER},
            #{chargeType,jdbcType=TINYINT},
            #{sourceId,jdbcType=TINYINT},
            #{operator,jdbcType=INTEGER},
            #{operateDate,jdbcType=TIMESTAMP}
            )
    </insert>

    <!--获取积分明细-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.anteroom.PointsDetailDto" resultType="cn.psycloud.psyplatform.dto.anteroom.PointsDetailDto">
        select
            ppd.id,
            ppd.user_id,
            pu.login_name,
            pui.real_name,
            ppd.point,
            ppd.charge_type,
            ppd.source_id,
            pps.source_name,
            ppd.operate_date,
            f_GetStructFullName(pui.struct_id) as structFullName
        from psycloud_points_detail ppd
            inner join psycloud_user pu on pu.user_id = ppd.user_id
            inner join psycloud_user_info pui on pui.user_id = ppd.user_id
            inner join psycloud_points_source pps on pps.id = ppd.source_id
        where pui.is_valid = 1
            <if test="loginName != null and loginName != ''">and pu.login_name  like CONCAT('%',#{loginName},'%')</if>
            <if test="realName != null and realName != ''">and pui.real_name  like CONCAT('%',#{realName},'%')</if>
        order by  ppd.operate_date desc
    </select>
</mapper>