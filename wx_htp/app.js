// app.js
App({
  globalData: {
    userInfo: null,
    baseUrl: 'https://your-domain.com', // 请替换为您的服务器域名
    apiPrefix: '/aiAgent/measuring',
    conversationId: '',
    primaryColor: '#6f42c1',
    primaryHover: '#5a359a'
  },

  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res.code)
      }
    })
  },

  onShow() {
    // 小程序启动，或从后台进入前台显示时触发
  },

  onHide() {
    // 小程序从前台进入后台时触发
  },

  onError(msg) {
    console.log('小程序发生错误：', msg)
  },

  // 全局方法：显示提示信息
  showToast(title, icon = 'none', duration = 2000) {
    wx.showToast({
      title: title,
      icon: icon,
      duration: duration
    })
  },

  // 全局方法：显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 全局方法：隐藏加载中
  hideLoading() {
    wx.hideLoading()
  },

  // 全局方法：显示确认对话框
  showModal(title, content) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: title,
        content: content,
        success: (res) => {
          if (res.confirm) {
            resolve(true)
          } else {
            resolve(false)
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
})
