/* pages/index/index.wxss */

.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
}

/* 头部横幅 */
.banner {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(111, 66, 193, 0.8) 0%, rgba(90, 53, 154, 0.9) 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.banner-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.3);
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  text-shadow: 0 1rpx 4rpx rgba(0,0,0,0.3);
}

/* 介绍卡片 */
.intro-card {
  margin-top: 32rpx;
  background: var(--primary-gradient);
  color: white;
  border: none;
}

.intro-text {
  font-size: 32rpx;
  line-height: 1.8;
  text-align: justify;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

/* 卡片图标 */
.card-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--primary-light);
}

.materials-icon {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.instructions-icon {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.process-icon {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
}

/* 提示文本 */
.tip-text {
  font-size: 30rpx;
  color: var(--text-muted);
  margin-bottom: 32rpx;
  line-height: 1.6;
}

/* 高亮框 */
.highlight-box {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-left: 8rpx solid #ffc107;
  padding: 24rpx;
  border-radius: var(--border-radius);
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #856404;
  line-height: 1.6;
}

/* 列表项 */
.item-list {
  margin-top: 24rpx;
}

.item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: rgba(111, 66, 193, 0.05);
  border-radius: var(--border-radius);
  border-left: 6rpx solid var(--primary-color);
}

.item-number {
  width: 48rpx;
  height: 48rpx;
  background: var(--primary-gradient);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color);
}

/* 引用列表 */
.quote-list {
  margin-top: 24rpx;
}

.quote-item {
  margin-bottom: 20rpx;
}

.quote-text {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-left: 6rpx solid #2196f3;
  padding: 24rpx;
  border-radius: var(--border-radius);
  font-size: 28rpx;
  line-height: 1.6;
  color: #1565c0;
  position: relative;
}

.quote-text::before {
  content: '"';
  font-size: 48rpx;
  color: #2196f3;
  position: absolute;
  left: 16rpx;
  top: 8rpx;
  opacity: 0.3;
}

/* 操作区域 */
.action-section {
  margin: 64rpx 0;
  text-align: center;
}

.start-btn {
  background: var(--primary-gradient);
  box-shadow: 0 8rpx 32rpx rgba(111, 66, 193, 0.3);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.start-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(111, 66, 193, 0.4);
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 36rpx;
  font-weight: 600;
}

/* 免责声明 */
.disclaimer {
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius);
  border: 2rpx dashed #ffc107;
}

.disclaimer-text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .banner {
    height: 320rpx;
  }
  
  .banner-title {
    font-size: 40rpx;
  }
  
  .banner-subtitle {
    font-size: 24rpx;
  }
  
  .card-icon {
    width: 64rpx;
    height: 64rpx;
    font-size: 40rpx;
    margin-right: 16rpx;
  }
  
  .card-title {
    font-size: 32rpx;
  }
  
  .intro-text {
    font-size: 28rpx;
  }
  
  .item-number {
    width: 40rpx;
    height: 40rpx;
    font-size: 20rpx;
    margin-right: 16rpx;
  }
  
  .item-text {
    font-size: 26rpx;
  }
  
  .quote-text {
    font-size: 26rpx;
    padding: 20rpx;
  }
  
  .btn-icon {
    font-size: 32rpx;
  }
  
  .btn-text {
    font-size: 32rpx;
  }
}
