// pages/index/index.js
const app = getApp()

Page({
  data: {
    
  },

  onLoad(options) {
    // 页面加载时的逻辑
    console.log('房树人测试介绍页面加载')
  },

  onShow() {
    // 页面显示时的逻辑
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }
  },

  onReady() {
    // 页面初次渲染完成
  },

  onHide() {
    // 页面隐藏
  },

  onUnload() {
    // 页面卸载
  },

  onPullDownRefresh() {
    // 下拉刷新
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    // 上拉触底
  },

  onShareAppMessage() {
    // 分享功能
    return {
      title: '房树人绘画心理测试',
      desc: '通过绘画了解内心世界，专业心理投射测试',
      path: '/pages/index/index'
    }
  },

  onShareTimeline() {
    // 分享到朋友圈
    return {
      title: '房树人绘画心理测试 - 通过绘画了解内心世界',
      query: '',
      imageUrl: '/images/share-image.jpg'
    }
  },

  // 前往测试页面
  goToTest() {
    wx.switchTab({
      url: '/pages/test/test'
    })
  }
})
