// pages/result/result.js
const app = getApp()
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    originalImage: '',
    analysisResult: '',
    formattedResult: '',
    fileId: '',
    generatingImage: false,
    showImageModal: false,
    generatedImagePath: ''
  },

  onLoad(options) {
    console.log('结果页面加载，参数：', options)
    
    // 从页面参数获取数据
    if (options.originalImage) {
      this.setData({
        originalImage: decodeURIComponent(options.originalImage)
      })
    }
    
    if (options.analysisResult) {
      const result = decodeURIComponent(options.analysisResult)
      this.setData({
        analysisResult: result,
        formattedResult: this.formatAnalysisContent(result)
      })
    }
    
    if (options.fileId) {
      this.setData({
        fileId: options.fileId
      })
    }
  },

  onShow() {
    // 页面显示时的逻辑
  },

  onReady() {
    // 页面初次渲染完成
  },

  onHide() {
    // 页面隐藏
  },

  onUnload() {
    // 页面卸载
  },

  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  onShareAppMessage() {
    return {
      title: '我的房树人绘画心理测试结果',
      desc: '通过绘画了解内心世界，专业心理投射测试',
      path: '/pages/index/index'
    }
  },

  onShareTimeline() {
    return {
      title: '房树人绘画心理测试 - 我的测试结果',
      query: '',
      imageUrl: '/images/share-result.jpg'
    }
  },

  // 格式化分析内容
  formatAnalysisContent(content) {
    // 移除markdown格式字符
    let cleanContent = content
      .replace(/#+\s*/g, '')  // 移除 # 标题标记
      .replace(/\*+/g, '')    // 移除 * 强调标记
      .replace(/^\s*[\*\-\+]\s+/gm, '') // 移除列表标记
      .trim()

    // 按段落分割内容
    let paragraphs = cleanContent.split(/\n\s*\n/)

    // 格式化每个段落
    let formattedParagraphs = paragraphs.map(paragraph => {
      paragraph = paragraph.trim()
      if (paragraph) {
        return `<p style="line-height: 2.0; margin-bottom: 1.2em; text-indent: 2em;">${paragraph.replace(/\n/g, '<br>')}</p>`
      }
      return ''
    }).filter(p => p)

    return formattedParagraphs.join('')
  },

  // 分享结果
  shareResult() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 保存为图片
  saveAsImage() {
    if (!this.data.fileId || !this.data.analysisResult) {
      app.showToast('数据不完整，无法生成图片', 'none')
      return
    }

    const that = this
    that.setData({
      generatingImage: true
    })

    api.generateHtpImage(this.data.fileId, this.data.analysisResult)
      .then(imagePath => {
        console.log('图片生成成功：', imagePath)
        that.setData({
          generatingImage: false,
          generatedImagePath: imagePath,
          showImageModal: true
        })
      })
      .catch(err => {
        console.error('图片生成失败：', err)
        that.setData({
          generatingImage: false
        })
      })
  },

  // 保存图片到相册
  saveImageToAlbum() {
    if (!this.data.generatedImagePath) {
      app.showToast('没有可保存的图片', 'none')
      return
    }

    util.saveImageToPhotosAlbum(this.data.generatedImagePath)
      .then(() => {
        console.log('图片保存成功')
      })
      .catch(err => {
        console.error('图片保存失败：', err)
      })
  },

  // 隐藏图片模态框
  hideImageModal() {
    this.setData({
      showImageModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 重新测试
  newTest() {
    wx.switchTab({
      url: '/pages/test/test'
    })
  }
})
