/* pages/result/result.wxss */

.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 结果头部 */
.result-header {
  background: var(--primary-gradient);
  color: white;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
}

.header-content {
  display: flex;
  align-items: center;
}

.result-icon {
  font-size: 80rpx;
  margin-right: 32rpx;
}

.header-text {
  flex: 1;
}

.result-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.result-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
}

.title-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
  color: var(--primary-color);
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
}

/* 图片区域 */
.image-section {
  margin-bottom: 32rpx;
}

.image-container {
  background: white;
  border-radius: var(--border-radius);
  padding: 32rpx;
  margin: 0 32rpx;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.original-image {
  width: 100%;
  max-height: 500rpx;
  border-radius: var(--border-radius);
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

/* 分析区域 */
.analysis-section {
  margin-bottom: 32rpx;
}

.analysis-container {
  background: white;
  border-radius: var(--border-radius);
  margin: 0 32rpx;
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.analysis-content {
  padding: 32rpx;
  line-height: 1.8;
  font-size: 30rpx;
  color: var(--text-color);
}

.analysis-footer {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--border-color);
  background: var(--light-color);
}

.ai-disclaimer {
  display: flex;
  align-items: center;
}

.disclaimer-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: var(--warning-color);
}

.disclaimer-text {
  font-size: 24rpx;
  color: var(--text-muted);
  line-height: 1.5;
}

/* 操作区域 */
.action-section {
  margin: 32rpx;
}

.action-buttons {
  /* 按钮容器样式 */
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}

.loading-spinner-sm {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255,255,255,0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 建议区域 */
.suggestions-section {
  margin-bottom: 32rpx;
}

.suggestions-container {
  margin: 0 32rpx;
}

.suggestion-item {
  background: white;
  border-radius: var(--border-radius);
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(111, 66, 193, 0.1) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.suggestion-desc {
  font-size: 28rpx;
  color: var(--text-muted);
  line-height: 1.6;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
}

.modal-close {
  font-size: 48rpx;
  color: var(--text-muted);
  cursor: pointer;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--light-color);
  color: var(--text-color);
}

.modal-body {
  padding: 32rpx;
  text-align: center;
}

.modal-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: var(--border-radius);
}

.modal-tip {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: var(--text-muted);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .result-header {
    padding: 40rpx 24rpx;
  }
  
  .result-icon {
    font-size: 64rpx;
    margin-right: 24rpx;
  }
  
  .result-title {
    font-size: 40rpx;
  }
  
  .result-subtitle {
    font-size: 24rpx;
  }
  
  .section-title {
    padding: 0 24rpx;
  }
  
  .title-icon {
    font-size: 32rpx;
  }
  
  .title-text {
    font-size: 32rpx;
  }
  
  .image-container,
  .analysis-container,
  .suggestions-container {
    margin: 0 24rpx;
  }
  
  .analysis-content {
    padding: 24rpx;
    font-size: 28rpx;
  }
  
  .suggestion-item {
    padding: 24rpx;
  }
  
  .suggestion-icon {
    font-size: 40rpx;
    width: 64rpx;
    height: 64rpx;
    margin-right: 16rpx;
  }
  
  .suggestion-title {
    font-size: 28rpx;
  }
  
  .suggestion-desc {
    font-size: 26rpx;
  }
  
  .action-section {
    margin: 24rpx;
  }
}
