<!--pages/test/test.wxml-->
<view class="page-container">
  <view class="container">
    
    <!-- 上传区域 -->
    <view class="upload-section" wx:if="{{!imageUploaded}}">
      <view class="upload-area {{dragOver ? 'drag-over' : ''}}" bindtap="chooseImage">
        <view class="upload-icon">📤</view>
        <view class="upload-text">点击上传您的房树人绘画作品</view>
        <view class="upload-hint">支持 JPG、PNG、GIF、BMP 格式，文件大小不超过10MB</view>
      </view>
    </view>

    <!-- 图片预览区域 -->
    <view class="preview-section" wx:if="{{imageUploaded}}">
      <view class="preview-container">
        <image class="preview-image" src="{{imagePath}}" mode="aspectFit"></image>
        <view class="preview-actions">
          <button class="btn btn-outline-primary btn-sm" bindtap="reUploadImage">
            重新上传
          </button>
        </view>
      </view>
    </view>

    <!-- 分析按钮区域 -->
    <view class="analyze-section" wx:if="{{imageUploaded && !analyzing && !analysisResult}}">
      <button class="btn btn-primary btn-lg btn-block analyze-btn" bindtap="analyzeImage" disabled="{{!fileId}}">
        <view class="btn-content">
          <view class="btn-icon">🔍</view>
          <view class="btn-text">开始分析</view>
        </view>
      </button>
    </view>

    <!-- 分析中状态 -->
    <view class="analyzing-section" wx:if="{{analyzing}}">
      <view class="analyzing-container">
        <view class="loading-spinner"></view>
        <view class="analyzing-text">AI正在分析您的绘画作品...</view>
        <view class="analyzing-tip">这可能需要几秒钟时间，请耐心等待</view>
      </view>
    </view>

    <!-- 分析结果区域 -->
    <view class="result-section" wx:if="{{analysisResult}}">
      <view class="result-container">
        <view class="result-header">
          <view class="result-icon">📊</view>
          <view class="result-title">分析结果</view>
        </view>
        <view class="result-content">
          <rich-text nodes="{{formattedResult}}"></rich-text>
        </view>
        <view class="result-footer">
          <view class="ai-disclaimer">
            该内容由AI生成，不代表平台观点。
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="btn btn-outline-primary btn-block" bindtap="reAnalyze">
          <view class="btn-icon">🔄</view>
          <view class="btn-text">重新分析</view>
        </button>
        <button class="btn btn-primary btn-block mt-2" bindtap="saveAsImage" disabled="{{generatingImage}}">
          <view class="btn-content">
            <view class="btn-icon" wx:if="{{!generatingImage}}">💾</view>
            <view class="loading-spinner-sm" wx:if="{{generatingImage}}"></view>
            <view class="btn-text">{{generatingImage ? '生成中...' : '保存为图片'}}</view>
          </view>
        </button>
      </view>
    </view>

    <!-- 使用提示 -->
    <view class="tips-section" wx:if="{{!imageUploaded}}">
      <view class="tips-container">
        <view class="tips-header">
          <view class="tips-icon">💡</view>
          <view class="tips-title">使用提示</view>
        </view>
        <view class="tips-list">
          <view class="tip-item">
            <view class="tip-number">1</view>
            <view class="tip-text">请确保绘画作品清晰可见，光线充足</view>
          </view>
          <view class="tip-item">
            <view class="tip-number">2</view>
            <view class="tip-text">建议上传包含房子、树木、人物的完整作品</view>
          </view>
          <view class="tip-item">
            <view class="tip-number">3</view>
            <view class="tip-text">图片格式支持JPG、PNG等常见格式</view>
          </view>
        </view>
      </view>
    </view>

  </view>
</view>

<!-- 图片预览模态框 -->
<view class="modal {{showImageModal ? 'show' : ''}}" bindtap="hideImageModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">分析结果图片</view>
      <view class="modal-close" bindtap="hideImageModal">×</view>
    </view>
    <view class="modal-body">
      <image class="modal-image" src="{{generatedImagePath}}" mode="aspectFit" wx:if="{{generatedImagePath}}"></image>
      <view class="modal-tip">长按图片保存到相册</view>
    </view>
  </view>
</view>
