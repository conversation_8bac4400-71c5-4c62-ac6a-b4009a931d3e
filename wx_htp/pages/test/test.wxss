/* pages/test/test.wxss */

.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 上传区域 */
.upload-section {
  margin-top: 32rpx;
}

.upload-area {
  border: 4rpx dashed var(--primary-color);
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  background: rgba(111, 66, 193, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(111, 66, 193, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.upload-area:hover::before {
  opacity: 1;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.upload-area.drag-over {
  border-color: var(--success-color);
  background: rgba(40, 167, 69, 0.1);
  transform: scale(1.02);
}

.upload-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  color: var(--primary-color);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20rpx); }
  60% { transform: translateY(-10rpx); }
}

.upload-text {
  font-size: 32rpx;
  color: var(--text-color);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.upload-hint {
  font-size: 24rpx;
  color: var(--text-muted);
  line-height: 1.5;
}

/* 预览区域 */
.preview-section {
  margin-top: 32rpx;
}

.preview-container {
  background: white;
  border-radius: var(--border-radius);
  padding: 32rpx;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.preview-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: var(--border-radius);
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.preview-actions {
  margin-top: 32rpx;
}

/* 分析按钮区域 */
.analyze-section {
  margin-top: 32rpx;
}

.analyze-btn {
  background: var(--primary-gradient);
  box-shadow: 0 8rpx 32rpx rgba(111, 66, 193, 0.3);
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.analyze-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(111, 66, 193, 0.4);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 36rpx;
  font-weight: 600;
}

/* 分析中状态 */
.analyzing-section {
  margin-top: 32rpx;
}

.analyzing-container {
  background: white;
  border-radius: var(--border-radius);
  padding: 64rpx 32rpx;
  text-align: center;
  box-shadow: var(--box-shadow);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid var(--gray-light);
  border-top: 8rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

.loading-spinner-sm {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid var(--gray-light);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analyzing-text {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 16rpx;
}

.analyzing-tip {
  font-size: 28rpx;
  color: var(--text-muted);
}

/* 结果区域 */
.result-section {
  margin-top: 32rpx;
}

.result-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.result-header {
  background: var(--primary-gradient);
  color: white;
  padding: 32rpx;
  display: flex;
  align-items: center;
}

.result-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
}

.result-content {
  padding: 32rpx;
  line-height: 1.8;
  font-size: 30rpx;
  color: var(--text-color);
}

.result-footer {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--border-color);
  background: var(--light-color);
}

.ai-disclaimer {
  font-size: 24rpx;
  color: var(--text-muted);
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  margin-top: 32rpx;
}

/* 使用提示 */
.tips-section {
  margin-top: 48rpx;
}

.tips-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.tips-header {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  padding: 32rpx;
  display: flex;
  align-items: center;
}

.tips-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.tips-title {
  font-size: 36rpx;
  font-weight: 600;
}

.tips-list {
  padding: 32rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-number {
  width: 48rpx;
  height: 48rpx;
  background: var(--info-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color);
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
}

.modal-close {
  font-size: 48rpx;
  color: var(--text-muted);
  cursor: pointer;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--light-color);
  color: var(--text-color);
}

.modal-body {
  padding: 32rpx;
  text-align: center;
}

.modal-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: var(--border-radius);
}

.modal-tip {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: var(--text-muted);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .upload-area {
    padding: 60rpx 32rpx;
  }
  
  .upload-icon {
    font-size: 80rpx;
  }
  
  .upload-text {
    font-size: 28rpx;
  }
  
  .upload-hint {
    font-size: 22rpx;
  }
  
  .preview-image {
    max-height: 500rpx;
  }
  
  .btn-icon {
    font-size: 32rpx;
  }
  
  .btn-text {
    font-size: 32rpx;
  }
  
  .analyzing-text {
    font-size: 32rpx;
  }
  
  .result-content {
    font-size: 28rpx;
    padding: 24rpx;
  }
}
