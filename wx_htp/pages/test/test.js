// pages/test/test.js
const app = getApp()
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    imageUploaded: false,
    imagePath: '',
    fileId: '',
    analyzing: false,
    analysisResult: '',
    formattedResult: '',
    generatingImage: false,
    showImageModal: false,
    generatedImagePath: '',
    dragOver: false
  },

  onLoad(options) {
    console.log('房树人测试页面加载')
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  onReady() {
    // 页面初次渲染完成
  },

  onHide() {
    // 页面隐藏
  },

  onUnload() {
    // 页面卸载
  },

  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  onShareAppMessage() {
    return {
      title: '房树人绘画心理测试',
      desc: '通过绘画了解内心世界，专业心理投射测试',
      path: '/pages/test/test'
    }
  },

  // 选择图片
  chooseImage() {
    const that = this
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        const fileSize = res.tempFiles[0].size
        
        // 检查文件大小
        if (fileSize > 10 * 1024 * 1024) {
          app.showToast('图片文件大小不能超过10MB', 'none')
          return
        }

        // 检查文件类型
        if (!util.isImageFile(tempFilePath)) {
          app.showToast('请选择图片文件', 'none')
          return
        }

        that.setData({
          imagePath: tempFilePath,
          imageUploaded: true,
          analyzing: false,
          analysisResult: '',
          formattedResult: '',
          fileId: ''
        })

        // 上传图片
        that.uploadImage(tempFilePath)
      },
      fail: (err) => {
        console.error('选择图片失败：', err)
        if (err.errMsg !== 'chooseMedia:fail cancel') {
          app.showToast('选择图片失败', 'none')
        }
      }
    })
  },

  // 上传图片到服务器
  uploadImage(filePath) {
    const that = this
    
    api.uploadHtpImage(filePath)
      .then(res => {
        console.log('图片上传成功：', res)
        that.setData({
          fileId: res.data.id
        })
        app.showToast('图片上传成功', 'success')
      })
      .catch(err => {
        console.error('图片上传失败：', err)
        that.setData({
          imageUploaded: false,
          imagePath: '',
          fileId: ''
        })
      })
  },

  // 重新上传图片
  reUploadImage() {
    this.setData({
      imageUploaded: false,
      imagePath: '',
      fileId: '',
      analyzing: false,
      analysisResult: '',
      formattedResult: ''
    })
  },

  // 分析图片
  analyzeImage() {
    if (!this.data.fileId) {
      app.showToast('请先上传图片', 'none')
      return
    }

    const that = this
    that.setData({
      analyzing: true,
      analysisResult: '',
      formattedResult: ''
    })

    api.getHtpResult(this.data.fileId, app.globalData.conversationId)
      .then(res => {
        console.log('分析结果：', res)
        
        // 更新会话ID
        if (res.data && res.data.length > 0 && res.data[0].conversation_id) {
          app.globalData.conversationId = res.data[0].conversation_id
        }

        // 过滤只取type=answer的数据
        const answerData = res.data.filter(message => message.type === 'answer')
        
        // 拼接分析结果
        let resultContent = ''
        if (answerData.length > 0) {
          answerData.forEach(message => {
            if (message.role === 'assistant' && message.content) {
              resultContent += message.content + '\n'
            }
          })
        }

        if (resultContent.trim()) {
          const formattedContent = that.formatAnalysisContent(resultContent.trim())
          that.setData({
            analyzing: false,
            analysisResult: resultContent.trim(),
            formattedResult: formattedContent
          })

          // 分析完成后跳转到结果页面
          setTimeout(() => {
            wx.navigateTo({
              url: `/pages/result/result?originalImage=${encodeURIComponent(that.data.imagePath)}&analysisResult=${encodeURIComponent(resultContent.trim())}&fileId=${that.data.fileId}`
            })
          }, 1000)
        } else {
          that.setData({
            analyzing: false
          })
          app.showToast('未获取到分析结果，请重试', 'none')
        }
      })
      .catch(err => {
        console.error('分析失败：', err)
        that.setData({
          analyzing: false
        })
      })
  },

  // 格式化分析内容
  formatAnalysisContent(content) {
    // 移除markdown格式字符
    let cleanContent = content
      .replace(/#+\s*/g, '')  // 移除 # 标题标记
      .replace(/\*+/g, '')    // 移除 * 强调标记
      .replace(/^\s*[\*\-\+]\s+/gm, '') // 移除列表标记
      .trim()

    // 按段落分割内容
    let paragraphs = cleanContent.split(/\n\s*\n/)

    // 格式化每个段落
    let formattedParagraphs = paragraphs.map(paragraph => {
      paragraph = paragraph.trim()
      if (paragraph) {
        return `<p style="line-height: 2.0; margin-bottom: 1.2em; text-indent: 2em;">${paragraph.replace(/\n/g, '<br>')}</p>`
      }
      return ''
    }).filter(p => p)

    return formattedParagraphs.join('')
  },

  // 重新分析
  reAnalyze() {
    this.setData({
      analysisResult: '',
      formattedResult: ''
    })
    this.analyzeImage()
  },

  // 保存为图片
  saveAsImage() {
    if (!this.data.fileId || !this.data.analysisResult) {
      app.showToast('请先完成分析', 'none')
      return
    }

    const that = this
    that.setData({
      generatingImage: true
    })

    api.generateHtpImage(this.data.fileId, this.data.analysisResult)
      .then(imagePath => {
        console.log('图片生成成功：', imagePath)
        that.setData({
          generatingImage: false,
          generatedImagePath: imagePath,
          showImageModal: true
        })
      })
      .catch(err => {
        console.error('图片生成失败：', err)
        that.setData({
          generatingImage: false
        })
      })
  },

  // 隐藏图片模态框
  hideImageModal() {
    this.setData({
      showImageModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
})
