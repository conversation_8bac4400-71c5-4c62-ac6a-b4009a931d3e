// utils/api.js - API接口封装
const app = getApp()

// 基础请求方法
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      method = 'GET',
      data = {},
      header = {},
      showLoading = true,
      loadingText = '加载中...'
    } = options

    if (showLoading) {
      app.showLoading(loadingText)
    }

    wx.request({
      url: app.globalData.baseUrl + url,
      method: method,
      data: data,
      header: {
        'Content-Type': 'application/json',
        ...header
      },
      success: (res) => {
        if (showLoading) {
          app.hideLoading()
        }

        if (res.statusCode === 200) {
          if (res.data.resultCode === 200) {
            resolve(res.data)
          } else {
            app.showToast(res.data.resultMsg || '请求失败', 'none')
            reject(res.data)
          }
        } else {
          app.showToast('网络请求失败', 'none')
          reject(res)
        }
      },
      fail: (err) => {
        if (showLoading) {
          app.hideLoading()
        }
        app.showToast('网络连接失败', 'none')
        reject(err)
      }
    })
  })
}

// 上传文件方法
function uploadFile(filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      name = 'file',
      formData = {},
      showLoading = true,
      loadingText = '上传中...'
    } = options

    if (showLoading) {
      app.showLoading(loadingText)
    }

    wx.uploadFile({
      url: app.globalData.baseUrl + app.globalData.apiPrefix + '/upload_htp_image',
      filePath: filePath,
      name: name,
      formData: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      },
      success: (res) => {
        if (showLoading) {
          app.hideLoading()
        }

        try {
          const data = JSON.parse(res.data)
          if (data.resultCode === 200) {
            resolve(data)
          } else {
            app.showToast(data.resultMsg || '上传失败', 'none')
            reject(data)
          }
        } catch (e) {
          app.showToast('上传响应解析失败', 'none')
          reject(e)
        }
      },
      fail: (err) => {
        if (showLoading) {
          app.hideLoading()
        }
        app.showToast('上传失败', 'none')
        reject(err)
      }
    })
  })
}

// API接口定义
const api = {
  // 上传HTP图片
  uploadHtpImage(filePath) {
    return uploadFile(filePath, {
      name: 'file',
      loadingText: '上传图片中...'
    })
  },

  // 获取HTP分析结果
  getHtpResult(fileId, conversationId = '') {
    return request(app.globalData.apiPrefix + '/get_htp_result', {
      method: 'POST',
      data: {
        fileId: fileId,
        conversationId: conversationId
      },
      loadingText: '分析中...'
    })
  },

  // 生成HTP分析结果图片
  generateHtpImage(fileId, analysisText) {
    return new Promise((resolve, reject) => {
      app.showLoading('生成图片中...')
      
      wx.request({
        url: app.globalData.baseUrl + app.globalData.apiPrefix + '/generate_htp_image',
        method: 'POST',
        data: {
          fileId: fileId,
          analysisText: analysisText
        },
        responseType: 'arraybuffer',
        success: (res) => {
          app.hideLoading()
          if (res.statusCode === 200) {
            // 将arraybuffer转换为base64
            const base64 = wx.arrayBufferToBase64(res.data)
            const tempFilePath = `${wx.env.USER_DATA_PATH}/htp_result_${Date.now()}.png`
            
            // 写入临时文件
            wx.getFileSystemManager().writeFile({
              filePath: tempFilePath,
              data: base64,
              encoding: 'base64',
              success: () => {
                resolve(tempFilePath)
              },
              fail: (err) => {
                app.showToast('图片生成失败', 'none')
                reject(err)
              }
            })
          } else {
            app.showToast('图片生成失败', 'none')
            reject(res)
          }
        },
        fail: (err) => {
          app.hideLoading()
          app.showToast('图片生成失败', 'none')
          reject(err)
        }
      })
    })
  }
}

module.exports = api
