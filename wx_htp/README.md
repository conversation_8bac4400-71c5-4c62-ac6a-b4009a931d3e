# 房树人绘画心理测试 - 微信小程序

## 项目简介

这是一个基于微信小程序开发的房树人绘画心理测试应用，通过AI智能分析用户上传的绘画作品，提供专业的心理分析报告。

## 功能特性

### 核心功能
- **测试介绍页面**：详细介绍房树人测试的原理、准备材料、指导语和绘画过程
- **图片上传功能**：支持拍照或从相册选择绘画作品，自动压缩和格式验证
- **AI智能分析**：调用后端AI接口，对绘画作品进行专业心理分析
- **结果展示**：美观的分析结果展示，支持富文本格式
- **图片生成**：将分析结果合成为图片，方便保存和分享
- **结果保存**：支持将分析结果保存到手机相册

### 设计特色
- **简约大气**：采用紫色主色调(#6f42c1)，界面简洁美观
- **用户体验优良**：流畅的交互动画，清晰的操作引导
- **响应式设计**：适配不同尺寸的手机屏幕
- **无障碍支持**：良好的可访问性设计

## 技术架构

### 前端技术
- **微信小程序原生开发**：使用原生WXML、WXSS、JavaScript
- **模块化设计**：工具函数、API接口分离，代码结构清晰
- **组件化开发**：可复用的UI组件和样式

### 后端接口
- **文件上传接口**：`/aiAgent/measuring/upload_htp_image`
- **AI分析接口**：`/aiAgent/measuring/get_htp_result`
- **图片生成接口**：`/aiAgent/measuring/generate_htp_image`

## 项目结构

```
wx_htp/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── pages/                # 页面目录
│   ├── index/            # 介绍页面
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   ├── test/             # 测试页面
│   │   ├── test.js
│   │   ├── test.json
│   │   ├── test.wxml
│   │   └── test.wxss
│   └── result/           # 结果页面
│       ├── result.js
│       ├── result.json
│       ├── result.wxml
│       └── result.wxss
├── utils/                # 工具函数
│   ├── api.js           # API接口封装
│   └── util.js          # 通用工具函数
└── images/              # 图片资源
    ├── home.png         # 首页图标
    ├── home-active.png  # 首页激活图标
    ├── test.png         # 测试图标
    ├── test-active.png  # 测试激活图标
    └── htp-banner.jpg   # 横幅图片
```

## 安装部署

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 配置服务器域名白名单

### 2. 配置修改
在 `app.js` 中修改服务器配置：
```javascript
globalData: {
  baseUrl: 'https://your-domain.com', // 替换为您的服务器域名
  // ...其他配置
}
```

### 3. 图片资源
需要准备以下图片资源并放入 `images/` 目录：
- `home.png` (64x64px) - 首页图标
- `home-active.png` (64x64px) - 首页激活图标  
- `test.png` (64x64px) - 测试图标
- `test-active.png` (64x64px) - 测试激活图标
- `htp-banner.jpg` (750x400px) - 横幅图片

### 4. 服务器配置
确保后端服务器已部署并配置以下接口：
- 文件上传接口支持multipart/form-data格式
- AI分析接口返回标准JSON格式
- 图片生成接口返回二进制图片数据

## 使用说明

### 用户操作流程
1. **查看介绍**：在首页了解房树人测试的相关信息
2. **上传作品**：点击"前往测试"，上传绘画作品照片
3. **AI分析**：等待AI智能分析绘画内容
4. **查看结果**：阅读详细的心理分析报告
5. **保存分享**：将结果保存为图片或分享给朋友

### 注意事项
- 支持的图片格式：JPG、PNG、GIF、BMP、WEBP
- 图片大小限制：不超过10MB
- 建议上传清晰、光线充足的绘画作品照片
- 分析结果仅供参考，不能替代专业心理咨询

## 开发说明

### 样式规范
- 主色调：#6f42c1 (紫色)
- 辅助色：#5a359a (深紫色)
- 成功色：#28a745 (绿色)
- 警告色：#ffc107 (黄色)
- 危险色：#dc3545 (红色)

### 代码规范
- 使用ES6+语法
- 采用模块化开发
- 注释清晰完整
- 错误处理完善

### 性能优化
- 图片懒加载
- 接口请求缓存
- 页面预加载
- 资源压缩

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础的房树人测试功能
- 支持图片上传和AI分析
- 完成结果展示和保存功能

## 技术支持

如有技术问题或建议，请联系开发团队。

## 许可证

本项目仅供学习和研究使用。
