/* app.wxss - 全局样式 */

/* 全局变量 */
page {
  --primary-color: #6f42c1;
  --primary-hover: #5a359a;
  --primary-light: #dacef1;
  --primary-gradient: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --gray: #6c757d;
  --gray-light: #e9ecef;
  --text-color: #333333;
  --text-muted: #8D95A0;
  --border-color: #e9ecef;
  --border-radius: 12rpx;
  --box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.08);
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--light-color);
}

/* 容器样式 */
.container {
  padding: 0 32rpx;
}

.page-container {
  min-height: 100vh;
  background-color: var(--light-color);
}

/* 卡片样式 */
.card {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 32rpx;
  overflow: hidden;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
}

.card-body {
  padding: 32rpx;
}

.card-footer {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--border-color);
  background-color: var(--light-color);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--border-radius);
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  min-height: 88rpx;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-hover);
  transform: translateY(-2rpx);
}

.btn-outline-primary {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: var(--white);
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-lg {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
  min-height: 96rpx;
}

.btn-sm {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  min-height: 72rpx;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 文本样式 */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-info { color: var(--info-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--white); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 间距样式 */
.mt-1 { margin-top: 16rpx; }
.mt-2 { margin-top: 32rpx; }
.mt-3 { margin-top: 48rpx; }
.mt-4 { margin-top: 64rpx; }

.mb-1 { margin-bottom: 16rpx; }
.mb-2 { margin-bottom: 32rpx; }
.mb-3 { margin-bottom: 48rpx; }
.mb-4 { margin-bottom: 64rpx; }

.ml-1 { margin-left: 16rpx; }
.ml-2 { margin-left: 32rpx; }
.mr-1 { margin-right: 16rpx; }
.mr-2 { margin-right: 32rpx; }

.pt-1 { padding-top: 16rpx; }
.pt-2 { padding-top: 32rpx; }
.pt-3 { padding-top: 48rpx; }
.pt-4 { padding-top: 64rpx; }

.pb-1 { padding-bottom: 16rpx; }
.pb-2 { padding-bottom: 32rpx; }
.pb-3 { padding-bottom: 48rpx; }
.pb-4 { padding-bottom: 64rpx; }

.pl-1 { padding-left: 16rpx; }
.pl-2 { padding-left: 32rpx; }
.pr-1 { padding-right: 16rpx; }
.pr-2 { padding-right: 32rpx; }

/* 图标样式 */
.icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.icon-lg {
  font-size: 64rpx;
}

.icon-sm {
  font-size: 32rpx;
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--gray-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 隐藏类 */
.hidden {
  display: none !important;
}

/* 响应式 */
@media (max-width: 750rpx) {
  .container {
    padding: 0 24rpx;
  }
  
  .card-body {
    padding: 24rpx;
  }
  
  .btn {
    font-size: 30rpx;
    padding: 20rpx 40rpx;
  }
}
