# 部署配置说明

## 1. 服务器配置

### 1.1 修改服务器地址
在 `app.js` 文件中修改 `baseUrl`：
```javascript
globalData: {
  baseUrl: 'https://your-domain.com', // 替换为您的实际服务器域名
  // ...
}
```

### 1.2 微信小程序后台配置
1. 登录微信公众平台 (mp.weixin.qq.com)
2. 进入"开发" -> "开发管理" -> "开发设置"
3. 在"服务器域名"中添加：
   - request合法域名：`https://your-domain.com`
   - uploadFile合法域名：`https://your-domain.com`
   - downloadFile合法域名：`https://your-domain.com`

## 2. 后端接口要求

### 2.1 接口列表
确保以下接口已正确部署：

#### 文件上传接口
- **URL**: `/aiAgent/measuring/upload_htp_image`
- **方法**: POST
- **格式**: multipart/form-data
- **参数**: file (图片文件)
- **返回**: 
```json
{
  "resultCode": 200,
  "resultMsg": "上传成功",
  "data": {
    "id": "文件ID"
  }
}
```

#### AI分析接口
- **URL**: `/aiAgent/measuring/get_htp_result`
- **方法**: POST
- **格式**: application/json
- **参数**: 
```json
{
  "fileId": "文件ID",
  "conversationId": "会话ID（可选）"
}
```
- **返回**:
```json
{
  "resultCode": 200,
  "data": [
    {
      "conversation_id": "会话ID",
      "type": "answer",
      "role": "assistant",
      "content": "分析结果内容"
    }
  ]
}
```

#### 图片生成接口
- **URL**: `/aiAgent/measuring/generate_htp_image`
- **方法**: POST
- **格式**: application/json
- **参数**:
```json
{
  "fileId": "文件ID",
  "analysisText": "分析结果文本"
}
```
- **返回**: 二进制图片数据 (image/png)

### 2.2 CORS配置
确保服务器支持跨域请求，配置适当的CORS头：
```
Access-Control-Allow-Origin: https://servicewechat.com
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type
```

## 3. 微信开发者工具配置

### 3.1 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择 `wx_htp` 文件夹
4. 填写AppID（在project.config.json中修改）

### 3.2 开发设置
1. 在"详情"页面中：
   - 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"（仅开发时）
   - 选择基础库版本 2.19.4 或更高

### 3.3 编译设置
1. 点击"编译"按钮进行编译
2. 检查控制台是否有错误信息
3. 在模拟器中测试各项功能

## 4. 图片资源准备

### 4.1 必需图片
将以下图片放入 `images/` 目录：
- `home.png` - 首页图标
- `home-active.png` - 首页激活图标
- `test.png` - 测试图标  
- `test-active.png` - 测试激活图标
- `htp-banner.jpg` - 横幅图片

### 4.2 图片规格
- 图标：64x64px，PNG格式，透明背景
- 横幅：750x400px，JPG格式

## 5. 测试流程

### 5.1 功能测试
1. **介绍页面**：检查内容显示和样式
2. **图片上传**：测试拍照和相册选择
3. **AI分析**：验证分析结果显示
4. **结果保存**：测试图片生成和保存
5. **页面跳转**：检查各页面间的导航

### 5.2 兼容性测试
1. 不同手机型号测试
2. 不同网络环境测试
3. 不同图片格式和大小测试

## 6. 发布上线

### 6.1 代码审查
1. 检查所有TODO和FIXME
2. 移除调试代码和console.log
3. 确认所有配置正确

### 6.2 版本发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 等待审核通过后发布

### 6.3 线上监控
1. 监控接口调用情况
2. 收集用户反馈
3. 分析使用数据

## 7. 常见问题

### 7.1 网络请求失败
- 检查域名是否在白名单中
- 确认HTTPS证书有效
- 验证接口返回格式

### 7.2 图片上传失败
- 检查文件大小限制
- 确认图片格式支持
- 验证服务器存储空间

### 7.3 AI分析超时
- 增加请求超时时间
- 优化后端处理逻辑
- 添加重试机制

## 8. 性能优化

### 8.1 图片优化
- 压缩图片资源
- 使用WebP格式（如支持）
- 实现图片懒加载

### 8.2 代码优化
- 减少不必要的setData调用
- 优化页面渲染性能
- 使用分包加载（如需要）

### 8.3 网络优化
- 实现请求缓存
- 添加离线提示
- 优化接口响应时间
